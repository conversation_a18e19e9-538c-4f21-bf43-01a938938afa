package com.bell.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 微信支付配置属性
 *
 */
@Data
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayProperties {
    /**
     * 设置微信公众号或者小程序等的appid
     */
    private String appId;

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信APIv3密钥
     */
    private String apiV3Key;

    /**
     * 微信支付商户密钥
     */
    private String mchKey;

    /**
     * 证书相对路径
     */
    private String privateKeyPath;

    /**
     * 证书相对路径
     */
    private String privateCertPath;

    /**
     * 下单回调地址
     */
    private String returnUrl;

    /**
     * 退款回调地址
     */
    private String refundUrl;

    /**
     * apiclient_cert.p12文件的绝对路径，或者如果放在项目中，请以classpath:开头指定
     */
    private String keyPath;

}