package com.bell.common.core.domain.entity;

import com.bell.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 天气
 * 
 * <AUTHOR>
 */
@Data
public class AppWeather
{

    /** 状态码 */
    private Integer status;

    /** 版本 */
    private String version;

    /** 时间信息 */
    private AppWeatherDate date;

    /** 天气信息 */
    private AppWeatherResult result;

    /** 地区信息 */
    private AppWeatherLocation appWeatherLocation;

    /** 潮汐信息 */
    private AppTide appTide;

    /** 潮汐规模 */
    private String appTideString;

    /** 台风信息 */
    private AppTyphoonDetail appTyphoon;

    /** 日期 */
    private String time;

    /** 星期 */
    private String week;

    public AppWeather(Integer status, String version, AppWeatherDate date, AppWeatherResult result, AppWeatherLocation appWeatherLocation) {
        this.status = status;
        this.version = version;
        this.date = date;
        this.result = result;
        this.appWeatherLocation = appWeatherLocation;
    }

    public AppWeather() {
    }
}
