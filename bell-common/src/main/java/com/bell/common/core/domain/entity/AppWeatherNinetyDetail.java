package com.bell.common.core.domain.entity;

import lombok.Data;

import java.util.List;

/**
 * 天气
 * <AUTHOR>
 */
@Data
public class AppWeatherNinetyDetail
{

    /** 当前日期 */
    private String fc_time;

    /** 日期 */
    private String time;

    /** 星期 */
    private String week;

    /** 白天风向 */
    private String wd_day;

    /** 晚上风向 */
    private String wd_night;

    /** 白天风速 */
    private String ws_day;

    /** 晚上风速 */
    private String ws_night;

    /** 白天天气 */
    private String wp_day;

    /** 夜晚天气 */
    private String wp_night;

    /** 白天天气编码 */
    private String wp_day_code;

    /** 夜晚天气编码 */
    private String wp_night_code;

    /** 白天温度 */
    private String tem_max;

    /** 夜晚温度 */
    private String tem_min;

    /** 昼夜温差 0 不大 1 大 */
    private Integer temChange;
}
