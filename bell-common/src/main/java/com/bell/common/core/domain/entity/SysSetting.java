package com.bell.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bell.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 系统设置对象 sys_setting
 * 
 * <AUTHOR>
 * @date 2023-08-19
 */
@Data
@TableName("sys_setting")
public class SysSetting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long settingId;

    /** 预购结单日期 */
    @Excel(name = "预购结单日期")
    private Integer settlementDate;

    /** 1删除 */
    @Excel(name = "1删除")
    private Integer isDel;
    private Integer orderDdl;
    private Integer depositRatio;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("settingId", getSettingId())
            .append("settlementDate", getSettlementDate())
            .append("isDel", getIsDel())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
