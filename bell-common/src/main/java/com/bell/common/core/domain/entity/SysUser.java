package com.bell.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bell.common.annotation.Excel;
import com.bell.common.annotation.Excel.ColumnType;
import com.bell.common.annotation.Excel.Type;
import com.bell.common.annotation.Excels;
import com.bell.common.core.domain.BaseEntity;
import com.bell.common.xss.Xss;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
public class SysUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @Excel(name = "用户序号", cellType = ColumnType.NUMERIC, prompt = "用户编号")
    private Long userId;

    /** 部门ID */
    @Excel(name = "部门编号", type = Type.IMPORT)
    private Long deptId;

    /** 用户账号 */
    @Excel(name = "登录名称")
    private String userName;

    private String userType;

    /** 用户昵称 */
    @Excel(name = "用户名称")
    private String nickName;

    /** 用户邮箱 */
    @Excel(name = "用户邮箱")
    private String email;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phonenumber;

    /** 用户性别 */
    @Excel(name = "用户性别", readConverterExp = "0=男,1=女,2=未知")
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    @Excel(name = "最后登录IP", type = Type.EXPORT)
    private String loginIp;

    /** 最后登录时间 */
    @Excel(name = "最后登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Type.EXPORT)
    private Date loginDate;

    @TableField(exist = false)
    private Integer auditState;

    /** 部门对象 */
    @Excels({
        @Excel(name = "部门名称", targetAttr = "deptName", type = Type.EXPORT),
        @Excel(name = "部门负责人", targetAttr = "leader", type = Type.EXPORT)
    })
    private SysDept dept;

    /** 角色对象 */
    private List<SysRole> roles;

    /** 角色组 */
    private Long[] roleIds;

    /** 岗位组 */
    private Long[] postIds;

    /** 角色ID */
    private Long roleId;


    private String microAppOpenId;
    private String gzhOpenId;
    private Integer levelId;
    private Integer lostScore;
    private Integer maxScore;
    private Integer fishScore;
    private Integer maxFishScore;
    private Integer medal;
    private Integer fishFrequency;
    private Integer fishExperience;
    private Integer isNameAuth;
    private Integer isCaptain;
    private Integer bindShipId;
    private String certNo;
    private Integer captionScore;
    private Integer captionExperience;
    private Integer captionThreeMonthOutQuantity;
    private Integer captionTotalOutQuantity;
    private Integer certType;
    private String realName;
    /**
     * 船长佣金比例
     */
    private BigDecimal commissionRatio;
    /**
     * 账户类型：0自主注册（小程序），1后台注册
     */
    private Integer accountType;
    /**
     * 船只绑定状态：0未绑定，1已绑定，2绑定申请中，3绑定驳回，4更换申请中，5更换申请驳回，6解绑申请中，7解绑申请驳回
     */
    private Integer bindShipState;
    /**
     * 驻港人员所属村镇
     */
    private Long areaId;

    /**
     * 系统层面,此船长能否接单：0信息不全不可接单，1可以接单
     */
    private Integer canOrder;

    /**
     * 船长是否开启接单功能：0不接单，1可以接单
     */
    private Integer orderFlag;

    /**
     * 车企ID
     */
    private Long companyId;

    /**
     * 联系方式
     */
    private String contactInfo;

    @TableField(exist = false)
    private Integer age;

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
    private String captainIntroduceImage;

    public String getCaptainIntroduceImage() {
        return captainIntroduceImage;
    }

    public void setCaptainIntroduceImage(String captainIntroduceImage) {
        this.captainIntroduceImage = captainIntroduceImage;
    }

    public Integer getOrderFlag() {
        return orderFlag;
    }

    public void setOrderFlag(Integer orderFlag) {
        this.orderFlag = orderFlag;
    }

    public Integer getCanOrder() {
        return canOrder;
    }

    public void setCanOrder(Integer canOrder) {
        this.canOrder = canOrder;
    }

    public Integer getBindShipState() {
        return bindShipState;
    }

    public void setBindShipState(Integer bindShipState) {
        this.bindShipState = bindShipState;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public BigDecimal getCommissionRatio() {
        return commissionRatio;
    }

    public void setCommissionRatio(BigDecimal commissionRatio) {
        this.commissionRatio = commissionRatio;
    }

    public Integer getCertType() {
        return certType;
    }

    public void setCertType(Integer certType) {
        this.certType = certType;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMicroAppOpenId() {
        return microAppOpenId;
    }

    public void setMicroAppOpenId(String microAppOpenId) {
        this.microAppOpenId = microAppOpenId;
    }

    public String getGzhOpenId() {
        return gzhOpenId;
    }

    public void setGzhOpenId(String gzhOpenId) {
        this.gzhOpenId = gzhOpenId;
    }

    public Integer getLevelId() {
        return levelId;
    }

    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    public Integer getLostScore() {
        return lostScore;
    }

    public void setLostScore(Integer lostScore) {
        this.lostScore = lostScore;
    }

    public Integer getMaxScore() {
        return maxScore;
    }

    public void setMaxScore(Integer maxScore) {
        this.maxScore = maxScore;
    }

    public Integer getFishScore() {
        return fishScore;
    }

    public void setFishScore(Integer fishScore) {
        this.fishScore = fishScore;
    }

    public Integer getMedal() {
        return medal;
    }

    public void setMedal(Integer medal) {
        this.medal = medal;
    }

    public Integer getFishFrequency() {
        return fishFrequency;
    }

    public void setFishFrequency(Integer fishFrequency) {
        this.fishFrequency = fishFrequency;
    }

    public Integer getFishExperience() {
        return fishExperience;
    }

    public void setFishExperience(Integer fishExperience) {
        this.fishExperience = fishExperience;
    }

    public Integer getIsNameAuth() {
        return isNameAuth;
    }

    public void setIsNameAuth(Integer isNameAuth) {
        this.isNameAuth = isNameAuth;
    }

    public Integer getIsCaptain() {
        return isCaptain;
    }

    public void setIsCaptain(Integer isCaptain) {
        this.isCaptain = isCaptain;
    }

    public Integer getBindShipId() {
        return bindShipId;
    }

    public void setBindShipId(Integer bindShipId) {
        this.bindShipId = bindShipId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public Integer getCaptionScore() {
        return captionScore;
    }

    public void setCaptionScore(Integer captionScore) {
        this.captionScore = captionScore;
    }

    public Integer getCaptionExperience() {
        return captionExperience;
    }

    public void setCaptionExperience(Integer captionExperience) {
        this.captionExperience = captionExperience;
    }

    public Integer getCaptionThreeMonthOutQuantity() {
        return captionThreeMonthOutQuantity;
    }

    public void setCaptionThreeMonthOutQuantity(Integer captionThreeMonthOutQuantity) {
        this.captionThreeMonthOutQuantity = captionThreeMonthOutQuantity;
    }

    public Integer getCaptionTotalOutQuantity() {
        return captionTotalOutQuantity;
    }

    public void setCaptionTotalOutQuantity(Integer captionTotalOutQuantity) {
        this.captionTotalOutQuantity = captionTotalOutQuantity;
    }

    public SysUser()
    {

    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public SysUser(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public boolean isAdmin()
    {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId)
    {
        return userId != null && 1L == userId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getNickName()
    {
        return nickName;
    }

    public void setNickName(String nickName)
    {
        this.nickName = nickName;
    }

    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName()
    {
        return userName;
    }

    public void setUserName(String userName)
    {
        this.userName = userName;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail()
    {
        return email;
    }

    public void setEmail(String email)
    {
        this.email = email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhonenumber()
    {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber)
    {
        this.phonenumber = phonenumber;
    }

    public String getSex()
    {
        return sex;
    }

    public void setSex(String sex)
    {
        this.sex = sex;
    }

    public String getAvatar()
    {
        return avatar;
    }

    public void setAvatar(String avatar)
    {
        this.avatar = avatar;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getLoginIp()
    {
        return loginIp;
    }

    public void setLoginIp(String loginIp)
    {
        this.loginIp = loginIp;
    }

    public Date getLoginDate()
    {
        return loginDate;
    }

    public void setLoginDate(Date loginDate)
    {
        this.loginDate = loginDate;
    }

    public SysDept getDept()
    {
        return dept;
    }

    public void setDept(SysDept dept)
    {
        this.dept = dept;
    }

    public List<SysRole> getRoles()
    {
        return roles;
    }

    public void setRoles(List<SysRole> roles)
    {
        this.roles = roles;
    }

    public Long[] getRoleIds()
    {
        return roleIds;
    }

    public void setRoleIds(Long[] roleIds)
    {
        this.roleIds = roleIds;
    }

    public Long[] getPostIds()
    {
        return postIds;
    }

    public void setPostIds(Long[] postIds)
    {
        this.postIds = postIds;
    }

    public Long getRoleId()
    {
        return roleId;
    }

    public void setRoleId(Long roleId)
    {
        this.roleId = roleId;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public Integer getMaxFishScore() {
        return maxFishScore;
    }

    public void setMaxFishScore(Integer maxFishScore) {
        this.maxFishScore = maxFishScore;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("deptId", getDeptId())
            .append("userName", getUserName())
            .append("nickName", getNickName())
            .append("email", getEmail())
            .append("phonenumber", getPhonenumber())
            .append("sex", getSex())
            .append("avatar", getAvatar())
            .append("password", getPassword())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("loginIp", getLoginIp())
            .append("loginDate", getLoginDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("dept", getDept())
                .append("commissionRatio", getCommissionRatio())
                .append("account_type", getAccountType())
                .append("bindShipState", getBindShipState())
                .append("can_order", getCanOrder())
                .append("order_flag", getOrderFlag())
                .append("captainIntroduceImage", getCaptainIntroduceImage())
                .append("contact_info", getContactInfo())
            .toString();
    }
}
