package com.bell.common.core.domain.model;

import javax.validation.constraints.NotBlank;

/**
 * 微信用户登录对象
 * 
 * <AUTHOR>
 */
public class AppLoginBody
{
    /**
     * accessToken
     */
    private String accessToken;

    /**
     * openId
     */
    private String openId;

    /**
     * 临时票据
     */
    @NotBlank
    private String code;

    /**
     * 临时票据(手机号)
     */
    @NotBlank
    private String codePhone;

    /**
     * 微信开发平台客户多端同一ID
     */
    private String unionId;

    /**
     * 微信用户手机号
     */
    private String phone;

    /**
     * 分享人id
     */
    private Long shareUserId;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCodePhone() {
        return codePhone;
    }

    public void setCodePhone(String codePhone) {
        this.codePhone = codePhone;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getShareUserId() {
        return shareUserId;
    }

    public void setShareUserId(Long shareUserId) {
        this.shareUserId = shareUserId;
    }
}
