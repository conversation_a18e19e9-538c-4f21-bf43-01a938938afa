package com.bell.framework.config;

import com.bell.framework.tcp.NettyTcpServer;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NettyShutdownHook implements DisposableBean {
    @Autowired
    private NettyTcpServer nettyTcpServer;

    @Override
    public void destroy() {
        nettyTcpServer.shutdown();
        System.out.println("Netty服务已关闭");
    }
}
