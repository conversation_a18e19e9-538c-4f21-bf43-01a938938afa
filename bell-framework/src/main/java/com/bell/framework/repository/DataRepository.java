package com.bell.framework.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;

@Repository
public class DataRepository {
    private static final Duration CACHE_TTL = Duration.ofMinutes(5);

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 原子性检查并存储数据
     * @param key 数据唯一标识
     * @param value 数据值
     * @return true表示是新数据，false表示重复
     */
    public boolean checkAndStore(String key, String value) {
        Boolean result = redisTemplate.opsForValue().setIfAbsent(key, value, CACHE_TTL);
        return Boolean.TRUE.equals(result);
    }
}
