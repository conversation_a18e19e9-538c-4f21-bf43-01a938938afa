package com.bell.framework.service;

import com.bell.framework.websocket.RealtimeWebSocketHandler;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 统一数据推送服务
 * 整合TCP和HTTP数据源，统一推送到前端
 */
@Service
public class UnifiedDataPushService {

    private static final Logger log = LoggerFactory.getLogger(UnifiedDataPushService.class);

    @Autowired
    private RealtimeWebSocketHandler webSocketHandler;
    
    /**
     * 推送TCP设备数据（来自Netty称重设备）
     * 需要传入deviceId来匹配对应的Vue页面
     */
    public void pushTcpDeviceData(String deviceId, String scaleNo, String weight, String rawData) {
        try {
            long timestamp = System.currentTimeMillis();
            String formattedTime = LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            // 构造消息
            JsonObject message = new JsonObject();
            message.addProperty("type", "tcp_device");
            message.addProperty("source", "netty");
            message.addProperty("deviceId", deviceId);
            message.addProperty("scaleNo", scaleNo);
            message.addProperty("weight", weight);
            message.addProperty("time", formattedTime);
            message.addProperty("timestamp", timestamp);
            message.addProperty("rawData", rawData);

            String jsonMsg = message.toString();

            // 推送到对应deviceId的Vue页面
            webSocketHandler.sendToVueByDeviceId(deviceId, jsonMsg);

            log.warn("TCP称重设备数据已推送: deviceId={}, scaleNo={}, weight={}", deviceId, scaleNo, weight);

        } catch (Exception e) {
            log.error("TCP设备数据推送失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 推送自定义数据到指定deviceId的Vue页面
     */
    public void pushCustomDataToDevice(String deviceId, String type, String source, Object data) {
        try {
            long timestamp = System.currentTimeMillis();
            String formattedTime = LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            // 构造消息
            JsonObject message = new JsonObject();
            message.addProperty("type", type);
            message.addProperty("source", source);
            message.addProperty("deviceId", deviceId);
            message.addProperty("time", formattedTime);
            message.addProperty("timestamp", timestamp);
            message.addProperty("data", data.toString());

            String jsonMsg = message.toString();

            // 推送到指定deviceId的Vue页面
            webSocketHandler.sendToVueByDeviceId(deviceId, jsonMsg);

            log.warn("自定义数据已推送: deviceId={}, type={}, source={}", deviceId, type, source);

        } catch (Exception e) {
            log.error("自定义数据推送失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 直接推送数据到指定设备的小程序（保留原有逻辑）
     */
    public void pushToMiniProgram(String deviceId, Object data) {
        try {
            long timestamp = System.currentTimeMillis();
            String formattedTime = LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("HH:mm:ss"));

            // 构造消息
            JsonObject message = new JsonObject();
            message.addProperty("type", "device_submit");
            message.addProperty("source", "device_vue");
            message.addProperty("deviceId", deviceId);
            message.addProperty("time", formattedTime);
            message.addProperty("timestamp", timestamp);

            // 正确处理数据对象，保持JSON结构
            if (data instanceof String) {
                message.addProperty("data", (String) data);
            } else {
                // 使用Gson将对象转换为JSON字符串
                Gson gson = new Gson();
                String dataJson = gson.toJson(data);
                message.addProperty("data", dataJson);
            }

            String jsonMsg = message.toString();

            // 推送到指定设备的小程序
            webSocketHandler.sendToDevice(deviceId, jsonMsg);

            log.warn("数据已推送到小程序: deviceId={}, dataType={}", deviceId, data.getClass().getSimpleName());

        } catch (Exception e) {
            log.error("推送到小程序失败: {}", e.getMessage(), e);
        }
    }


}
