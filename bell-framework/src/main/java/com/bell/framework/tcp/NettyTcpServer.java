package com.bell.framework.tcp;

import com.bell.framework.tcp.handler.NettyChannelInitializer;
import com.bell.framework.tcp.handler.NettyServerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.epoll.EpollChannelOption;
import io.netty.channel.epoll.EpollEventLoopGroup;
import io.netty.channel.epoll.EpollServerSocketChannel;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class NettyTcpServer {
    private Logger log = LoggerFactory.getLogger(NettyServerHandler.class);

    @Value("${netty.port:8889}")
    private int port;

    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;

    @Autowired
    private NettyChannelInitializer channelInitializer;

    @PostConstruct
    public void start() {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
//        bossGroup = new EpollEventLoopGroup(1);  linux用
//        workerGroup = new EpollEventLoopGroup();
        new Thread(() -> {
            try {
                ServerBootstrap bootstrap = new ServerBootstrap()
                        .group(bossGroup, workerGroup)
                        .channel(NioServerSocketChannel.class) // NioServerSocketChannel适合多平台 .channel(EpollServerSocketChannel.class)
                        .childHandler(channelInitializer)
                        .option(ChannelOption.SO_BACKLOG, 100)
                        .childOption(ChannelOption.SO_KEEPALIVE, true)
                        .childOption(EpollChannelOption.TCP_KEEPIDLE, 30)    // 30秒无活动开始探测
                        .childOption(EpollChannelOption.TCP_KEEPINTVL, 5)    // 每5秒探测一次
                        .childOption(EpollChannelOption.TCP_KEEPCNT, 3);     // 探测3次后断开;
                ChannelFuture future = bootstrap.bind(port).sync();
                log.error("Netty服务已启动，端口：" + port);
                System.out.println("Netty服务已启动，端口：" + port);
                future.channel().closeFuture().sync();
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                log.error("异常", e);
                Thread.currentThread().interrupt();
            } finally {
                shutdown();
            }
        }, "Netty-Server-Thread").start();
    }

    public void shutdown() {
        log.error("Netty服务已关闭");
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
    }

}
