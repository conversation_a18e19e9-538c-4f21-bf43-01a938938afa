package com.bell.framework.tcp.handler;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NettyChannelInitializer extends ChannelInitializer<SocketChannel> {

    @Autowired
    private NettyServerHandler nettyServerHandler;

    @Override
    protected void initChannel(SocketChannel ch) {
        // 按换行符拆分数据（解决粘包/拆包问题）
//        ch.pipeline().addLast(new LineBasedFrameDecoder(1024));
//        ch.pipeline().addLast(new StringDecoder(CharsetUtil.US_ASCII)); // ASCII解码
//        ch.pipeline().addLast(new StringEncoder(CharsetUtil.US_ASCII)); // ASCII编码
        ch.pipeline().addLast(new StringDecoder())
                .addLast(new StringEncoder())
                .addLast(nettyServerHandler);
    }
}
