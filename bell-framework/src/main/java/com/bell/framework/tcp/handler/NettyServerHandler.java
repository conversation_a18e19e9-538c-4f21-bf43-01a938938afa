package com.bell.framework.tcp.handler;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bell.basic.domain.BellScale;
import com.bell.basic.mapper.BellScaleMapper;
import com.bell.framework.repository.DataRepository;
import com.bell.framework.service.UnifiedDataPushService;
import com.bell.framework.tcp.DeviceManager;
import com.bell.framework.websocket.RealtimeWebSocketHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetSocketAddress;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import io.netty.channel.ChannelHandler.Sharable;

@Sharable
@Component
public class NettyServerHandler extends SimpleChannelInboundHandler<String> {
    private Logger log = LoggerFactory.getLogger(NettyServerHandler.class);

    @Autowired
    private RealtimeWebSocketHandler wsHandler;

    @Autowired
    private UnifiedDataPushService dataService;

    @Autowired
    private BellScaleMapper bellScaleMapper;

    // 设备ID属性键
    public static final AttributeKey<String> DEVICE_ID = AttributeKey.valueOf("deviceId");
    // 设备是否已注册的标记
    public static final AttributeKey<Boolean> REGISTERED = AttributeKey.valueOf("registered");


    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String rawData) {
        try {
            // 1. 基础数据校验
            if (rawData == null || rawData.length() < 4) {
                log.warn("无效数据格式: {}", rawData);
                return;
            }

            log.warn("设备数据推送数据: {}", rawData);

            // 2. 解析数据（格式示例：01204.20）
            String scaleNo = rawData.substring(0, 3);  // 前3位为称编号
            String weightPart = rawData.substring(3, rawData.length() - 1);
            String formattedWeight = formatWeight(weightPart);

            // 通过scaleNo查找对应的scale_id作为deviceId
            LambdaQueryWrapper<BellScale> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BellScale::getScaleNo, scaleNo);
            BellScale bellScale = bellScaleMapper.selectOne(queryWrapper);

            String deviceId = null;
            if (bellScale != null && bellScale.getScaleId() != null) {
                deviceId = bellScale.getScaleId().toString(); // 使用scale_id作为deviceId
            } else {
                log.warn("未找到scaleNo对应的设备信息: {}", scaleNo);
                return;
            }

            // 更新设备状态
            if (ctx.channel().attr(REGISTERED).get()) {
                DeviceManager.updateStatus(scaleNo, ctx.channel());
            } else {
                ctx.channel().attr(DEVICE_ID).set(scaleNo);
                ctx.channel().attr(REGISTERED).set(true);
                DeviceManager.updateStatus(scaleNo, ctx.channel());
                LambdaUpdateWrapper<BellScale> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BellScale::getOnlineState, 1)
                        .eq(BellScale::getScaleNo, scaleNo);
                bellScaleMapper.update(null, updateWrapper);
                log.warn("称重设备注册成功: scaleNo={}, deviceId={}", scaleNo, deviceId);
            }

            // 使用统一推送服务推送数据到对应deviceId的Vue页面
            dataService.pushTcpDeviceData(deviceId, scaleNo, formattedWeight, rawData);
            log.warn("称重数据已推送: deviceId={}, scaleNo={}, weight={}", deviceId, scaleNo, formattedWeight);

        } catch (Exception e) {
            log.warn("数据处理异常: {}", e.getMessage());
            ctx.writeAndFlush("ERROR: " + e.getMessage());
        }
    }

    /**
     * 格式化重量值（保证两位小数）
     */
    private String formatWeight(String rawWeight) {
        try {
            BigDecimal weight = new BigDecimal(rawWeight)
                    .setScale(2, RoundingMode.HALF_UP);
            return weight.toString();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的重量值: " + rawWeight);
        }
    }
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("连接异常: {}", cause.getMessage());
        ctx.close();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("客户端连接: {}", ctx.channel().remoteAddress());
        // 初始化设备注册状态为false
        ctx.channel().attr(REGISTERED).set(false); // 初始化未注册状态
        ctx.fireChannelActive();

    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        String deviceKey = ctx.channel().attr(DEVICE_ID).get();
        if (deviceKey != null) {
            DeviceManager.markOffline(deviceKey);
            log.info("设备离线: {}", deviceKey);
            LambdaUpdateWrapper<BellScale> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BellScale::getOnlineState, 0)
                    .set(BellScale::getUpdateTime, new Date())
                    .eq(BellScale::getScaleNo, deviceKey);
            bellScaleMapper.update(null, updateWrapper);
        }
        log.warn("客户端断开连接: {}", ctx.channel().remoteAddress());
        ctx.fireChannelInactive();
    }
}
