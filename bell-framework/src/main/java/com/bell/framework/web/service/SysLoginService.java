package com.bell.framework.web.service;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bell.basic.domain.UserShareLog;
import com.bell.basic.mapper.UserShareLogMapper;
import com.bell.common.constant.AppConstants;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.domain.model.AppLoginBody;
import com.bell.common.enums.UserScoreRuleEnums;
import com.bell.common.exception.user.*;
import com.bell.common.utils.http.HttpUtils;
import com.bell.common.utils.spring.SpringUtils;
import com.bell.competition.domain.UserScoreLog;
import com.bell.competition.domain.UserScoreRule;
import com.bell.competition.mapper.UserScoreLogMapper;
import com.bell.competition.mapper.UserScoreRuleMapper;
import com.bell.framework.config.WechatConfig;
import com.bell.framework.manager.AsyncManager;
import com.bell.framework.web.domain.server.Sys;
import com.bell.system.domain.DTO.AppSysUserDto;
import com.bell.system.domain.DTO.SysUserDto;
import com.bell.system.domain.vo.*;
import com.bell.system.mapper.SysUserMapper;
import com.bell.system.util.WechatTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import com.bell.common.constant.CacheConstants;
import com.bell.common.constant.Constants;
import com.bell.common.constant.UserConstants;
import com.bell.common.core.domain.entity.SysUser;
import com.bell.common.core.domain.model.LoginUser;
import com.bell.common.core.redis.RedisCache;
import com.bell.common.exception.ServiceException;
import com.bell.common.utils.DateUtils;
import com.bell.common.utils.MessageUtils;
import com.bell.common.utils.StringUtils;
import com.bell.common.utils.ip.IpUtils;
import com.bell.framework.manager.factory.AsyncFactory;
import com.bell.framework.security.context.AuthenticationContextHolder;
import com.bell.system.service.ISysConfigService;
import com.bell.system.service.ISysUserService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;
import org.apache.hc.client5.http.fluent.Request;
import org.apache.http.entity.ContentType;
/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SysLoginService
{
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;
    
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private UserShareLogMapper userShareLogMapper;

    @Autowired
    private UserScoreLogMapper userScoreLogMapper;

    @Autowired
    private UserScoreRuleMapper userScoreRuleMapper;

    @Autowired
    private WechatTemplateUtil wechatTemplateUtil;

    @Value("${sfeport.appId}")
    private String appId;

    @Value("${sfeport.appSecret}")
    private String appSecret;

    @Value("${wechat.mpAppId}")
    private String mpAppId;

    @Value("${wechat.mpSecret}")
    private String mpSecret;

    /**
     * 登录验证
     * 
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public String login(String username, String password, String code, String uuid, String userType, Integer checkCode)
    {
        /**
         *  注册用户
         *  注册用户username 可能是手机号
         */
        if (ObjectUtil.isEmpty(sysUserMapper.selectBackendUserByUserName(username))) {
            throw new ServiceException("没有后台权限");
        }

        if (checkCode==null) {
            // 验证码校验
            validateCaptcha(username, code, uuid);
            loginPreCheck(username, password);
        }
        // 登录前置校验
//        validateUserType(username, userType);
        // 用户验证
        Authentication authentication;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 微信登录
     * @param appLoginBody 登录参数
     * @return 结果
     */
    public AjaxResult AppLogin(AppLoginBody appLoginBody){
        //根据临时票据code获取微信用户token
        String replaceUrl = AppConstants.APP_OPEN_ID_URL.replace("{0}", appId).replace("{1}", appSecret).replace("{2}", appLoginBody.getCode());
        JSONObject jsonObject = JSONObject.parseObject(HttpUtils.sendGet(replaceUrl));
        String openid = jsonObject.getString("openid");
//        String nickname = jsonObject.getString("nickname");
        //String session_key = jsonObject.getString("session_key");
        //判断当前用户是否存在，存在更新，不存在创建
        String accToken = getAccessToken();
        if (null == accToken){
            return AjaxResult.error("微信错误");
        }
        String phoneNumber = getPhoneNumber(appLoginBody.getCodePhone(), openid, accToken);
        if (null == phoneNumber){
            return AjaxResult.error("微信错误");
        }
        SysUser info = sysUserMapper.selectUserByPhonenumber(phoneNumber);
        SysUser user = new SysUser();
        if (null == info){
            UserScoreRule rule = userScoreRuleMapper.selectById(UserScoreRuleEnums.REGISTER.getCode());
            user.setUserName(phoneNumber);
            user.setNickName("微信用户");
            user.setUserType("01");
            user.setPhonenumber(phoneNumber);
            user.setLoginIp(IpUtils.getIpAddr());
            user.setLoginDate(DateUtils.getNowDate());
            user.setCreateTime(DateUtils.getNowDate());
            user.setUpdateTime(DateUtils.getNowDate());
            user.setMicroAppOpenId(openid);
            user.setAccountType(0);
            if(null != rule && null != rule.getScore() && 0 != rule.getScore()){
                user.setFishScore(Math.toIntExact(rule.getScore()));
                user.setMaxFishScore(Math.toIntExact(rule.getScore()));
            }else{
                user.setFishScore(0);
            }
            sysUserMapper.insertUser(user);
            //判断注册送鱼粮规则是否有效
            if(null != rule && null != rule.getScore() && 0 != rule.getScore()){
                //插入鱼粮获取记录表
                UserScoreLog scoreLog = new UserScoreLog();
                scoreLog.setUserId(user.getUserId());
                scoreLog.setScoreFrom(UserScoreRuleEnums.REGISTER.getInfo());
                scoreLog.setRuleId(rule.getRuleId());
                scoreLog.setScore(rule.getScore());
                scoreLog.setState(1);
                scoreLog.setDataId(user.getUserId());
                scoreLog.setScoreType(1);
                scoreLog.setCreateTime(DateUtils.getNowDate());
                scoreLog.setUpdateTime(DateUtils.getNowDate());
                scoreLog.setIsDel(0);
                userScoreLogMapper.insert(scoreLog);
            }
            //判断是否存在邀请人
            if (null != appLoginBody.getShareUserId()){
                //插入分享记录表
                UserShareLog log = new UserShareLog();
                log.setShareUserId(appLoginBody.getShareUserId());
                log.setRegistUserId(user.getUserId());
                userShareLogMapper.insert(log);
            }
        }else{
            user = info;
            user.setMicroAppOpenId(openid);
            user.setLoginIp(IpUtils.getIpAddr());
            user.setLoginDate(DateUtils.getNowDate());
            user.setUpdateTime(DateUtils.getNowDate());
            sysUserMapper.updateUser(user);
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        //获取token并返回
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(user.getUserId());
        loginUser.setUser(user);
        String token = tokenService.createToken(loginUser);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        ajax.put("userInfo", user);
        return ajax;
    }

    /**
     * 获取微信用户access_token
     * @return access_token
     */
    private String getAccessToken(){
        String accToken = redisCache.getCacheObject(CacheConstants.ACCESS_TOKEN_KEY);
        if (StringUtils.isBlank(accToken)) {
            String url = AppConstants.APP_ACCESS_TOKEN_URL;
            String replaceUrl = url.replace("{0}", appId).replace("{1}", appSecret);
            JSONObject jsonObject = JSONObject.parseObject(HttpUtils.sendGet(replaceUrl));
            Integer errCode = jsonObject.getInteger("errcode");
            if (ObjectUtils.isEmpty(errCode)) {
                String accessToken = jsonObject.getString(AppConstants.ACCESS_TOKEN);
                redisCache.setCacheObject(CacheConstants.ACCESS_TOKEN_KEY, accessToken, AppConstants.ACCESS_TOKEN_TIME, TimeUnit.SECONDS);
                return accessToken;
            } else {
                return null;
            }
        } else {
            return accToken;
        }
    }

    /**
     * 获取手机号
     * @param codePhone 手机号临时票据
     * @return 手机号
     */
    private String getPhoneNumber(String codePhone, String openid, String accessToken) {
        String phoneUrl = AppConstants.APP_PHONE_URL.replace("{0}", accessToken);
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("code", codePhone);
        map.put("openid", openid);
        String res = HttpUtils.sendPost(phoneUrl, JSONObject.toJSONString(map));
        JSONObject jsonObject = JSONObject.parseObject(res);
        Integer errCode = jsonObject.getInteger("errcode");
        if (errCode == 0) {
            JSONObject phoneInfo = jsonObject.getJSONObject("phone_info");
            return phoneInfo.getString("phoneNumber");
        } else {
            redisCache.deleteObject(CacheConstants.ACCESS_TOKEN_KEY);
            getAccessToken();
            return null;
        }
    }


    private void validateUserType(String username, String userType) {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (sysUser!=null && !sysUser.getUserType().equals(userType)) {
            throw new UserTypeNotMatchException();
        }
    }

    /**
     * 校验验证码
     * 
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled)
        {
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisCache.getCacheObject(verifyKey);
            redisCache.deleteObject(verifyKey);
            if (captcha == null)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new CaptchaExpireException();
            }
            if (!code.equalsIgnoreCase(captcha))
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("not.null")));
            throw new UserNotExistsException();
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
            throw new UserPasswordNotMatchException();
        }
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId)
    {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
    }

    public WechatResponse getWechatOpenIdByCode(String code) {
        WechatConfig wechatConfig = SpringUtils.getBean(WechatConfig.class);
        try {
            String url = wechatConfig.getGetOpenIdUrl().replace("CODE", code);
            log.error("url===" + url);
            //如果金融code没有，注册
            String returnStr = Request.get(url)
                    .execute()
                    .returnContent()
                    .asString(StandardCharsets.UTF_8);
            log.error("getOpenId result: " + returnStr);
            if (StringUtils.isNotEmpty(returnStr)) {
                WechatResponse wechatResponse = JSONObject.parseObject(returnStr, WechatResponse.class);
                return wechatResponse;
            } else {
                throw new ServiceException("获取用户信息失败");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public AjaxResult bindWxUser(SysUserDto sysUserDto) {
        if(StringUtils.isNotEmpty(sysUserDto.getUserType())){
            SysUser checkUser = sysUserMapper.selectUserByUserName(sysUserDto.getUserName());
            if(null != checkUser && !sysUserDto.getUserType().equals(checkUser.getUserType())){
                if(!(sysUserDto.getUserType().equals("04") && checkUser.getUserType().equals("01"))){
                    throw new ServiceException("账户不存在");
                }
            }
        }

        // 用户验证
        Authentication authentication;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(sysUserDto.getUserName(), sysUserDto.getPassword());
            AuthenticationContextHolder.setContext(authenticationToken);
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            { AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUserDto.getUserName(), Constants.LOGIN_FAIL, "账号或密码错误"));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUserDto.getUserName(), Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(sysUserDto.getUserName(), Constants.LOGIN_SUCCESS, "绑定成功"));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        recordLoginInfo(loginUser.getUserId());
        sysUserMapper.deleteUserGzhOpenId(sysUserDto.getOpenId());
        sysUserMapper.updateUserGzhOpenId(loginUser.getUserId(),sysUserDto.getOpenId());
        // 生成token
        tokenService.createToken(loginUser);
        // 获取客户信息
        GzhUserVo gzhVo = sysUserMapper.selectGzhUserDetail(loginUser.getUserId());
        return AjaxResult.success(gzhVo);
    }

    public AppSysUserMineVo getUserInfoByGzhOpenId(String openId, String type) {
        return sysUserMapper.selectUserByGzhOpenid(openId,type);
    }

    /**
     * 获取景区人员信息
     *
     * @param openId openId
     */
    public AppSysUserScenicVo getScenicUserInfoByGzhOpenId(String openId) {
        List<AppSysUserScenicVo> list = sysUserMapper.selectScenicUserByGzhOpenid(openId);
        if(list.isEmpty()){
            return new AppSysUserScenicVo();
        }else{
            return list.get(0);
        }
    }

    /**
     * 获取车企人员信息
     *
     * @param openId openId
     */
    public AppSysUserCarVo getCarUserInfoByGzhOpenId(String openId) {
        List<AppSysUserCarVo> list = sysUserMapper.selectCarUserByGzhOpenid(openId);
        if(list.isEmpty()){
            return new AppSysUserCarVo();
        }else{
            return list.get(0);
        }
    }

    /**
     * 获取JS-SDK配置
     */
    public Map<String,String> getJsSdk(String url) {
        String accessToken = getGzhAccessToken();
        String ticket = getJsapiTicket(accessToken);

        Map<String, String> config = new HashMap<>();
        String nonceStr = generateNonceStr(32);
        String timestamp = Long.toString(System.currentTimeMillis() / 1000);
        String signature = DigestUtils.sha1Hex("jsapi_ticket=" + ticket +
                "&noncestr=" + nonceStr +
                "&timestamp=" + timestamp +
                "&url=" + url);

        config.put("appId", mpAppId);
        config.put("nonceStr", nonceStr);
        config.put("timestamp", timestamp);
        config.put("signature", signature);
        return config;
    }

    /**
     * 获取ticket
     */
    private String getJsapiTicket(String accessToken) {
        String templateKey = CacheConstants.GZH_TICKET_KEY;
        if(redisCache.hasKey(templateKey)){
            return redisCache.getCacheObject(templateKey);
        }else{
            String url = AppConstants.ACCESS_TICKET_URL.replace("{0}", accessToken);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            Map<String, String> query = new HashMap<String, String>();
            try {
                HttpResponse response = com.bell.common.utils.aliyun.HttpUtils.doGet(url,"", "GET", headers, query);
                String responseBody  = EntityUtils.toString(response.getEntity());
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                redisCache.setCacheObject(templateKey, jsonObject.getString("ticket"), AppConstants.GZH_ACCESS_TOKEN_TIME, TimeUnit.MINUTES);
                return jsonObject.getString("ticket");
            }catch (Exception e){
                throw new ServiceException("获取公众号ticket失败");
            }
        }
    }

    /**
     * 获取AccessToken
     */
    private String getGzhAccessToken() {
        String templateKey = CacheConstants.GZH_ACCESS_TOKEN_KEY;
        if(redisCache.hasKey(templateKey)){
            return redisCache.getCacheObject(templateKey);
        }else{
            String url = AppConstants.ACCESS_TOKEN_URL.replace("{0}", mpAppId).replace("{1}", mpSecret);

            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            Map<String, String> query = new HashMap<String, String>();
            try {
                HttpResponse response = com.bell.common.utils.aliyun.HttpUtils.doGet(url,"", "GET", headers, query);
                String responseBody  = EntityUtils.toString(response.getEntity());
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                redisCache.setCacheObject(templateKey, jsonObject.getString("access_token"), AppConstants.GZH_ACCESS_TOKEN_TIME, TimeUnit.MINUTES);
                return jsonObject.getString("access_token");
            }catch (Exception e){
                throw new ServiceException("获取公众号token失败");
            }
        }
    }

    /**
     * 公众号签名随机数
     */
    public static String generateNonceStr(int length) {
        SecureRandom random = new SecureRandom();
        char[] nonceChars = new char[length];
        for (int i = 0; i < length; i++) {
            nonceChars[i] = AppConstants.SYMBOLS.charAt(random.nextInt(AppConstants.SYMBOLS.length()));
        }
        return new String(nonceChars);
    }

    /**
     * 公众号退出登录
     */
    public boolean gzhLogout(String openId, String type) {
        AppSysUserMineVo vo = sysUserMapper.selectUserByGzhOpenid(openId,type);
        return sysUserMapper.gzhLogout(Long.valueOf(vo.getUserId())) > 0;
    }
}
