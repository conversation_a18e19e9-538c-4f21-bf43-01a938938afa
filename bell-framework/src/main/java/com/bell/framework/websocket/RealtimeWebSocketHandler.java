package com.bell.framework.websocket;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class RealtimeWebSocketHandler extends TextWebSocketHandler {

    // 存储所有会话
    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    // Vue页面会话映射（按deviceId分组）
    private static final ConcurrentHashMap<String, WebSocketSession> vuePagesByDeviceId = new ConcurrentHashMap<>();
    // 小程序设备绑定映射（保留原有逻辑）
    private static final ConcurrentHashMap<String, String> deviceSessionMap = new ConcurrentHashMap<>();
    // Python客户端会话（按deviceId分组）
    private static final ConcurrentHashMap<String, WebSocketSession> pythonClientsByDeviceId = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.put(session.getId(), session);
        try {
            session.sendMessage(new TextMessage(
                    "{\"type\":\"system\", \"status\":\"CONNECTED\"}"
            ));
        } catch (IOException e) {
            System.err.println("连接确认消息发送失败");
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        JsonObject json = JsonParser.parseString(payload).getAsJsonObject();

        // 处理小程序设备绑定请求（保留原有逻辑）
        if (json.has("action") && "bind".equals(json.get("action").getAsString())) {
            String deviceId = json.get("deviceId").getAsString();
            deviceSessionMap.put(deviceId, session.getId());
            session.sendMessage(new TextMessage("{\"status\":\"BIND_SUCCESS\"}"));
        }
        // 处理Vue页面注册请求（按deviceId注册）
        else if (json.has("action") && "register_vue".equals(json.get("action").getAsString())) {
            String deviceId = json.get("deviceId").getAsString(); // 必须提供deviceId
            vuePagesByDeviceId.put(deviceId, session);
            session.sendMessage(new TextMessage("{\"status\":\"VUE_REGISTER_SUCCESS\",\"deviceId\":\"" + deviceId + "\"}"));
        }
        // 处理Python客户端注册请求（按deviceId注册）
        else if (json.has("action") && "register_python".equals(json.get("action").getAsString())) {
            String deviceId = json.get("deviceId").getAsString(); // 必须提供deviceId
            pythonClientsByDeviceId.put(deviceId, session);

            JsonObject response = new JsonObject();
            response.addProperty("status", "PYTHON_REGISTER_SUCCESS");
            response.addProperty("deviceId", deviceId);

            session.sendMessage(new TextMessage(response.toString()));
        }
        // 处理Python发送的鱼类识别数据
        else if (json.has("type") && "fish_recognition".equals(json.get("type").getAsString())) {
            String deviceId = json.get("deviceId").getAsString();
            String fishName = json.get("fishName").getAsString();
            String image = json.has("image") ? json.get("image").getAsString() : "";
            String length = json.has("fishLength") ? json.get("fishLength").getAsString() : "";
            long timestamp = json.has("timestamp") ? json.get("timestamp").getAsLong() : System.currentTimeMillis();

            // 构造转发消息
            JsonObject forwardMessage = new JsonObject();
            forwardMessage.addProperty("type", "fish_recognition");
            forwardMessage.addProperty("source", "python");
            forwardMessage.addProperty("deviceId", deviceId);
            forwardMessage.addProperty("fishName", fishName);
            forwardMessage.addProperty("fishLength", length);
            forwardMessage.addProperty("image", image);
            forwardMessage.addProperty("timestamp", timestamp);
            forwardMessage.addProperty("time", LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("HH:mm:ss")));

            // 转发到对应deviceId的Vue页面
            sendToVueByDeviceId(deviceId, forwardMessage.toString());
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        sessions.remove(sessionId);

        // 清理设备绑定映射（小程序）
        deviceSessionMap.entrySet().removeIf(entry -> entry.getValue().equals(sessionId));

        // 清理Vue页面会话映射
        vuePagesByDeviceId.entrySet().removeIf(entry -> entry.getValue().getId().equals(sessionId));

        // 清理Python客户端会话映射
        pythonClientsByDeviceId.entrySet().removeIf(entry -> entry.getValue().getId().equals(sessionId));


    }

    /**
     * 向指定设备关联的小程序推送数据（保留原有逻辑）
     */
    public void sendToDevice(String deviceId, String message) {
        String sessionId = deviceSessionMap.get(deviceId);
        if (sessionId != null) {
            WebSocketSession session = sessions.get(sessionId);
            if (session != null && session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (IOException e) {
                    System.err.println("推送到小程序失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 向指定deviceId的Vue页面推送数据
     */
    public void sendToVueByDeviceId(String deviceId, String message) {
        if (deviceId != null) {
            WebSocketSession session = vuePagesByDeviceId.get(deviceId);
            if (session != null && session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (IOException e) {
                    System.err.println("Vue页面推送失败: deviceId=" + deviceId + ", error=" + e.getMessage());
                }
            }
        }
    }


}