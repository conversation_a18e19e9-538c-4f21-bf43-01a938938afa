package ${packageName}.service.impl;

import java.util.List;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
import com.bell.common.utils.DateUtils;
#break
#end
#end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
#if($table.sub)
import java.util.ArrayList;
import com.bell.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service
{
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;

    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    @Override
    public ${ClassName} select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        return this.getById(${pkColumn.javaField});
    }

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> select${ClassName}List(${ClassName} ${className})
    {
        LambdaQueryWrapper<${ClassName}> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        #foreach ($column in $columns)
            #if($column.isPk != 1 && !$table.isSuperColumn($column.javaField) && $column.javaField != 'isDel')
                #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                if(ObjectUtil.isNotEmpty(${className}.get${AttrName}())) {
                lambdaQueryWrapper.${column.queryType.toLowerCase()}(${ClassName}::get${AttrName}
                ,${className}.get${AttrName}());
            }
            #end
        #end
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public boolean insert${ClassName}(${ClassName} ${className})
    {
        return this.save(${className});
    }

    /**
     * 修改${functionName}
     *
     * @param ${className} ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public boolean update${ClassName}(${ClassName} ${className})
    {
        return this.updateById(${className});
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
    @Override
    public boolean delete${ClassName}By${pkColumn.capJavaField}s(List<${pkColumn.javaType}> ${pkColumn.javaField}s)
    {
        return this.removeByIds(${pkColumn.javaField}s);
    }

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
    @Override
    public boolean delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        return this.removeById(${pkColumn.javaField});
    }
    #if($table.sub)

        /**
         * 新增${subTable.functionName}信息
         *
         * @param ${className} ${functionName}对象
         */
        public void insert${subClassName}(${ClassName} ${className})
        {
            List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
            ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
            if (StringUtils.isNotNull(${subclassName}List))
            {
                List<${subClassName}> list = new ArrayList<${subClassName}>();
                for (${subClassName} ${subclassName} : ${subclassName}List)
                {
                    ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                    list.add(${subclassName});
                }
                if (list.size() > 0)
                {
                        ${className}Mapper.batch${subClassName}(list);
                }
            }
        }
    #end
}
