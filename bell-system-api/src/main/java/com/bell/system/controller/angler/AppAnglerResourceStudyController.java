package com.bell.system.controller.angler;

import com.bell.angler.domain.AnglerResourceStudy;
import com.bell.angler.domain.dto.AppAnglerResourceStudyDto;
import com.bell.angler.service.IAnglerResourceStudyService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 钓客学习记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app/angler/study")
public class AppAnglerResourceStudyController extends BaseController
{
    @Autowired
    private IAnglerResourceStudyService anglerResourceStudyService;

    /**
     * 新增钓客学习记录
     */
    @Log(title = "钓客学习记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppAnglerResourceStudyDto appAnglerSchoolResourceStudyDto)
    {
        appAnglerSchoolResourceStudyDto.setUserId(getUserId());
        return success(anglerResourceStudyService.AppAnglerSchoolResourceStudy(appAnglerSchoolResourceStudyDto));
    }
}
