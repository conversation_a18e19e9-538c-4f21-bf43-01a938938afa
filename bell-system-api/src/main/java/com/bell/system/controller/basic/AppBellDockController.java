package com.bell.system.controller.basic;

import com.bell.basic.domain.BellDock;
import com.bell.basic.domain.vo.BellDockVo;
import com.bell.basic.service.IBellDockService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 码头Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppBellDockController extends BaseController {
    @Autowired
    private IBellDockService bellDockService;

    /**
     * 小程序查询码头列表
     */
    @GetMapping("/appDeparturePortList/{areaId}")
    public AjaxResult departurePortList(@PathVariable Long areaId) {
        BellDock bellDock = new BellDock();
        bellDock.setAreaId(areaId);
        List<BellDockVo> list = bellDockService.selectAppBellDockList(bellDock);
        return success(list);
    }


}
