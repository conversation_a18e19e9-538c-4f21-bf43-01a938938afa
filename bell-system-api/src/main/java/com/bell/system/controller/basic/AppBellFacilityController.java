package com.bell.system.controller.basic;

import com.bell.basic.domain.BellFacility;
import com.bell.basic.service.IBellFacilityService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 船舶设施Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppBellFacilityController extends BaseController {
    @Autowired
    private IBellFacilityService bellFacilityService;

    /**
     * 小程序获取船舶设施列表
     */
    @GetMapping("/selectFacilityList")
    public AjaxResult selectFacilityList() {
        List<BellFacility> list = bellFacilityService.selectFacilityList();
        return success(list);
    }


}
