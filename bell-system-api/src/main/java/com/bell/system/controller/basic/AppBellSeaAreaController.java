package com.bell.system.controller.basic;

import com.bell.basic.domain.BellSeaArea;
import com.bell.basic.domain.vo.BellSeaAreaVo;
import com.bell.basic.service.IBellSeaAreaService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 小程序海域Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app/basic/area")
public class AppBellSeaAreaController extends BaseController
{
    @Autowired
    private IBellSeaAreaService bellSeaAreaService;

    /**
     * 查询海域列表
     */
    @GetMapping("/list")
    public TableDataInfo list(BellSeaArea bellSeaArea)
    {
        startPage();
        List<BellSeaAreaVo> list = bellSeaAreaService.selectBellSeaAreaTree(bellSeaArea);
        return getDataTable(list);
    }

    /**
     * 获取海域详细信息
     */
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId)
    {
        return success(bellSeaAreaService.selectBellSeaAreaByAreaId(areaId));
    }
}
