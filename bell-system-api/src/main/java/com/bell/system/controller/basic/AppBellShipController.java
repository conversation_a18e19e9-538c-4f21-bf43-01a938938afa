package com.bell.system.controller.basic;

import com.bell.basic.domain.BellShip;
import com.bell.basic.domain.dto.BellShipDto;
import com.bell.basic.service.IBellShipService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 船只Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppBellShipController extends BaseController {
    @Autowired
    private IBellShipService bellShipService;

    /**
     * 小程序查询当前海域未绑定的船只列表
     */
    @PostMapping("/appShipList")
    public AjaxResult list(@RequestBody BellShip bellShip) {
        List<BellShip> list = bellShipService.selectAppShipList(bellShip);
        return success(list);
    }


    /**
     * 船艇绑定页面——获取船只详细信息
     */
    @GetMapping(value = "/appShipDetail/{shipId}")
    public AjaxResult getInfo(@PathVariable("shipId") Long shipId) {
        return success(bellShipService.appShipDetail(shipId));
    }

    /**
     * 小程序船艇绑定,绑定船只和码头
     *
     * @param bellShipDto 船只和码头信息
     * @return 结果
     */
    @PostMapping(value = "/appSaveShipAndDock")
    public AjaxResult updateShipAndDock(@RequestBody BellShipDto bellShipDto) {
        return toAjax(bellShipService.saveShipAndDock(bellShipDto));
    }

    /**
     * 获取选中船舶的预约日期有没有订单
     * @param shipId
     * @param bookDate
     * @return
     */
    @GetMapping("/getShipOrderToday")
    public AjaxResult getShipOrderToday(Long shipId, Date bookDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("shipId", shipId);
        param.put("bookDate", bookDate);
        return success(bellShipService.getShipOrderToday(param));
    }

    /**
     * 修改订单的场合下, 获取选中船舶的预约日期有没有订单
     * @param shipId
     * @param bookDate
     * @param orderId
     * @return
     */
    @GetMapping("/getShipOrderTodayUpdate")
    public AjaxResult getShipOrderTodayUpdate(Long shipId, Date bookDate, Long orderId) {
        Map<String, Object> param = new HashMap<>();
        param.put("shipId", shipId);
        param.put("bookDate", bookDate);
        param.put("orderId", orderId);
        return success(bellShipService.getShipOrderTodayUpdate(param));
    }
}
