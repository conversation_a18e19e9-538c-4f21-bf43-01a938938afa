package com.bell.system.controller.basic;

import cn.hutool.core.lang.Snowflake;
import com.bell.basic.domain.PostHouse;
import com.bell.basic.service.IPostHouseService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 驿站APPController
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app/house")
public class AppPostHouseController extends BaseController
{
    @Autowired
    private IPostHouseService postHouseService;

    /**
     * 查询驿站列表
     */
    @GetMapping("/list")
    public AjaxResult list(PostHouse postHouse)
    {
        return success(postHouseService.selectAppPostHouseList(postHouse));
    }

    /**
     * 驿站列表扫码称重
     */
    @GetMapping("/scanCode/{houseId}/{areaId}")
    public AjaxResult scanCodeToWeigh(@PathVariable("houseId") Integer houseId, @PathVariable("areaId") Integer areaId)
    {
        return postHouseService.scanCodeToWeigh(houseId, areaId);
    }

    /**
     * 驿站列表扫码开门
     */
    @GetMapping("/scanCodeOpen/{houseId}/{areaId}/{typeId}")
    public AjaxResult scanCodeOpen(@PathVariable("houseId") Integer houseId, @PathVariable("areaId") Integer areaId, @PathVariable("typeId") Integer typeId)
    {
        return postHouseService.scanCodeOpen(houseId, areaId, typeId);
    }
}
