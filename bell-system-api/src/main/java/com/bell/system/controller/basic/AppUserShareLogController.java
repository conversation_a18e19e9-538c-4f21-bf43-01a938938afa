package com.bell.system.controller.basic;

import com.bell.basic.domain.AppUserShareLog;
import com.bell.basic.service.IUserShareLogService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 我的分享
 */
@RestController
@RequestMapping("/app/appUserShareCollect")
public class AppUserShareLogController extends BaseController {

    @Autowired
    private IUserShareLogService userShareLogService;

    /**
     * 根据查询条件查询我的分享列表
     * @param appUserShareLog
     * @return
     */
    @GetMapping("/getUserShareLogList")
    public TableDataInfo getUserShareLogList(AppUserShareLog appUserShareLog) {
        startPage();
        appUserShareLog.setShareUserId(getUserId());
        List<AppUserShareLog> appUserShareLogList = userShareLogService.getUserShareLogList(appUserShareLog);
        return getDataTable(appUserShareLogList);
    }

    /**
     * 注册总人数
     * @param appUserShareLog
     * @return
     */
    @GetMapping("/getUserShareLogCount")
    public AjaxResult getUserShareLogCount(AppUserShareLog appUserShareLog) {
        return success(userShareLogService.getUserShareLogCount(appUserShareLog));
    }

    /**
     * 分享给鱼粮
     * @return
     */
    @GetMapping("/editUserScoreInfo")
    public AjaxResult editUserScoreInfo() {
        return success(userShareLogService.editUserScoreInfo());
    }
}
