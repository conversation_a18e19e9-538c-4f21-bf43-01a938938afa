package com.bell.system.controller.basic;

import com.bell.basic.domain.UserShipCollect;
import com.bell.basic.service.IUserShipCollectService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户收藏Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppUserShipCollectController extends BaseController {
    @Autowired
    private IUserShipCollectService userShipCollectService;

    /**
     * 新增用户收藏
     */
    @PostMapping("/saveShipCollect")
    public AjaxResult add(@RequestBody UserShipCollect userShipCollect) {
        return toAjax(userShipCollectService.insertUserShipCollect(userShipCollect));
    }

    /**
     * 删除用户收藏
     */
    @DeleteMapping("/deleteShipCollect/{collectId}")
    public AjaxResult deleteShipCollect(@PathVariable("collectId") Long collectId) {
        return toAjax(userShipCollectService.deleteShipCollect(collectId));
    }

    /**
     * 根据船艇id与用户id查询收藏信息
     * @param shipId
     * @return
     */
    @GetMapping("/getShipCollectByUserInfo/{shipId}")
    public AjaxResult getShipCollectByUserInfo(@PathVariable("shipId") Long shipId) {
        return success(userShipCollectService.getShipCollectByUserInfo(shipId));
    }
}
