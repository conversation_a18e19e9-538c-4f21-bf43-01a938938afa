package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.BellArticle;
import com.bell.basic.service.IBellArticleService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 文章，提示，规则等Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/article")
public class BellArticleController extends BaseController
{
    @Autowired
    private IBellArticleService bellArticleService;

    /**
     * 查询文章，提示，规则等列表
     */
    @PreAuthorize("@ss.hasPermi('basic:article:list')")
    @GetMapping("/list")
    public TableDataInfo list(BellArticle bellArticle)
    {
        startPage();
        List<BellArticle> list = bellArticleService.selectBellArticleList(bellArticle);
        return getDataTable(list);
    }

    /**
     * 导出文章，提示，规则等列表
     */
    @PreAuthorize("@ss.hasPermi('basic:article:export')")
    @Log(title = "文章，提示，规则等", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BellArticle bellArticle)
    {
        List<BellArticle> list = bellArticleService.selectBellArticleList(bellArticle);
        ExcelUtil<BellArticle> util = new ExcelUtil<BellArticle>(BellArticle.class);
        util.exportExcel(response, list, "文章，提示，规则等数据");
    }

    /**
     * 获取文章，提示，规则等详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:article:query')")
    @GetMapping(value = "/{articleId}")
    public AjaxResult getInfo(@PathVariable("articleId") Long articleId)
    {
        return success(bellArticleService.selectBellArticleByArticleId(articleId));
    }

    /**
     * 新增文章，提示，规则等
     */
    @PreAuthorize("@ss.hasPermi('basic:article:add')")
    @Log(title = "文章，提示，规则等", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BellArticle bellArticle)
    {
        return toAjax(bellArticleService.insertBellArticle(bellArticle));
    }

    /**
     * 修改文章，提示，规则等
     */
    @PreAuthorize("@ss.hasPermi('basic:article:edit')")
    @Log(title = "文章，提示，规则等", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BellArticle bellArticle)
    {
        return toAjax(bellArticleService.updateBellArticle(bellArticle));
    }

    /**
     * 删除文章，提示，规则等
     */
    @PreAuthorize("@ss.hasPermi('basic:article:remove')")
    @Log(title = "文章，提示，规则等", businessType = BusinessType.DELETE)
	@DeleteMapping("/{articleIds}")
    public AjaxResult remove(@PathVariable List<Long> articleIds)
    {
        return toAjax(bellArticleService.deleteBellArticleByArticleIds(articleIds));
    }
}
