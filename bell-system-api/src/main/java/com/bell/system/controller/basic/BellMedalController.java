package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.BellMedal;
import com.bell.basic.service.IBellMedalService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 勋章Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/medal")
public class BellMedalController extends BaseController
{
    @Autowired
    private IBellMedalService bellMedalService;

    /**
     * 查询勋章列表
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:list')")
    @GetMapping("/list")
    public TableDataInfo list(BellMedal bellMedal)
    {
        startPage();
        List<BellMedal> list = bellMedalService.selectBellMedalList(bellMedal);
        return getDataTable(list);
    }

    /**
     * 导出勋章列表
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:export')")
    @Log(title = "勋章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BellMedal bellMedal)
    {
        List<BellMedal> list = bellMedalService.selectBellMedalList(bellMedal);
        ExcelUtil<BellMedal> util = new ExcelUtil<BellMedal>(BellMedal.class);
        util.exportExcel(response, list, "勋章数据");
    }

    /**
     * 获取勋章详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:query')")
    @GetMapping(value = "/{medalId}")
    public AjaxResult getInfo(@PathVariable("medalId") Integer medalId)
    {
        return success(bellMedalService.selectBellMedalByMedalId(medalId));
    }

    /**
     * 新增勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:add')")
    @Log(title = "勋章", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BellMedal bellMedal)
    {
        return toAjax(bellMedalService.insertBellMedal(bellMedal));
    }

    /**
     * 修改勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:edit')")
    @Log(title = "勋章", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BellMedal bellMedal)
    {
        return toAjax(bellMedalService.updateBellMedal(bellMedal));
    }

    /**
     * 删除勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:medal:remove')")
    @Log(title = "勋章", businessType = BusinessType.DELETE)
	@DeleteMapping("/{medalIds}")
    public AjaxResult remove(@PathVariable List<Integer> medalIds)
    {
        return toAjax(bellMedalService.deleteBellMedalByMedalIds(medalIds));
    }
}
