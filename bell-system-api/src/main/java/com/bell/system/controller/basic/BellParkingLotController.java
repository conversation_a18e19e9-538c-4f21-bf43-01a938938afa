package com.bell.system.controller.basic;

import com.bell.basic.domain.BellParkingLot;
import com.bell.basic.domain.vo.BellParkingLotVo;
import com.bell.basic.service.IBellParkingLotService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 停车场Controller
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@RestController
@RequestMapping("/basic/parkingLot")
public class BellParkingLotController extends BaseController {
    @Autowired
    private IBellParkingLotService bellParkingLotService;

    /**
     * 查询停车场列表
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:list')")
    @GetMapping("/list")
    public TableDataInfo list(BellParkingLot bellParkingLot) {
        startPage();
        List<BellParkingLotVo> list = bellParkingLotService.selectBellParkingLotList(bellParkingLot);
        return getDataTable(list);
    }

    /**
     * 导出停车场列表
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:export')")
    @Log(title = "停车场", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BellParkingLot bellParkingLot) {
        List<BellParkingLotVo> list = bellParkingLotService.selectBellParkingLotList(bellParkingLot);
        ExcelUtil<BellParkingLotVo> util = new ExcelUtil<>(BellParkingLotVo.class);
        util.exportExcel(response, list, "停车场数据");
    }

    /**
     * 获取停车场详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:query')")
    @GetMapping(value = "/{parkingLotId}")
    public AjaxResult getInfo(@PathVariable("parkingLotId") Long parkingLotId) {
        return success(bellParkingLotService.selectBellParkingLotByParkingLotId(parkingLotId));
    }

    /**
     * 新增停车场
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:add')")
    @Log(title = "停车场", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BellParkingLot bellParkingLot) {
        return toAjax(bellParkingLotService.insertBellParkingLot(bellParkingLot));
    }

    /**
     * 修改停车场
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:edit')")
    @Log(title = "停车场", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BellParkingLot bellParkingLot) {
        return toAjax(bellParkingLotService.updateBellParkingLot(bellParkingLot));
    }

    /**
     * 删除停车场
     */
    @PreAuthorize("@ss.hasPermi('basic:parkingLot:remove')")
    @Log(title = "停车场", businessType = BusinessType.DELETE)
    @DeleteMapping("/{parkingLotIds}")
    public AjaxResult remove(@PathVariable List<Long> parkingLotIds) {
        return bellParkingLotService.deleteBellParkingLotByParkingLotIds(parkingLotIds);
    }

    /**
     * 根据一级地区的id获取二级乡镇集合
     *
     * @param areaId 地区id
     * @return 乡镇集合
     */
    @GetMapping("/villagesList/{areaId}")
    public AjaxResult getVillagesList(@PathVariable("areaId") Long areaId) {
        return success(bellParkingLotService.getVillagesList(areaId));
    }
}
