package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.BellRefundReason;
import com.bell.basic.service.IBellRefundReasonService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 退款原因Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/reason")
public class BellRefundReasonController extends BaseController
{
    @Autowired
    private IBellRefundReasonService bellRefundReasonService;

    /**
     * 查询退款原因列表
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:list')")
    @GetMapping("/list")
    public TableDataInfo list(BellRefundReason bellRefundReason)
    {
        startPage();
        List<BellRefundReason> list = bellRefundReasonService.selectBellRefundReasonList(bellRefundReason);
        return getDataTable(list);
    }

    /**
     * 导出退款原因列表
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:export')")
    @Log(title = "退款原因", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BellRefundReason bellRefundReason)
    {
        List<BellRefundReason> list = bellRefundReasonService.selectBellRefundReasonList(bellRefundReason);
        ExcelUtil<BellRefundReason> util = new ExcelUtil<BellRefundReason>(BellRefundReason.class);
        util.exportExcel(response, list, "退款原因数据");
    }

    /**
     * 获取退款原因详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:query')")
    @GetMapping(value = "/{reasonId}")
    public AjaxResult getInfo(@PathVariable("reasonId") Long reasonId)
    {
        return success(bellRefundReasonService.selectBellRefundReasonByReasonId(reasonId));
    }

    /**
     * 新增退款原因
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:add')")
    @Log(title = "退款原因", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BellRefundReason bellRefundReason)
    {
        return toAjax(bellRefundReasonService.insertBellRefundReason(bellRefundReason));
    }

    /**
     * 修改退款原因
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:edit')")
    @Log(title = "退款原因", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BellRefundReason bellRefundReason)
    {
        return toAjax(bellRefundReasonService.updateBellRefundReason(bellRefundReason));
    }

    /**
     * 删除退款原因
     */
    @PreAuthorize("@ss.hasPermi('basic:reason:remove')")
    @Log(title = "退款原因", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reasonIds}")
    public AjaxResult remove(@PathVariable List<Long> reasonIds)
    {
        return toAjax(bellRefundReasonService.deleteBellRefundReasonByReasonIds(reasonIds));
    }
}
