package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.basic.domain.vo.BellScaleVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.BellScale;
import com.bell.basic.service.IBellScaleService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 秤Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/scale")
public class BellScaleController extends BaseController
{
    @Autowired
    private IBellScaleService bellScaleService;

    /**
     * 查询秤列表
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:list')")
    @GetMapping("/list")
    public TableDataInfo list(BellScale bellScale)
    {
        startPage();
        List<BellScale> list = bellScaleService.selectBellScaleList(bellScale);
        return getDataTable(list);
    }

    /**
     * 导出秤列表
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:export')")
    @Log(title = "秤", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BellScale bellScale)
    {
        List<BellScale> list = bellScaleService.selectBellScaleList(bellScale);
        ExcelUtil<BellScale> util = new ExcelUtil<BellScale>(BellScale.class);
        util.exportExcel(response, list, "秤数据");
    }

    /**
     * 获取秤详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:query')")
    @GetMapping(value = "/{scaleId}")
    public AjaxResult getInfo(@PathVariable("scaleId") Long scaleId)
    {
        return success(bellScaleService.selectBellScaleByScaleId(scaleId));
    }

    /**
     * 新增秤
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:add')")
    @Log(title = "秤", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BellScale bellScale)
    {
        return toAjax(bellScaleService.insertBellScale(bellScale));
    }

    /**
     * 修改秤
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:edit')")
    @Log(title = "秤", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BellScale bellScale)
    {
        return toAjax(bellScaleService.updateBellScale(bellScale));
    }

    /**
     * 删除秤
     */
    @PreAuthorize("@ss.hasPermi('basic:scale:remove')")
    @Log(title = "秤", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scaleIds}")
    public AjaxResult remove(@PathVariable List<Long> scaleIds)
    {
        return toAjax(bellScaleService.deleteBellScaleByScaleIds(scaleIds));
    }

    /**
     * 查询秤列表
     */
    @GetMapping("/selectList")
    public TableDataInfo selectList(BellScale bellScale){
        startPage();
        List<BellScaleVO> list = bellScaleService.selectList(bellScale);
        return getDataTable(list);
    }
//    @PreAuthorize("@ss.hasPermi('system:qrcode:export')")
    @PostMapping("/exportZip")
    public void  exportZip(HttpServletResponse response, BellScale bellScale) {
        bellScaleService.exportZip(response, bellScale);
    }
    
    /**
     * 检查秤编号是否存在
     */
    @GetMapping("/checkScaleNo")
    public AjaxResult checkScaleNo(String scaleNo)
    {
        return success(bellScaleService.checkScaleNoExist(scaleNo));
    }

    /**
     * 检查驿站是否已绑定其他秤
     */
    @GetMapping("/checkHouseBinding")
    public AjaxResult checkHouseBinding(Long houseId, Long scaleId)
    {
        return success(bellScaleService.checkHouseBinding(houseId, scaleId));
    }
}
