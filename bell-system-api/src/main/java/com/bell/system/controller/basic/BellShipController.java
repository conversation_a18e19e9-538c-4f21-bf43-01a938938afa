package com.bell.system.controller.basic;

import com.bell.basic.domain.dto.BellShipDto;
import com.bell.basic.domain.dto.ShipDto;
import com.bell.basic.domain.dto.ShipEditDto;
import com.bell.basic.domain.vo.BellShipExportVo;
import com.bell.basic.domain.vo.ShipCaptainRecordVo;
import com.bell.basic.domain.vo.ShipDockDetailVo;
import com.bell.basic.domain.vo.ShipListVo;
import com.bell.basic.service.IBellShipService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 船只Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/ship")
public class BellShipController extends BaseController
{
    @Autowired
    private IBellShipService bellShipService;

    /**
     * 查询船只列表
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipDto shipDto) {
        startPage();
        List<ShipListVo> list = bellShipService.selectShipList(shipDto);
        TableDataInfo dataTable = getDataTable(list);
        return dataTable;
    }

    /**
     * 导出船只列表
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:export')")
    @Log(title = "船只", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipDto shipDto) {
        List<BellShipExportVo> list = bellShipService.exportBellShip(shipDto);
        ExcelUtil<BellShipExportVo> util = new ExcelUtil<BellShipExportVo>(BellShipExportVo.class);
        util.exportExcel(response, list, "船只数据");
    }

    /**
     * 获取船只详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:query')")
    @GetMapping(value = "/{shipId}")
    public AjaxResult getInfo(@PathVariable("shipId") Long shipId) {
        return success(bellShipService.selectBellShipByShipId(shipId));
    }

    /**
     * 新增船只
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:add')")
    @Log(title = "船只", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipEditDto shipEditDto) {
        return bellShipService.insertBellShip(shipEditDto);
    }

    /**
     * 修改船只
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:edit')")
    @Log(title = "船只", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipEditDto shipEditDto) {
        return bellShipService.updateBellShip(shipEditDto);
    }

    /**
     * 删除船只
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:remove')")
    @Log(title = "船只", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipIds}")
    public AjaxResult remove(@PathVariable List<Long> shipIds) {
        return bellShipService.deleteShipByShipId(shipIds);
    }

    /**
     * 绑定船长（未绑定船长）
     * @param shipDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:edit')")
    @PostMapping("/bindingCaptionForShip")
    public AjaxResult bindingCaptionForShip(@RequestBody ShipDto shipDto) {
        return bellShipService.bindingCaptionForShip(shipDto);
    }

    /**
     * 获取船艇与出港码头数据
     * @param shipDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('basic:ship:edit')")
    @GetMapping("/getShipDockDetail")
    public AjaxResult getShipDockDetail(ShipDto shipDto) {
        ShipDockDetailVo shipDockDetail = bellShipService.getShipDockDetail(shipDto);
        return success(shipDockDetail);
    }

    /**
     * 绑定船长（已绑定船长）
     * @param shipDto
     * @return
     */
    @PostMapping("/bindingedCaptionForShip")
    public AjaxResult bindingedCaptionForShip(@RequestBody ShipDto shipDto) {
        bellShipService.bindingedCaptionForShip(shipDto);
        return success();
    }

    /**
     * 解绑船长
     * @param shipDto
     * @return
     */
    @PostMapping("/deleteCaptionForShip")
    public AjaxResult deleteCaptionForShip(@RequestBody ShipDto shipDto) {
        return bellShipService.deleteCaptionForShip(shipDto);
    }

    /**
     * 根据船艇主键查询绑定记录
     * @param shipDto
     * @return
     */
    @GetMapping("/getShipBindingRecord")
    public AjaxResult getShipBindingRecord(ShipDto shipDto) {
        List<ShipCaptainRecordVo> shipBindingRecord = bellShipService.getShipBindingRecord(shipDto);
        return success(shipBindingRecord);
    }

    /**
     * 船长绑定、换绑船艇
     *
     * @param bellShipDto 船艇信息
     * @return 结果
     */
    @PutMapping("/bindShip")
    public AjaxResult bindShip(@RequestBody BellShipDto bellShipDto) {
        return bellShipService.bindShip(bellShipDto);
    }

    /**
     * 已绑定的船艇的详细信息
     *
     * @param shipId 船只id
     * @return 详细信息
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:bindShip')")
    @GetMapping("/getShipInfoAndDock/{shipId}")
    public AjaxResult getShipInfoAndDock(@PathVariable("shipId") Long shipId) {
        return success(bellShipService.getShipInfoAndDock(shipId));
    }

    /**
     * 根据船只id解绑船长
     *
     * @param shipId 船只id
     * @return 结果
     */
    @GetMapping("/unbindShip/{shipId}")
    public AjaxResult unbindShip(@PathVariable("shipId") Long shipId) {
        return bellShipService.unbindShip(shipId);
    }

    /**
     * 获取设施列表
     * @return
     */
    @GetMapping("/getFacilityInfo")
    public AjaxResult getFacilityInfo() {
        return success(bellShipService.getFacilityInfo());
    }

    /**
     * 获取俱乐部列表
     * @return
     */
    @GetMapping("/getShipClubList")
    public AjaxResult getShipClubList() {
        return success(bellShipService.getShipClubList());
    }
}
