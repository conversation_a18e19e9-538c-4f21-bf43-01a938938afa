package com.bell.system.controller.basic;

import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.framework.service.UnifiedDataPushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 设备数据Controller
 *
 * <AUTHOR>
 * @date 2025-01-XX
 */
@RestController
@RequestMapping("/basic/device")
public class DeviceDataController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(DeviceDataController.class);

    @Autowired
    private UnifiedDataPushService dataService;



    /**
     * 提交整合数据到小程序（按设备ID）
     */
    @PostMapping("/submitToMiniProgram")
    public AjaxResult submitToMiniProgram(@RequestBody Map<String, Object> data) {
        try {
            log.info("提交整合数据到小程序: {}", data);

            // 验证必要参数
            if (!data.containsKey("deviceId")) {
                return AjaxResult.error("缺少deviceId参数");
            }

            String deviceId = data.get("deviceId").toString();

            // 直接推送到指定设备的小程序
            dataService.pushToMiniProgram(deviceId, data);

            return AjaxResult.success("数据已提交到小程序");

        } catch (Exception e) {
            log.error("提交到小程序失败: {}", e.getMessage(), e);
            return AjaxResult.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 提交数据到指定deviceId的小程序
     */
    @PostMapping("/submitByDeviceId")
    public AjaxResult submitByDeviceId(@RequestBody Map<String, Object> data) {
        try {
            log.info("按deviceId提交数据: {}", data);

            // 验证必要参数
            if (!data.containsKey("deviceId")) {
                return AjaxResult.error("缺少deviceId参数");
            }

            String deviceId = data.get("deviceId").toString();

            // 推送自定义数据到指定deviceId的Vue页面
            dataService.pushCustomDataToDevice(deviceId, "manual_submit", "device_vue", data);

            return AjaxResult.success("数据已提交到设备: " + deviceId);

        } catch (Exception e) {
            log.error("按deviceId提交失败: {}", e.getMessage(), e);
            return AjaxResult.error("提交失败: " + e.getMessage());
        }
    }
}
