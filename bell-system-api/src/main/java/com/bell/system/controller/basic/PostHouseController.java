package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.basic.domain.dto.PostHouseDetailDto;
import com.bell.basic.domain.dto.PostHouseDto;
import com.bell.basic.domain.vo.PostHouseVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.PostHouse;
import com.bell.basic.service.IPostHouseService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 驿站Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/house")
public class PostHouseController extends BaseController
{
    @Autowired
    private IPostHouseService postHouseService;

    /**
     * 查询驿站列表
     */
    @PreAuthorize("@ss.hasPermi('basic:house:list')")
    @GetMapping("/list")
    public TableDataInfo list(PostHouseDto postHouseDto)
    {
        startPage();
        List<PostHouseVo> list = postHouseService.getPostHouseListForManager(postHouseDto);
        return getDataTable(list);
    }

    /**
     * 导出驿站列表
     */
    @PreAuthorize("@ss.hasPermi('basic:house:export')")
    @Log(title = "驿站", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PostHouse postHouse)
    {
        List<PostHouse> list = postHouseService.selectPostHouseList(postHouse);
        ExcelUtil<PostHouse> util = new ExcelUtil<PostHouse>(PostHouse.class);
        util.exportExcel(response, list, "驿站数据");
    }

    /**
     * 获取驿站详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:house:query')")
    @GetMapping(value = "/{houseId}")
    public AjaxResult getInfo(@PathVariable("houseId") Long houseId)
    {
        return success(postHouseService.getPostHouseDetail(houseId));
    }

    /**
     * 新增驿站
     */
    @PreAuthorize("@ss.hasPermi('basic:house:add')")
    @Log(title = "驿站", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostHouseDetailDto postHouseDetailDto)
    {
        return toAjax(postHouseService.insertPostHouse(postHouseDetailDto));
    }

    /**
     * 修改驿站
     */
    @PreAuthorize("@ss.hasPermi('basic:house:edit')")
    @Log(title = "驿站", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostHouseDetailDto postHouseDetailDto)
    {
        return toAjax(postHouseService.updatePostHouse(postHouseDetailDto));
    }

    /**
     * 删除驿站
     */
    @PreAuthorize("@ss.hasPermi('basic:house:remove')")
    @Log(title = "驿站", businessType = BusinessType.DELETE)
	@DeleteMapping("/{houseIds}")
    public AjaxResult remove(@PathVariable List<Long> houseIds)
    {
        return toAjax(postHouseService.deletePostHouseByHouseIds(houseIds));
    }

    /**
     * 更改驿站状态
     * @param postHouseDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('basic:house:edit')")
    @PostMapping("/editState")
    public AjaxResult editState(@RequestBody PostHouseDto postHouseDto) {
        return success(postHouseService.editState(postHouseDto));
    }

    /**
     * 查询驿站列表（代码生成）
     */
    @GetMapping("/selectHouseList")
    public AjaxResult selectHouseList(PostHouse postHouse){
        return success(postHouseService.selectHouseList(postHouse));
    }

    /**
     * 检查驿站名称是否已存在
     * @param houseName 驿站名称
     * @return 结果
     */
    @GetMapping("/checkName")
    public AjaxResult checkName(String houseName){
        PostHouse postHouse = new PostHouse();
        postHouse.setHouseName(houseName);
        List<PostHouse> list = postHouseService.selectHouseList(postHouse);
        return success(!list.isEmpty());
    }
}
