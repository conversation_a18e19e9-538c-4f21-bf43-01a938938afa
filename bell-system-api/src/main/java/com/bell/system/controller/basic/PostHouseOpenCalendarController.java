package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.PostHouseOpenCalendar;
import com.bell.basic.service.IPostHouseOpenCalendarService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 驿站日历【特殊日期单独设置】Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/houseCalendar")
public class PostHouseOpenCalendarController extends BaseController
{
    @Autowired
    private IPostHouseOpenCalendarService postHouseOpenCalendarService;

    /**
     * 查询驿站日历【特殊日期单独设置】列表
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:list')")
    @GetMapping("/list")
    public TableDataInfo list(PostHouseOpenCalendar postHouseOpenCalendar)
    {
        startPage();
        List<PostHouseOpenCalendar> list = postHouseOpenCalendarService.selectPostHouseOpenCalendarList(postHouseOpenCalendar);
        return getDataTable(list);
    }

    /**
     * 导出驿站日历【特殊日期单独设置】列表
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:export')")
    @Log(title = "驿站日历【特殊日期单独设置】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PostHouseOpenCalendar postHouseOpenCalendar)
    {
        List<PostHouseOpenCalendar> list = postHouseOpenCalendarService.selectPostHouseOpenCalendarList(postHouseOpenCalendar);
        ExcelUtil<PostHouseOpenCalendar> util = new ExcelUtil<PostHouseOpenCalendar>(PostHouseOpenCalendar.class);
        util.exportExcel(response, list, "驿站日历【特殊日期单独设置】数据");
    }

    /**
     * 获取驿站日历【特殊日期单独设置】详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:query')")
    @GetMapping(value = "/{calendarId}")
    public AjaxResult getInfo(@PathVariable("calendarId") Long calendarId)
    {
        return success(postHouseOpenCalendarService.selectPostHouseOpenCalendarByCalendarId(calendarId));
    }

    /**
     * 新增驿站日历【特殊日期单独设置】
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:add')")
    @Log(title = "驿站日历【特殊日期单独设置】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PostHouseOpenCalendar postHouseOpenCalendar)
    {
        return toAjax(postHouseOpenCalendarService.insertPostHouseOpenCalendar(postHouseOpenCalendar));
    }

    /**
     * 修改驿站日历【特殊日期单独设置】
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:edit')")
    @Log(title = "驿站日历【特殊日期单独设置】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PostHouseOpenCalendar postHouseOpenCalendar)
    {
        return toAjax(postHouseOpenCalendarService.updatePostHouseOpenCalendar(postHouseOpenCalendar));
    }

    /**
     * 删除驿站日历【特殊日期单独设置】
     */
    @PreAuthorize("@ss.hasPermi('basic:houseCalendar:remove')")
    @Log(title = "驿站日历【特殊日期单独设置】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{calendarIds}")
    public AjaxResult remove(@PathVariable List<Long> calendarIds)
    {
        return toAjax(postHouseOpenCalendarService.deletePostHouseOpenCalendarByCalendarIds(calendarIds));
    }
}
