package com.bell.system.controller.basic;

import com.bell.basic.domain.ShipDock;
import com.bell.basic.domain.vo.ShipDockVo;
import com.bell.basic.service.IShipDockService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 船只码头Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/shipDock")
public class ShipDockController extends BaseController
{
    @Autowired
    private IShipDockService shipDockService;

    /**
     * 查询船只码头列表
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipDock shipDock)
    {
        startPage();
        List<ShipDock> list = shipDockService.selectShipDockList(shipDock);
        return getDataTable(list);
    }

    /**
     * 导出船只码头列表
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:export')")
    @Log(title = "船只码头", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipDock shipDock)
    {
        List<ShipDock> list = shipDockService.selectShipDockList(shipDock);
        ExcelUtil<ShipDock> util = new ExcelUtil<ShipDock>(ShipDock.class);
        util.exportExcel(response, list, "船只码头数据");
    }

    /**
     * 获取船只码头详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:query')")
    @GetMapping(value = "/{shipDockId}")
    public AjaxResult getInfo(@PathVariable("shipDockId") Long shipDockId)
    {
        return success(shipDockService.selectShipDockByShipDockId(shipDockId));
    }

    /**
     * 新增船只码头
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:add')")
    @Log(title = "船只码头", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipDock shipDock)
    {
        return toAjax(shipDockService.insertShipDock(shipDock));
    }

    /**
     * 修改船只码头
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:edit')")
    @Log(title = "船只码头", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipDock shipDock)
    {
        return toAjax(shipDockService.updateShipDock(shipDock));
    }

    /**
     * 删除船只码头
     */
    @PreAuthorize("@ss.hasPermi('basic:shipDock:remove')")
    @Log(title = "船只码头", businessType = BusinessType.DELETE)
	@DeleteMapping("/{shipDockIds}")
    public AjaxResult remove(@PathVariable List<Long> shipDockIds)
    {
        return toAjax(shipDockService.deleteShipDockByShipDockIds(shipDockIds));
    }

    /**
     * 更新出港码头的价格
     *
     * @param outPortList 更新出港码头
     * @return 结果
     */
    @PostMapping("/updateShipDockList")
    public AjaxResult updateShipDockList(@RequestBody List<ShipDockVo> outPortList) {
        return toAjax(shipDockService.updateShipDockList(outPortList));
    }
}
