package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.UserMedal;
import com.bell.basic.service.IUserMedalService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 用户勋章Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/userMedal")
public class UserMedalController extends BaseController
{
    @Autowired
    private IUserMedalService userMedalService;

    /**
     * 查询用户勋章列表
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserMedal userMedal)
    {
        startPage();
        List<UserMedal> list = userMedalService.selectUserMedalList(userMedal);
        return getDataTable(list);
    }

    /**
     * 导出用户勋章列表
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:export')")
    @Log(title = "用户勋章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserMedal userMedal)
    {
        List<UserMedal> list = userMedalService.selectUserMedalList(userMedal);
        ExcelUtil<UserMedal> util = new ExcelUtil<UserMedal>(UserMedal.class);
        util.exportExcel(response, list, "用户勋章数据");
    }

    /**
     * 获取用户勋章详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:query')")
    @GetMapping(value = "/{userMedalId}")
    public AjaxResult getInfo(@PathVariable("userMedalId") Long userMedalId)
    {
        return success(userMedalService.selectUserMedalByUserMedalId(userMedalId));
    }

    /**
     * 新增用户勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:add')")
    @Log(title = "用户勋章", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserMedal userMedal)
    {
        return toAjax(userMedalService.insertUserMedal(userMedal));
    }

    /**
     * 修改用户勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:edit')")
    @Log(title = "用户勋章", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserMedal userMedal)
    {
        return toAjax(userMedalService.updateUserMedal(userMedal));
    }

    /**
     * 删除用户勋章
     */
    @PreAuthorize("@ss.hasPermi('basic:userMedal:remove')")
    @Log(title = "用户勋章", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userMedalIds}")
    public AjaxResult remove(@PathVariable List<Long> userMedalIds)
    {
        return toAjax(userMedalService.deleteUserMedalByUserMedalIds(userMedalIds));
    }
}
