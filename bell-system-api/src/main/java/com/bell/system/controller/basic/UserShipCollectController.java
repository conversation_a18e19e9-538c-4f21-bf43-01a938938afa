package com.bell.system.controller.basic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.basic.domain.UserShipCollect;
import com.bell.basic.service.IUserShipCollectService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 用户收藏Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/basic/shipCollect")
public class UserShipCollectController extends BaseController
{
    @Autowired
    private IUserShipCollectService userShipCollectService;

    /**
     * 查询用户收藏列表
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserShipCollect userShipCollect)
    {
        startPage();
        List<UserShipCollect> list = userShipCollectService.selectUserShipCollectList(userShipCollect);
        return getDataTable(list);
    }

    /**
     * 导出用户收藏列表
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:export')")
    @Log(title = "用户收藏", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserShipCollect userShipCollect)
    {
        List<UserShipCollect> list = userShipCollectService.selectUserShipCollectList(userShipCollect);
        ExcelUtil<UserShipCollect> util = new ExcelUtil<UserShipCollect>(UserShipCollect.class);
        util.exportExcel(response, list, "用户收藏数据");
    }

    /**
     * 获取用户收藏详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:query')")
    @GetMapping(value = "/{collectId}")
    public AjaxResult getInfo(@PathVariable("collectId") Long collectId)
    {
        return success(userShipCollectService.selectUserShipCollectByCollectId(collectId));
    }

    /**
     * 新增用户收藏
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:add')")
    @Log(title = "用户收藏", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserShipCollect userShipCollect)
    {
        return toAjax(userShipCollectService.insertUserShipCollect(userShipCollect));
    }

    /**
     * 修改用户收藏
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:edit')")
    @Log(title = "用户收藏", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserShipCollect userShipCollect)
    {
        return toAjax(userShipCollectService.updateUserShipCollect(userShipCollect));
    }

    /**
     * 删除用户收藏
     */
    @PreAuthorize("@ss.hasPermi('basic:shipCollect:remove')")
    @Log(title = "用户收藏", businessType = BusinessType.DELETE)
	@DeleteMapping("/{collectIds}")
    public AjaxResult remove(@PathVariable List<Long> collectIds)
    {
        return toAjax(userShipCollectService.deleteUserShipCollectByCollectIds(collectIds));
    }
}
