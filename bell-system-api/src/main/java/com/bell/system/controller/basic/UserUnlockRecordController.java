package com.bell.system.controller.basic;

import com.bell.basic.domain.UserUnlockRecord;
import com.bell.basic.service.IUserUnlockRecordService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 驿站开锁记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/basic/unlock")
public class UserUnlockRecordController extends BaseController {
    @Autowired
    private IUserUnlockRecordService userUnlockRecordService;

    /**
     * 查询驿站开锁记录列表
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserUnlockRecord userUnlockRecord) {
        startPage();
        List<UserUnlockRecord> list = userUnlockRecordService.selectUserUnlockRecordList(userUnlockRecord);
        return getDataTable(list);
    }

    /**
     * 导出驿站开锁记录列表
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:export')")
    @Log(title = "驿站开锁记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserUnlockRecord userUnlockRecord) {
        List<UserUnlockRecord> list = userUnlockRecordService.selectUserUnlockRecordList(userUnlockRecord);
        ExcelUtil<UserUnlockRecord> util = new ExcelUtil<UserUnlockRecord>(UserUnlockRecord.class);
        util.exportExcel(response, list, "驿站开锁记录数据");
    }

    /**
     * 获取驿站开锁记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:query')")
    @GetMapping(value = "/{unlockId}")
    public AjaxResult getInfo(@PathVariable("unlockId") Long unlockId) {
        return success(userUnlockRecordService.selectUserUnlockRecordByUnlockId(unlockId));
    }

    /**
     * 新增驿站开锁记录
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:add')")
    @Log(title = "驿站开锁记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserUnlockRecord userUnlockRecord) {
        return toAjax(userUnlockRecordService.insertUserUnlockRecord(userUnlockRecord));
    }

    /**
     * 修改驿站开锁记录
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:edit')")
    @Log(title = "驿站开锁记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserUnlockRecord userUnlockRecord) {
        return toAjax(userUnlockRecordService.updateUserUnlockRecord(userUnlockRecord));
    }

    /**
     * 删除驿站开锁记录
     */
    @PreAuthorize("@ss.hasPermi('basic:unlock:remove')")
    @Log(title = "驿站开锁记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{unlockIds}")
    public AjaxResult remove(@PathVariable List<Long> unlockIds) {
        return toAjax(userUnlockRecordService.deleteUserUnlockRecordByUnlockIds(unlockIds));
    }
}
