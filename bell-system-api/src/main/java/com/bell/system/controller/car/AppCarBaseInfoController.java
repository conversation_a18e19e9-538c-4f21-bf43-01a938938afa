package com.bell.system.controller.car;

import com.bell.car.domain.CarBaseInfo;
import com.bell.car.domain.Dto.CarBaseInfoDto;
import com.bell.car.domain.VO.CarBaseInfoVo;
import com.bell.car.service.ICarBaseInfoService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/car/carBaseInfo")
public class AppCarBaseInfoController extends BaseController
{
    @Autowired
    private ICarBaseInfoService carBaseInfoService;

    @GetMapping(value = "/{carId}/{areaId}")
    public AjaxResult getInfo(@PathVariable("carId") Long carId, @PathVariable("areaId") Long areaId) {
        return success(carBaseInfoService.selectAppCarBaseInfoByCarId(carId, areaId));
    }

}
