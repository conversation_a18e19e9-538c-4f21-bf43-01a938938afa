package com.bell.system.controller.car;

import com.bell.car.domain.CarBaseInfoEntity;
import com.bell.car.domain.Dto.CarListDto;
import com.bell.car.domain.VO.CarDetailVo;
import com.bell.car.domain.VO.CarEvaluationVo;
import com.bell.car.service.ICarListService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序 车辆列表controller
 */
@RestController
@RequestMapping("/app")
public class AppCarListController extends BaseController {

    @Autowired
    private ICarListService carListService;

    /**
     * 小程序 租车首页信息查询
     * @param carListDto
     * @return
     */
    @GetMapping("/getCarHomePageInfo")
    public AjaxResult getCarHomePageInfo(CarListDto carListDto) {
        return success(carListService.getCarHomePageInfo(carListDto));
    }

    /**
     * 小程序 车辆列表初始化方法
     * @param carListDto
     * @return
     */
    @GetMapping("/getCarList")
    public AjaxResult getCarList(CarListDto carListDto) {
        return success(carListService.getCarList(carListDto));
    }

    /**
     * 小程序 根据查询条件查询车辆列表
     * @param carListDto
     * @return
     */
    @PostMapping("/getCarBaseInfoList")
    public TableDataInfo getCarBaseInfoList(@RequestBody CarListDto carListDto) {
        startPage();
        List<CarBaseInfoEntity> carBaseInfoList = carListService.getCarBaseInfoList(carListDto);
        return getDataTable(carBaseInfoList);
    }

    /**
     * 小程序 车辆详情
     * @param carId
     * @return
     */
    @GetMapping("/getCarBaseDetail/{carId}")
    public AjaxResult getCarBaseDetail(@PathVariable Long carId) {
        CarDetailVo carBaseDetail = carListService.getCarBaseDetail(carId);
        return success(carBaseDetail);
    }

    /**
     * 小程序 根据车id获取评价列表
     * @param carId
     * @return
     */
    @GetMapping("/getCarOrderEvalution")
    public TableDataInfo getCarOrderEvalution(Long carId) {
        startPage();
        List<CarEvaluationVo> carOrderEvalution = carListService.getCarOrderEvalution(carId);
        return getDataTable(carOrderEvalution);
    }

    /**
     * 小程序 根据二级地区id获取码头列表
     * @param areaId
     * @return
     */
    @GetMapping("/getDockListByTownId/{areaId}")
    public AjaxResult getDockListByTownId(@PathVariable Long areaId) {
        return success(carListService.getDockListByTownId(areaId));
    }

    /**
     * 小程序 获取二级地区及码头列表
     * @param carListDto
     * @return
     */
    @GetMapping("/getAreaInfo")
    public AjaxResult getAreaInfo(CarListDto carListDto) {
        return success(carListService.getAreaInfo(carListDto));
    }

    /**
     * 小程序 刷新车型列表
     * @param carListDto
     * @return
     */
    @PostMapping("/refreshCarModelList")
    public AjaxResult refreshCarModelList(@RequestBody CarListDto carListDto) {
        return success(carListService.refreshCarModelList(carListDto));
    }
}
