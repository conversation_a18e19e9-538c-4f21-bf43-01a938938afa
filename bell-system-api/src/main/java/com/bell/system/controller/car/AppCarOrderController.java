package com.bell.system.controller.car;

import com.bell.car.domain.CarOrder;
import com.bell.car.domain.Dto.CarAvailabilityRequest;
import com.bell.car.domain.Dto.CarOrderDTO;
import com.bell.car.domain.VO.AppCarOrderListVO;
import com.bell.car.service.ICarOrderService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.scenic.domain.Dto.ScenicOrderDTO;
import com.bell.wx.service.WxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 租车订单Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/car/carOrder")
public class AppCarOrderController extends BaseController
{
    @Autowired
    private ICarOrderService carOrderService;

    @Autowired
    private WxService wxService;

    /**
     * 提交订单
     */
    @PostMapping("/submitCarOrder")
    public AjaxResult submitOrder(@RequestBody CarOrderDTO request)
    {
        return success(wxService.createCarOrder(request));
    }


    @PostMapping("/refundCarOrder")
    public AjaxResult refundCarOrder(@RequestBody CarOrder carOrder)
    {
        return success(wxService.refundCarOrder(carOrder));
    }

    @PostMapping("/checkCarAvailability")
    public AjaxResult checkCarAvailability(@RequestBody CarAvailabilityRequest carAvailabilityRequest)
    {
        return success(carOrderService.checkCarAvailability(carAvailabilityRequest));
    }

    /**
     * 付款按钮
     */
    @PostMapping("/updateCarOrder")
    public AjaxResult updateCarOrder(@RequestBody CarOrderDTO request)
    {
        request.setUserId(getUserId());
        return success(wxService.updateCarOrder(request));
    }


    /**
     * 查询租车订单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CarOrder carOrder)
    {
        startPage();
        List<AppCarOrderListVO> list = carOrderService.selectAppCarOrderList(carOrder);
        return getDataTable(list);
    }

    /**
     * 查询租车订单详情
     */
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(carOrderService.selectAppCarOrderByOrderId(orderId));
    }
    
    /**
     * 获取续租车辆订单详情
     */
    @GetMapping("/details/{orderId}")
    public AjaxResult getCarOrderDetails(@PathVariable("orderId") Long orderId)
    {
        return success(carOrderService.getCarOrderDetails(orderId));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody CarOrder carOrder)
    {
        return toAjax(carOrderService.updateCarOrderDetail(carOrder));
    }

    /**
     * 续订
     */
    @PostMapping("/continueCarOrder")
    public AjaxResult createContinueCarOrder(@RequestBody CarOrderDTO request)
    {
        return success(wxService.createContinueCarOrder(request));
    }

    @PostMapping("/updateCarOrderTwice")
    public AjaxResult updateCarOrderTwice(@RequestBody CarOrder carOrder)
    {
        return toAjax(carOrderService.updateCarOrderTwice(carOrder));
    }

    /**
     * 查询车是否下架
     */
    @GetMapping(value = "/selectByCarId/{carId}")
    public AjaxResult getCarInfo(@PathVariable("carId") Long carId)
    {
        return success(carOrderService.selectAppCarByCarId(carId));
    }

}
