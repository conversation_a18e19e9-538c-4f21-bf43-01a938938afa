package com.bell.system.controller.car;

import com.bell.car.domain.CarOrderEvaluation;
import com.bell.car.domain.Dto.AppCarOrderEvaluationAddDTO;
import com.bell.car.service.ICarOrderEvaluationService;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆订单评价Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/car/carOrderEvaluation")
public class AppCarOrderEvaluationController extends BaseController
{
    @Autowired
    private ICarOrderEvaluationService carOrderEvaluationService;


    /**
     * 根据订单ID查询车辆订单评价详情
     */
    @GetMapping("/detail/{orderId}")
    public AjaxResult getDetailByOrderId(@PathVariable("orderId") Long orderId)
    {
        CarOrderEvaluation evaluation = carOrderEvaluationService.getAppCarOrderEvaluationByOrderId(orderId);
        return success(evaluation);
    }

    /**
     * 新增车辆订单评价
     */
    @PostMapping
    public AjaxResult add(@RequestBody AppCarOrderEvaluationAddDTO carOrderEvaluationAddDTO)
    {
        return toAjax(carOrderEvaluationService.addAppCarOrderEvaluation(carOrderEvaluationAddDTO));
    }

}
