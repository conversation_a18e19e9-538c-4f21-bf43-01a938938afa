package com.bell.system.controller.car;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bell.car.domain.CarBaseInfo;
import com.bell.car.domain.CarOrder;
import com.bell.car.domain.Dto.*;
import com.bell.car.domain.VO.CarOrderGzhListVo;
import com.bell.car.mapper.CarBaseInfoMapper;
import com.bell.car.service.ICarOrderService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.system.domain.vo.AppSysUserMineVo;
import com.bell.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 租车订单公众号Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/app/car/gzh")
public class AppCarOrderGzhController extends BaseController
{
    @Autowired
    private ICarOrderService carOrderService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private CarBaseInfoMapper carBaseInfoMapper;

    /**
     * 公众号 订单列表
     * @param carOrderListDto 查询信息
     * @return 订单
     */
    @GetMapping("/getOrderList")
    public TableDataInfo getOrderList(CarOrderListDto carOrderListDto) {
        AppSysUserMineVo user = sysUserMapper.selectUserByGzhOpenid(carOrderListDto.getOpenId(),"05");
        List<CarBaseInfo> carBaseInfos = carBaseInfoMapper.getCarBaseInfoListByUser(Long.valueOf(user.getUserId()));
        startPage();
        List<CarOrderGzhListVo> orderList = carOrderService.getOrderList(carOrderListDto, user, carBaseInfos);
        return getDataTable(orderList);
    }

    /**
     * 公众号 押金退还
     * @param carOrderRefundDto 押金信息
     * @return 结果
     */
    @PostMapping("/refund")
    public AjaxResult refund(@RequestBody CarOrderRefundDto carOrderRefundDto)
    {
        return carOrderService.refund(carOrderRefundDto);
    }

    /**
     * 公众号 验车
     * @param carOrderCheckDto 验车信息
     * @return 结果
     */
    @PostMapping("/check")
    public AjaxResult check(@RequestBody CarOrderCheckDto carOrderCheckDto)
    {
        return carOrderService.check(carOrderCheckDto);
    }

    /**
     * 公众号 上传合同
     * @param carOrderContractDto 合同信息
     * @return 结果
     */
    @PostMapping("/contract")
    public AjaxResult contract(@RequestBody CarOrderContractDto carOrderContractDto)
    {
        return carOrderService.contract(carOrderContractDto);
    }

    /**
     * 公众号 订单详情
     * @param orderId 订单id
     * @return 详情
     */
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(carOrderService.selectGzhCarOrderByOrderId(orderId));
    }

    /**
     * 通过OCR返回车牌号
     * @param imgUrl 图片地址
     * @return 车牌号
     */
    @GetMapping("/getCarNoByOCR")
    public AjaxResult getCarNoByOCR(String imgUrl) {
        return carOrderService.getCarNoByOCR(imgUrl);
    }
}
