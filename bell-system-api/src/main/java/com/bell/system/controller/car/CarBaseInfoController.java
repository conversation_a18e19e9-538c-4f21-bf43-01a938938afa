package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.car.domain.Dto.CarBaseInfoDto;
import com.bell.car.domain.VO.CarBaseInfoVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarBaseInfo;
import com.bell.car.service.ICarBaseInfoService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 车辆信息Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carBaseInfo")
public class CarBaseInfoController extends BaseController
{
    @Autowired
    private ICarBaseInfoService carBaseInfoService;

    /**
     * 查询车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarBaseInfoDto carBaseInfoDto)
    {
        startPage();
        List<CarBaseInfoVo> list = carBaseInfoService.getCarBaseInfoList(carBaseInfoDto);
        return getDataTable(list);
    }

    /**
     * 导出车辆信息列表
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:export')")
    @Log(title = "车辆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarBaseInfo carBaseInfo)
    {
        List<CarBaseInfo> list = carBaseInfoService.selectCarBaseInfoList(carBaseInfo);
        ExcelUtil<CarBaseInfo> util = new ExcelUtil<CarBaseInfo>(CarBaseInfo.class);
        util.exportExcel(response, list, "车辆信息数据");
    }

    /**
     * 获取车辆信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:query')")
    @GetMapping(value = "/{carId}")
    public AjaxResult getInfo(@PathVariable("carId") Long carId)
    {
        return success(carBaseInfoService.getCarBaseInfoByCarId(carId));
    }

    /**
     * 新增车辆信息
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:add')")
    @Log(title = "车辆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarBaseInfoDto carBaseInfoDto)
    {
        return carBaseInfoService.addCarBaseInfo(carBaseInfoDto);
    }

    /**
     * 修改车辆信息
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:edit')")
    @Log(title = "车辆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarBaseInfoDto carBaseInfoDto)
    {
        return carBaseInfoService.editCarBaseInfo(carBaseInfoDto);
    }

    /**
     * 删除车辆信息
     */
    @PreAuthorize("@ss.hasPermi('car:carBaseInfo:remove')")
    @Log(title = "车辆信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{carIds}")
    public AjaxResult remove(@PathVariable List<Long> carIds)
    {
        return toAjax(carBaseInfoService.deleteCarBaseInfoByCarId(carIds.get(0)));
    }

    /**
     * 后台 车型列表
     * @return
     */
    @GetMapping("/getCarModelList")
    public AjaxResult getCarModelList() {
        return AjaxResult.success(carBaseInfoService.getCarModelList());
    }

    /**
     * 后台 车企列表
     * @return
     */
    @GetMapping("/getCarCompanyList")
    public AjaxResult getCarCompanyList() {
        return AjaxResult.success(carBaseInfoService.getCarCompanyList());
    }

    /**
     * 后台 获取地区二级列表
     * @return
     */
    @GetMapping("/getAreaTowLevelList")
    public AjaxResult getAreaTowLevelList() {
        return AjaxResult.success(carBaseInfoService.getAreaTowLevelList());
    }

    /**
     * 后台 获取码头列表
     * @return
     */
    @GetMapping("/getDockList/{townId}")
    public AjaxResult getDockList(@PathVariable Long townId) {
        return AjaxResult.success(carBaseInfoService.getDockList(townId));
    }

    /**
     * 后台 获取车企人员列表
     * @return
     */
    @GetMapping("/getCarCompanyUserList/{companyId}")
    public AjaxResult getCarCompanyUserList(@PathVariable Long companyId) {
        return AjaxResult.success(carBaseInfoService.getCarCompanyUserList(companyId));
    }

    /**
     * 后台 获取车辆配置列表
     * @return
     */
    @GetMapping("/getCarFacilityList")
    public AjaxResult getCarFacilityList() {
        return AjaxResult.success(carBaseInfoService.getCarFacilityList());
    }

    /**
     * 修改车辆状态
     * @param carBaseInfoDto
     * @return
     */
    @PostMapping("/updateCarStateByCarId")
    public AjaxResult updateCarStateByCarId(@RequestBody CarBaseInfoDto carBaseInfoDto) {
        return toAjax(carBaseInfoService.updateCarStateByCarId(carBaseInfoDto));
    }
}
