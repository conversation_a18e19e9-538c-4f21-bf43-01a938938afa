package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.car.domain.Dto.CarOrderDTO;
import com.bell.car.domain.Dto.CarOrderPayDto;
import com.bell.car.domain.VO.CarOrderExcelVo;
import com.bell.car.domain.VO.CarOrderVo;
import com.bell.car.service.ICarOrderManagerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarOrder;
import com.bell.car.service.ICarOrderService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 租车订单Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carOrder")
public class CarOrderController extends BaseController
{
    @Autowired
    private ICarOrderService carOrderService;

    @Autowired
    private ICarOrderManagerService carOrderManagerService;

    /**
     * 查询租车订单列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderDTO carOrderDTO) {
        startPage();
        List<CarOrderVo> list = carOrderManagerService.getCarOrderList(carOrderDTO);
        return getDataTable(list);
    }

    /**
     * 导出租车订单列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:export')")
    @Log(title = "租车订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderDTO carOrderDTO)
    {
        List<CarOrderExcelVo> list = carOrderManagerService.exportExcelInfo(carOrderDTO);
        ExcelUtil<CarOrderExcelVo> util = new ExcelUtil<CarOrderExcelVo>(CarOrderExcelVo.class);
        util.exportExcel(response, list, "租车订单数据");
    }

    /**
     * 获取租车订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(carOrderManagerService.getCarOrderDetail(orderId));
    }

    /**
     * 新增租车订单
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:add')")
    @Log(title = "租车订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrder carOrder)
    {
        return toAjax(carOrderService.insertCarOrder(carOrder));
    }

    /**
     * 修改租车订单
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:edit')")
    @Log(title = "租车订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrder carOrder)
    {
        return toAjax(carOrderService.updateCarOrder(carOrder));
    }

    /**
     * 删除租车订单
     */
    @PreAuthorize("@ss.hasPermi('car:carOrder:remove')")
    @Log(title = "租车订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable List<Long> orderIds)
    {
        return toAjax(carOrderService.deleteCarOrderByOrderIds(orderIds));
    }

    /**
     * 查询续租订单列表
     * @param orderId
     * @return
     */
    @GetMapping("/getTenancyTermList/{orderId}")
    public AjaxResult getTenancyTermList(@PathVariable Long orderId) {
        return success(carOrderManagerService.getTenancyTermList(orderId));
    }

    /**
     * 校验是否存在未退款续租单
     * @param orderId
     * @return
     */
    @GetMapping("/checkTenancyTermList/{orderId}")
    public AjaxResult checkTenancyTermList(@PathVariable Long orderId) {
        return success(carOrderManagerService.checkTenancyTermList(orderId));
    }

    /**
     * 退款按钮及已打款按钮数据回显
     * @param orderNo
     * @return
     */
    @GetMapping("/getCarOrderInfo/{orderNo}")
    public AjaxResult getCarOrderInfo(@PathVariable String orderNo) {
        return success(carOrderManagerService.getCarOrderInfo(orderNo));
    }

    /**
     * 退款
     * @param carOrderPayDto
     * @return
     */
    @PostMapping("/refundCarOrder")
    public AjaxResult refundCarOrder(@RequestBody CarOrderPayDto carOrderPayDto) {
        return carOrderManagerService.refundCarOrder(carOrderPayDto);
    }

    /**
     * 已打款
     * @param carOrderPayDto
     * @return
     */
    @PostMapping("/refundPay")
    public AjaxResult refundPay(@RequestBody CarOrderPayDto carOrderPayDto) {
        return carOrderManagerService.refundPay(carOrderPayDto);
    }
}
