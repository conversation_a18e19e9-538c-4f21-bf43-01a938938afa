package com.bell.system.controller.car;

import com.bell.car.domain.CarOrderEvaluation;
import com.bell.car.domain.Dto.CarOrderEvaluationDto;
import com.bell.car.domain.VO.CarOrderEvaluationVo;
import com.bell.car.service.ICarOrderEvaluationService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车辆订单评价Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carOrderEvaluation")
public class CarOrderEvaluationController extends BaseController
{
    @Autowired
    private ICarOrderEvaluationService carOrderEvaluationService;

    /**
     * 查询车辆订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderEvaluationDto carOrderEvaluationDto)
    {
        startPage();
        List<CarOrderEvaluationVo> list = carOrderEvaluationService.selectCarOrderEvaluationList(carOrderEvaluationDto);
        return getDataTable(list);
    }

    /**
     * 导出车辆订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:export')")
    @Log(title = "车辆订单评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderEvaluationDto carOrderEvaluationDto)
    {
        List<CarOrderEvaluationVo> list = carOrderEvaluationService.selectCarOrderEvaluationList(carOrderEvaluationDto);
        ExcelUtil<CarOrderEvaluationVo> util = new ExcelUtil<CarOrderEvaluationVo>(CarOrderEvaluationVo.class);
        util.exportExcel(response, list, "车辆订单评价数据");
    }

    /**
     * 获取车辆订单评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:query')")
    @GetMapping(value = "/{evaluationId}")
    public AjaxResult getInfo(@PathVariable("evaluationId") Long evaluationId)
    {
        return success(carOrderEvaluationService.selectCarOrderEvaluationByEvaluationId(evaluationId));
    }

    /**
     * 新增车辆订单评价
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:add')")
    @Log(title = "车辆订单评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderEvaluation carOrderEvaluation)
    {
        return toAjax(carOrderEvaluationService.insertCarOrderEvaluation(carOrderEvaluation));
    }

    /**
     * 修改车辆订单评价
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:edit')")
    @Log(title = "车辆订单评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderEvaluation carOrderEvaluation)
    {
        return toAjax(carOrderEvaluationService.updateCarOrderEvaluation(carOrderEvaluation));
    }

    /**
     * 删除车辆订单评价
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluation:remove')")
    @Log(title = "车辆订单评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{evaluationIds}")
    public AjaxResult remove(@PathVariable List<Long> evaluationIds)
    {
        return toAjax(carOrderEvaluationService.deleteCarOrderEvaluationByEvaluationIds(evaluationIds));
    }

    /**
     * 获取车辆订单评价列表的码头列表
     */
    @GetMapping("/getCarBellSeaArea")
    public AjaxResult getCarBellSeaArea() {
        return success(carOrderEvaluationService.getCarBellSeaArea());
    }
}
