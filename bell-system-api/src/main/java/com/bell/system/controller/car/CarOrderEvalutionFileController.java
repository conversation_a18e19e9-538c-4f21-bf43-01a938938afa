package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarOrderEvalutionFile;
import com.bell.car.service.ICarOrderEvalutionFileService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 评价图片Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carOrderEvaluationfile")
public class CarOrderEvalutionFileController extends BaseController
{
    @Autowired
    private ICarOrderEvalutionFileService carOrderEvalutionFileService;

    /**
     * 查询评价图片列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderEvalutionFile carOrderEvalutionFile)
    {
        startPage();
        List<CarOrderEvalutionFile> list = carOrderEvalutionFileService.selectCarOrderEvalutionFileList(carOrderEvalutionFile);
        return getDataTable(list);
    }

    /**
     * 导出评价图片列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:export')")
    @Log(title = "评价图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderEvalutionFile carOrderEvalutionFile)
    {
        List<CarOrderEvalutionFile> list = carOrderEvalutionFileService.selectCarOrderEvalutionFileList(carOrderEvalutionFile);
        ExcelUtil<CarOrderEvalutionFile> util = new ExcelUtil<CarOrderEvalutionFile>(CarOrderEvalutionFile.class);
        util.exportExcel(response, list, "评价图片数据");
    }

    /**
     * 获取评价图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:query')")
    @GetMapping(value = "/{evaluationFileId}")
    public AjaxResult getInfo(@PathVariable("evaluationFileId") Long evaluationFileId)
    {
        return success(carOrderEvalutionFileService.selectCarOrderEvalutionFileByEvaluationFileId(evaluationFileId));
    }

    /**
     * 新增评价图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:add')")
    @Log(title = "评价图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderEvalutionFile carOrderEvalutionFile)
    {
        return toAjax(carOrderEvalutionFileService.insertCarOrderEvalutionFile(carOrderEvalutionFile));
    }

    /**
     * 修改评价图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:edit')")
    @Log(title = "评价图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderEvalutionFile carOrderEvalutionFile)
    {
        return toAjax(carOrderEvalutionFileService.updateCarOrderEvalutionFile(carOrderEvalutionFile));
    }

    /**
     * 删除评价图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderEvaluationfile:remove')")
    @Log(title = "评价图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{evaluationFileIds}")
    public AjaxResult remove(@PathVariable List<Long> evaluationFileIds)
    {
        return toAjax(carOrderEvalutionFileService.deleteCarOrderEvalutionFileByEvaluationFileIds(evaluationFileIds));
    }
}
