package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarOrderImage;
import com.bell.car.service.ICarOrderImageService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 车辆订单图片Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carOrderImage")
public class CarOrderImageController extends BaseController
{
    @Autowired
    private ICarOrderImageService carOrderImageService;

    /**
     * 查询车辆订单图片列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderImage carOrderImage)
    {
        startPage();
        List<CarOrderImage> list = carOrderImageService.selectCarOrderImageList(carOrderImage);
        return getDataTable(list);
    }

    /**
     * 导出车辆订单图片列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:export')")
    @Log(title = "车辆订单图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderImage carOrderImage)
    {
        List<CarOrderImage> list = carOrderImageService.selectCarOrderImageList(carOrderImage);
        ExcelUtil<CarOrderImage> util = new ExcelUtil<CarOrderImage>(CarOrderImage.class);
        util.exportExcel(response, list, "车辆订单图片数据");
    }

    /**
     * 获取车辆订单图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(carOrderImageService.selectCarOrderImageByImageId(imageId));
    }

    /**
     * 新增车辆订单图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:add')")
    @Log(title = "车辆订单图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderImage carOrderImage)
    {
        return toAjax(carOrderImageService.insertCarOrderImage(carOrderImage));
    }

    /**
     * 修改车辆订单图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:edit')")
    @Log(title = "车辆订单图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderImage carOrderImage)
    {
        return toAjax(carOrderImageService.updateCarOrderImage(carOrderImage));
    }

    /**
     * 删除车辆订单图片
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderImage:remove')")
    @Log(title = "车辆订单图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable List<Long> imageIds)
    {
        return toAjax(carOrderImageService.deleteCarOrderImageByImageIds(imageIds));
    }
}
