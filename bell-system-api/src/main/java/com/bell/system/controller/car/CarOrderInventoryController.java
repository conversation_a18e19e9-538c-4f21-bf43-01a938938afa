package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarOrderInventory;
import com.bell.car.service.ICarOrderInventoryService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 租车订单库存Controller
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
@RestController
@RequestMapping("/inventory/inventory")
public class CarOrderInventoryController extends BaseController
{
    @Autowired
    private ICarOrderInventoryService carOrderInventoryService;

    /**
     * 查询租车订单库存列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderInventory carOrderInventory)
    {
        startPage();
        List<CarOrderInventory> list = carOrderInventoryService.selectCarOrderInventoryList(carOrderInventory);
        return getDataTable(list);
    }

    /**
     * 导出租车订单库存列表
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:export')")
    @Log(title = "租车订单库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderInventory carOrderInventory)
    {
        List<CarOrderInventory> list = carOrderInventoryService.selectCarOrderInventoryList(carOrderInventory);
        ExcelUtil<CarOrderInventory> util = new ExcelUtil<CarOrderInventory>(CarOrderInventory.class);
        util.exportExcel(response, list, "租车订单库存数据");
    }

    /**
     * 获取租车订单库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:query')")
    @GetMapping(value = "/{inventoryId}")
    public AjaxResult getInfo(@PathVariable("inventoryId") Long inventoryId)
    {
        return success(carOrderInventoryService.selectCarOrderInventoryByInventoryId(inventoryId));
    }

    /**
     * 新增租车订单库存
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:add')")
    @Log(title = "租车订单库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderInventory carOrderInventory)
    {
        return toAjax(carOrderInventoryService.insertCarOrderInventory(carOrderInventory));
    }

    /**
     * 修改租车订单库存
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:edit')")
    @Log(title = "租车订单库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderInventory carOrderInventory)
    {
        return toAjax(carOrderInventoryService.updateCarOrderInventory(carOrderInventory));
    }

    /**
     * 删除租车订单库存
     */
    @PreAuthorize("@ss.hasPermi('inventory:inventory:remove')")
    @Log(title = "租车订单库存", businessType = BusinessType.DELETE)
	@DeleteMapping("/{inventoryIds}")
    public AjaxResult remove(@PathVariable List<Long> inventoryIds)
    {
        return toAjax(carOrderInventoryService.deleteCarOrderInventoryByInventoryIds(inventoryIds));
    }
}
