package com.bell.system.controller.car;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.car.domain.CarOrderTenancyTerm;
import com.bell.car.service.ICarOrderTenancyTermService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 订单租期Controller
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/car/carOrderTerm")
public class CarOrderTenancyTermController extends BaseController
{
    @Autowired
    private ICarOrderTenancyTermService carOrderTenancyTermService;

    /**
     * 查询订单租期列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarOrderTenancyTerm carOrderTenancyTerm)
    {
        startPage();
        List<CarOrderTenancyTerm> list = carOrderTenancyTermService.selectCarOrderTenancyTermList(carOrderTenancyTerm);
        return getDataTable(list);
    }

    /**
     * 导出订单租期列表
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:export')")
    @Log(title = "订单租期", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarOrderTenancyTerm carOrderTenancyTerm)
    {
        List<CarOrderTenancyTerm> list = carOrderTenancyTermService.selectCarOrderTenancyTermList(carOrderTenancyTerm);
        ExcelUtil<CarOrderTenancyTerm> util = new ExcelUtil<CarOrderTenancyTerm>(CarOrderTenancyTerm.class);
        util.exportExcel(response, list, "订单租期数据");
    }

    /**
     * 获取订单租期详细信息
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:query')")
    @GetMapping(value = "/{termId}")
    public AjaxResult getInfo(@PathVariable("termId") Long termId)
    {
        return success(carOrderTenancyTermService.selectCarOrderTenancyTermByTermId(termId));
    }

    /**
     * 新增订单租期
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:add')")
    @Log(title = "订单租期", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CarOrderTenancyTerm carOrderTenancyTerm)
    {
        return toAjax(carOrderTenancyTermService.insertCarOrderTenancyTerm(carOrderTenancyTerm));
    }

    /**
     * 修改订单租期
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:edit')")
    @Log(title = "订单租期", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CarOrderTenancyTerm carOrderTenancyTerm)
    {
        return toAjax(carOrderTenancyTermService.updateCarOrderTenancyTerm(carOrderTenancyTerm));
    }

    /**
     * 删除订单租期
     */
    @PreAuthorize("@ss.hasPermi('car:carOrderTerm:remove')")
    @Log(title = "订单租期", businessType = BusinessType.DELETE)
	@DeleteMapping("/{termIds}")
    public AjaxResult remove(@PathVariable List<Long> termIds)
    {
        return toAjax(carOrderTenancyTermService.deleteCarOrderTenancyTermByTermIds(termIds));
    }
}
