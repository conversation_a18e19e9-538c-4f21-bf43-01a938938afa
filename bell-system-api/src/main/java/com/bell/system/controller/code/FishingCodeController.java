package com.bell.system.controller.code;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.code.domain.DTO.FishingCodeDto;
import com.bell.code.domain.DTO.FishingCodeEditDto;
import com.bell.code.domain.VO.FishingCodeListVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.code.domain.FishingCode;
import com.bell.code.service.IFishingCodeService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 尾标码Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/code/code")
public class FishingCodeController extends BaseController {

    @Autowired
    private IFishingCodeService fishingCodeService;

    /**
     * 查询尾标码列表
     */
    @PreAuthorize("@ss.hasPermi('code:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(FishingCodeDto fishingCodeDto) {
        startPage();
        List<FishingCodeListVo> list = fishingCodeService.selectFishingCodeInfoList(fishingCodeDto);
        return getDataTable(list);
    }

    /**
     * 导出尾标码列表
     */
    @PreAuthorize("@ss.hasPermi('code:code:export')")
    @Log(title = "尾标码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FishingCodeDto fishingCodeDto) {
        List<FishingCodeListVo> list = fishingCodeService.selectFishingCodeInfoList(fishingCodeDto);
        ExcelUtil<FishingCodeListVo> util = new ExcelUtil<FishingCodeListVo>(FishingCodeListVo.class);
        util.exportExcel(response, list, "尾标码数据");
    }

    /**
     * 获取尾标码详细信息
     */
    @PreAuthorize("@ss.hasPermi('code:code:query')")
    @GetMapping(value = "/{codeId}")
    public AjaxResult getInfo(@PathVariable("codeId") Long codeId)
    {
        return success(fishingCodeService.selectFishingCodeByCodeId(codeId));
    }

    /**
     * 创建尾标码
     */
    @PreAuthorize("@ss.hasPermi('code:code:add')")
    @Log(title = "尾标码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FishingCodeEditDto fishingCodeEditDto) {
        return toAjax(fishingCodeService.insertFishingCode(fishingCodeEditDto));
    }

    /**
     * 绑定船长
     */
    @PreAuthorize("@ss.hasPermi('code:code:edit')")
    @Log(title = "尾标码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FishingCodeEditDto fishingCodeEditDto) {
        return toAjax(fishingCodeService.updateFishingCode(fishingCodeEditDto));
    }

    /**
     * 删除尾标码
     */
    @PreAuthorize("@ss.hasPermi('code:code:remove')")
    @Log(title = "尾标码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codeIds}")
    public AjaxResult remove(@PathVariable List<Long> codeIds) {
        return toAjax(fishingCodeService.deleteFishingCodeByCodeIds(codeIds));
    }

    /**
     * 批量绑定船长
     * @param fishingCodeEditDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('code:code:edit')")
    @Log(title = "尾标码", businessType = BusinessType.UPDATE)
    @PostMapping("/editBatch")
    public AjaxResult editBatch(@RequestBody FishingCodeEditDto fishingCodeEditDto) {
        return toAjax(fishingCodeService.editBatch(fishingCodeEditDto));
    }
}
