package com.bell.system.controller.cus;

import com.bell.activity.domain.ActivityInfo;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.cus.domain.CaptainAuth;
import com.bell.cus.domain.DTO.GzhReportDto;
import com.bell.cus.domain.vo.GzhReportVo;
import com.bell.cus.service.ICaptainAuthService;
import com.bell.ship.domain.dto.AppShipOrderDto;
import com.bell.ship.domain.dto.ShipOrderOutDockReportDto;
import com.bell.ship.domain.vo.ShipOrderVo;
import com.bell.system.domain.vo.GzhUserVo;
import com.bell.system.mapper.SysUserMapper;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 船长认证Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppCaptainAuthController extends BaseController {
    @Autowired
    private ICaptainAuthService captainAuthService;
    @Autowired
    private SysUserMapper sysUserMapper;


    /**
     * 小程序新增船长认证
     */
    @PostMapping("/appSaveCaptionAuth")
    public AjaxResult add(@RequestBody CaptainAuth captainAuth) {
        return captainAuthService.insertAppCaptainAuth(captainAuth);
    }

    /**
     * 小程序修改船长认证
     */
    @PutMapping("/appUpdateCaptionAuth")
    public AjaxResult edit(@RequestBody CaptainAuth captainAuth) {
        return toAjax(captainAuthService.appUpdateCaptainAuth(captainAuth));
    }

    /**
     * 小程序获取船长认证详细信息
     */
    @GetMapping(value = "/appDetailCaptionAuth")
    public AjaxResult getInfo() {
        return success(captainAuthService.appDetailCaptionAuth());
    }


    /**
     * 小程序船长首页的报备记录列表
     *
     * @param shipOrderOutDockReportDto 船长id
     * @return 报备记录的集合
     */
    @PostMapping("/appReportRecordList")
    public TableDataInfo appReportRecordList(@RequestBody ShipOrderOutDockReportDto shipOrderOutDockReportDto) {
        PageHelper.startPage(shipOrderOutDockReportDto.getPageNum(), shipOrderOutDockReportDto.getPageSize());
        return getDataTable(captainAuthService.appReportRecordList(shipOrderOutDockReportDto));

    }

    /**
     * 小程序船长首页  出港报备记录详情
     *
     * @param reportId 订单id
     * @return 出港报备记录详情
     */
    @GetMapping("/appOrderReportRecord/{reportId}")
    public AjaxResult appOrderReportRecord(@PathVariable("reportId") Long reportId) {
        return success(captainAuthService.appOrderReportRecord(reportId));
    }

    /**
     * 小程序船长首页的出港登记记录列表
     *
     * @return 出港登记记录的列表
     */
    @GetMapping("/departureRegistrationRecordList")
    public TableDataInfo departureRegistrationRecordList(AppShipOrderDto appShipOrderDto) {
        startPage();
        return getDataTable(captainAuthService.departureRegistrationRecordList());

    }

    /**
     * 小程序船长首页  当前订单的出港登记记录详情
     *
     * @param orderId 订单id
     * @return 出港登记记录
     */
    @GetMapping("/appOutDepartureRecord/{orderId}")
    public AjaxResult appOutDepartureRecord(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.appOutDepartureRecord(orderId));
    }

    /**
     * 小程序船长首页的预约记录列表
     *
     * @return 预约记录的列表
     */
    @GetMapping("/appointmentRecordList")
    public TableDataInfo appointmentRecordList(AppShipOrderDto appShipOrderDto) {
        startPage();
        return getDataTable(captainAuthService.appointmentRecordList(appShipOrderDto));
    }

    /**
     * 小程序船长首页  取消订单
     *
     * @param shipOrderVo 取消订单的原因
     * @return 结果
     */
    @PutMapping("/captainOrderCancelled")
    public AjaxResult captainOrderCancelled(@RequestBody ShipOrderVo shipOrderVo) {
        return captainAuthService.captainOrderCancelled(shipOrderVo);
    }

    /**
     * 小程序船长首页  确认订单
     *
     * @param orderId 订单id
     * @return 结果
     */
    @GetMapping("/confirmAnOrder/{orderId}")
    public AjaxResult confirmAnOrder(@PathVariable("orderId") Long orderId) {
        return toAjax(captainAuthService.confirmAnOrder(orderId));
    }

    /**
     * 小程序船长首页  预约记录详情的  订单详细信息
     *
     * @param orderId 订单id
     * @return 订单信息
     */
    @GetMapping("/reservationOrderInfo/{orderId}")
    public AjaxResult reservationOrderInfo(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.reservationOrderInfo(orderId));
    }

    /**
     * 小程序船长首页 本平台订单  预约记录详情的乘客人数，以及身份信息列表
     *
     * @param orderId 订单id等信息
     * @return 客人信息列表
     */
    @GetMapping("/getShipOrderMemberList/{orderId}")
    private AjaxResult getShipOrderMemberList(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.getShipOrderMemberList(orderId));
    }

    /**
     * 小程序船长首页 非平台订单报备详情页的乘客人数，以及身份信息列表
     *
     * @param reportId 订单id等信息
     * @return 客人信息列表
     */
    @GetMapping("/getReportOrderMemberList/{reportId}")
    private AjaxResult getReportOrderMemberList(@PathVariable("reportId") Long reportId) {
        return success(captainAuthService.getReportOrderMemberList(reportId));
    }

    /**
     * 小程序船长首页 当前订单的乘客离船记录
     *
     * @param orderId 订单id
     * @return 离船记录
     */
    @GetMapping("/disembarkRecord/{orderId}")
    public AjaxResult disembarkRecordList(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.disembarkRecordList(orderId));
    }

    /**
     * 小程序船长首页  乘客评价记录
     *
     * @param orderId 订单id
     * @return 评价记录
     */
    @GetMapping("/passengerEvaluationRecord/{orderId}")
    public AjaxResult passengerEvaluationRecord(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.passengerEvaluationRecord(orderId));
    }

    /**
     * 乘客出港，离船登记的船只详情
     *
     * @param orderId 订单id
     * @return 详情
     */
    @GetMapping("/appInAndOffInfo/{orderId}")
    public AjaxResult appInAndOffInfo(@PathVariable("orderId") Long orderId) {
        return success(captainAuthService.appInAndOffInfo(orderId));
    }

    /**
     * 船长认证获取实名信息
     *
     * @return 认证信息
     */
    @GetMapping("/captainNameAuthInfo")
    public AjaxResult getCaptainNameAuthInfo() {
        return success(captainAuthService.getCaptainNameAuthInfo());
    }

    /**
     * 根据订单id查询当前订单最新一条的出港报备状态
     *
     * @param shipOrderId 订单id
     * @return 出港报备状态
     */
    @GetMapping("/selectNowReportStateByOrderId/{shipOrderId}")
    public AjaxResult selectNowReportStateByOrderId(@PathVariable("shipOrderId") Long shipOrderId) {
        return captainAuthService.selectNowReportStateByOrderId(shipOrderId);
    }

    /**
     * 公众号查询报备列表
     *
     * @param gzhReportVo 查询参数
     * @return 报备列表
     */
    @PostMapping("/getGzhReportList")
    private TableDataInfo getGzhReportList(@RequestBody GzhReportVo gzhReportVo) {
        GzhUserVo gzhVo = sysUserMapper.selectGzhUserDetailByOpenId(gzhReportVo.getOpenId(),"02");
        gzhReportVo.setUserId(gzhVo.getUserId());
        startPage();
        List<GzhReportDto> list = captainAuthService.getGzhReportList(gzhReportVo);
        return getDataTable(list);
    }

}
