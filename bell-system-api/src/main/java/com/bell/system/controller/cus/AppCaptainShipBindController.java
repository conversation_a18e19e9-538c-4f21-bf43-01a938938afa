package com.bell.system.controller.cus;

import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.cus.domain.CaptainShipBind;
import com.bell.cus.service.ICaptainShipBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 船只绑定申请Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppCaptainShipBindController extends BaseController {
    @Autowired
    private ICaptainShipBindService captainShipBindService;

    /**
     * 小程序新增船只绑定申请
     */
    @PostMapping("/appSaveShipBind")
    public AjaxResult add(@RequestBody CaptainShipBind captainShipBind) {
        return captainShipBindService.appInsertCaptainShipBind(captainShipBind);
    }

    /**
     * 小程序获取船只绑定申请详细信息
     */
    @PostMapping(value = "/appDetail")
    public AjaxResult getInfo(@RequestBody CaptainShipBind captainShipBind) {
        return success(captainShipBindService.captainShipBindDetailByBindId(captainShipBind));
    }

    /**
     * 修改船只绑定申请
     */
    @PutMapping("/appUpdateShipBind")
    public AjaxResult edit(@RequestBody CaptainShipBind captainShipBind) {
        return captainShipBindService.updateCaptainShipBind(captainShipBind);
    }


}
