package com.bell.system.controller.cus;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.cus.domain.CaptainAuthAudit;
import com.bell.cus.service.ICaptainAuthAuditService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 船长认证审核记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/cus/captionAudit")
public class CaptainAuthAuditController extends BaseController
{
    @Autowired
    private ICaptainAuthAuditService captainAuthAuditService;

    /**
     * 查询船长认证审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaptainAuthAudit captainAuthAudit)
    {
        startPage();
        List<CaptainAuthAudit> list = captainAuthAuditService.selectCaptainAuthAuditList(captainAuthAudit);
        return getDataTable(list);
    }

    /**
     * 导出船长认证审核记录列表
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:export')")
    @Log(title = "船长认证审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaptainAuthAudit captainAuthAudit)
    {
        List<CaptainAuthAudit> list = captainAuthAuditService.selectCaptainAuthAuditList(captainAuthAudit);
        ExcelUtil<CaptainAuthAudit> util = new ExcelUtil<CaptainAuthAudit>(CaptainAuthAudit.class);
        util.exportExcel(response, list, "船长认证审核记录数据");
    }

    /**
     * 获取船长认证审核记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:query')")
    @GetMapping(value = "/{auditId}")
    public AjaxResult getInfo(@PathVariable("auditId") Long auditId)
    {
        return success(captainAuthAuditService.selectCaptainAuthAuditByAuditId(auditId));
    }

    /**
     * 新增船长认证审核记录
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:add')")
    @Log(title = "船长认证审核记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaptainAuthAudit captainAuthAudit)
    {
        return toAjax(captainAuthAuditService.insertCaptainAuthAudit(captainAuthAudit));
    }

    /**
     * 修改船长认证审核记录
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:edit')")
    @Log(title = "船长认证审核记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CaptainAuthAudit captainAuthAudit)
    {
        return toAjax(captainAuthAuditService.updateCaptainAuthAudit(captainAuthAudit));
    }

    /**
     * 删除船长认证审核记录
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAudit:remove')")
    @Log(title = "船长认证审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{auditIds}")
    public AjaxResult remove(@PathVariable List<Long> auditIds)
    {
        return toAjax(captainAuthAuditService.deleteCaptainAuthAuditByAuditIds(auditIds));
    }
}
