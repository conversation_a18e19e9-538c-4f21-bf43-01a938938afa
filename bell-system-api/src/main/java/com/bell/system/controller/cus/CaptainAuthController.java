package com.bell.system.controller.cus;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.cus.domain.DTO.CaptainAuthDto;
import com.bell.cus.domain.vo.CaptainAuthVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.cus.domain.CaptainAuth;
import com.bell.cus.service.ICaptainAuthService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 船长认证Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/cus/captionAuth")
public class CaptainAuthController extends BaseController
{
    @Autowired
    private ICaptainAuthService captainAuthService;

    /**
     * 查询船长认证列表
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaptainAuthDto captainAuthDto) {
        startPage();
        List<CaptainAuthVo> list = captainAuthService.getCaptainAuthList(captainAuthDto);
        return getDataTable(list);
    }

    /**
     * 导出船长认证列表
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:export')")
    @Log(title = "船长认证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaptainAuth captainAuth)
    {
        List<CaptainAuth> list = captainAuthService.selectCaptainAuthList(captainAuth);
        ExcelUtil<CaptainAuth> util = new ExcelUtil<CaptainAuth>(CaptainAuth.class);
        util.exportExcel(response, list, "船长认证数据");
    }

    /**
     * 获取船长认证详细信息
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:query')")
    @GetMapping(value = "/{authId}")
    public AjaxResult getInfo(@PathVariable("authId") Long authId)
    {
        return success(captainAuthService.getCaptainAuthDetail(authId));
    }

    /**
     * 新增船长认证
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:add')")
    @Log(title = "船长认证", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaptainAuth captainAuth)
    {
        return toAjax(captainAuthService.insertCaptainAuth(captainAuth));
    }

    /**
     * 修改船长认证
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:edit')")
    @Log(title = "船长认证", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody CaptainAuth captainAuth)
    {
        return toAjax(captainAuthService.updateCaptionAuthState(captainAuth));
    }

    /**
     * 删除船长认证
     */
    @PreAuthorize("@ss.hasPermi('cus:captionAuth:remove')")
    @Log(title = "船长认证", businessType = BusinessType.DELETE)
	@DeleteMapping("/{authIds}")
    public AjaxResult remove(@PathVariable List<Long> authIds)
    {
        return toAjax(captainAuthService.deleteCaptainAuthByAuthIds(authIds));
    }
}
