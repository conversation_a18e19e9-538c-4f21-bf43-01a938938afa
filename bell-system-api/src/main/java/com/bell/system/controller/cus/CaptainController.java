package com.bell.system.controller.cus;

import com.bell.basic.domain.BellShip;
import com.bell.basic.service.IBellShipService;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.cus.service.ICaptainService;
import com.bell.ship.domain.dto.AppShipOrderDto;
import com.bell.system.domain.DTO.SysUserDto;
import com.bell.system.domain.vo.SysUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 船长认证Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/cus")
public class Captain<PERSON><PERSON>roller extends BaseController {
    @Autowired
    private ICaptainService captainService;
    @Autowired
    private IBellShipService bellShipService;

    /**
     * 查询船长认证列表
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:list')")
    @GetMapping("/captionList")
    public TableDataInfo list(SysUserDto sysUserDto) {
        startPage();
        List<SysUserVo> list = captainService.selectCaptainList(sysUserDto);
        return getDataTable(list);
    }

    /**
     * 导出船长认证列表
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:export')")
    @Log(title = "船长认证", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUserDto sysUser) {
        List<SysUserVo> list = captainService.selectCaptainList(sysUser);
        ExcelUtil<SysUserVo> util = new ExcelUtil<SysUserVo>(SysUserVo.class);
        util.exportExcel(response, list, "船长数据");
    }

    /**
     * 获取船长详细信息
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:query')")
    @GetMapping(value = "/captionDetail/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId) {
        return success(captainService.selectCaptainById(userId));
    }

    /**
     * 新增船长
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:add')")
    @Log(title = "船长认证", businessType = BusinessType.INSERT)
    @PostMapping("/addCaption")
    public AjaxResult add(@RequestBody SysUserDto sysUserDto) {
        return captainService.insertCaptain(sysUserDto);
    }

    /**
     * 修改船长
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:edit')")
    @Log(title = "船长认证", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCaption")
    public AjaxResult edit(@RequestBody SysUserDto sysUserDto) {
        return toAjax(captainService.updateCaptain(sysUserDto));
    }

    /**
     * 删除船长
     * @param userId 用户id
     * @return 结果
     */
    @PreAuthorize("@ss.hasPermi('cus:caption:remove')")
    @Log(title = "船长认证", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteCaption/{userId}")
    public AjaxResult remove(@PathVariable("userId") Long userId) {
        return toAjax(captainService.deleteCaptainById(userId));
    }


    /**
     * 查询当前海域未绑定的船只列表
     * areaId  区域id
     *
     * @return 船只列表
     */
    @GetMapping("/shipList/{areaId}")
    public AjaxResult getShipList(@PathVariable("areaId") Long areaId) {
        List<BellShip> list = bellShipService.getShipList(areaId);
        return success(list);
    }

    /**
     * 查询所有没绑定的船只列表
     *
     * @return 船只列表
     */
    @GetMapping("/getUnboundShipList")
    public TableDataInfo getUnboundShipList() {
        startPage();
        List<BellShip> list = bellShipService.getUnboundShipList();
        return getDataTable(list);
    }

    /**
     * 查询所有没绑定的船只列表+已绑定的船只列表
     *
     * @return 船只列表
     */
    @GetMapping("/getCurrentShipList")
    public TableDataInfo getCurrentShipList(AppShipOrderDto appShipOrderDto) {
        startPage();
        // 未绑定的船只列表
        List<BellShip> list = bellShipService.getUnboundShipList();
        // 查询船东已绑定的船只列表
        List<BellShip> currentList = bellShipService.getCurrentShipList(Long.valueOf(appShipOrderDto.getOwnerId()));
        // 先复制未绑定列表
        List<BellShip> combinedList = new ArrayList<>(list);
        // 再添加已绑定列表
        combinedList.addAll(currentList);
        // 合并两个list
        return getDataTable(combinedList);
    }

}
