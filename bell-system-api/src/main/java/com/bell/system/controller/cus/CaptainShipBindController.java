package com.bell.system.controller.cus;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.cus.domain.CaptainShipBind;
import com.bell.cus.domain.DTO.CaptainShipBindDto;
import com.bell.cus.domain.vo.CaptainShipBindVo;
import com.bell.cus.service.ICaptainShipBindService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 船只绑定申请Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/cus/shipBind")
public class CaptainShipBindController extends BaseController
{
    @Autowired
    private ICaptainShipBindService captainShipBindService;

    /**
     * 查询船只绑定申请列表
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaptainShipBindDto captainShipBindDto)
    {
        startPage();
        List<CaptainShipBindVo> list = captainShipBindService.selectCaptainShipBindList(captainShipBindDto);
        return getDataTable(list);
    }

    /**
     * 导出船只绑定申请列表
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:export')")
    @Log(title = "船只绑定申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaptainShipBindDto captainShipBindDto)
    {
        List<CaptainShipBindVo> list = captainShipBindService.selectCaptainShipBindList(captainShipBindDto);
        ExcelUtil<CaptainShipBindVo> util = new ExcelUtil<CaptainShipBindVo>(CaptainShipBindVo.class);
        util.exportExcel(response, list, "船只绑定申请数据");
    }

    /**
     * 获取船只绑定申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:query')")
    @GetMapping(value = "/{bindId}")
    public AjaxResult getInfo(@PathVariable("bindId") Long bindId)
    {
        return success(captainShipBindService.selectCaptainShipBindByBindId(bindId));
    }

    /**
     * 新增船只绑定申请
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:add')")
    @Log(title = "船只绑定申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaptainShipBind captainShipBind)
    {
        return toAjax(captainShipBindService.insertCaptainShipBind(captainShipBind));
    }

    /**
     * 审核船艇绑定申请
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:edit')")
    @Log(title = "船只绑定申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CaptainShipBind captainShipBind)
    {
        return captainShipBindService.updateCaptainShipBind(captainShipBind);
    }

    /**
     * 删除船只绑定申请
     */
    @PreAuthorize("@ss.hasPermi('cus:shipBind:remove')")
    @Log(title = "船只绑定申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bindIds}")
    public AjaxResult remove(@PathVariable List<Long> bindIds)
    {
        return toAjax(captainShipBindService.deleteCaptainShipBindByBindIds(bindIds));
    }

}
