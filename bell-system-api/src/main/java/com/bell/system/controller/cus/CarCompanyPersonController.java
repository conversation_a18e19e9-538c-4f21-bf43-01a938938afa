package com.bell.system.controller.cus;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.cus.domain.DTO.CarCompanyPersonDto;
import com.bell.cus.domain.vo.CarCompanyPersonVo;
import com.bell.cus.service.ICarCompanyPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 车企工作人员管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/cus/carCompanyPerson")
public class CarCompanyPersonController extends BaseController {

    @Autowired
    private ICarCompanyPersonService carCompanyPersonService;

    /**
     * 获取车企工作人员列表
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:list')")
    @GetMapping("/getCarCompanyPersonList")
    public TableDataInfo list(CarCompanyPersonDto carCompanyPersonDto) {
        startPage();
        List<CarCompanyPersonVo> stationedPersonList = carCompanyPersonService.getCarCompanyPersonList(carCompanyPersonDto);
        return getDataTable(stationedPersonList);
    }

    /**
     * 导出车企工作人员列表
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:export')")
    @Log(title = "车企人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CarCompanyPersonDto carCompanyPersonDto) {
        List<CarCompanyPersonVo> list = carCompanyPersonService.getCarCompanyPersonList(carCompanyPersonDto);
        ExcelUtil<CarCompanyPersonVo> util = new ExcelUtil<CarCompanyPersonVo>(CarCompanyPersonVo.class);
        util.exportExcel(response, list, "车企工作人员数据");
    }

    /**
     * 修改车企工作人员状态
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:edit')")
    @PostMapping("/editState")
    public AjaxResult editState(@RequestBody CarCompanyPersonDto carCompanyPersonDto) {
        return toAjax(carCompanyPersonService.editState(carCompanyPersonDto));
    }

    /**
     * 根据id查询车企人员信息
     */
    @GetMapping("/getCarCompanyPerson")
    public AjaxResult getCarCompanyPerson(Long userId) {
        return success(carCompanyPersonService.getCarCompanyPerson(userId));
    }

    /**
     * 新增车企人员
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:add')")
    @PostMapping("/addCarCompanyPerson")
    public AjaxResult addCarCompanyPerson(@RequestBody CarCompanyPersonDto carCompanyPersonDto) {
        return carCompanyPersonService.addCarCompanyPerson(carCompanyPersonDto);
    }

    /**
     * 编辑车企人员信息
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:edit')")
    @PostMapping("/editCarCompanyPerson")
    public AjaxResult editScenicPerson(@RequestBody CarCompanyPersonDto carCompanyPersonDto) {
        return toAjax(carCompanyPersonService.editScenicPerson(carCompanyPersonDto));
    }

    /**
     * 删除车企人员信息
     */

    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:remove')")
    @GetMapping("/deleteCarCompanyPerson")
    public AjaxResult deleteCarCompanyPerson(CarCompanyPersonDto carCompanyPersonDto) {
        return toAjax(carCompanyPersonService.deleteCarCompanyPerson(carCompanyPersonDto));
    }

    /**
     * 修改密码
     */
    @PreAuthorize("@ss.hasPermi('cus:carCompanyPerson:edit')")
    @PostMapping("/updatePassword")
    public AjaxResult updatePassword(@RequestBody CarCompanyPersonDto carCompanyPersonDto) {
        return toAjax(carCompanyPersonService.updatePassword(carCompanyPersonDto));
    }


    /**
     * 获取当前用户是不是车企
     *
     * @return 结果
     */
    @GetMapping("/getUserIsCarCompany")
    public AjaxResult getUserIsCarCompany() {
        return success(carCompanyPersonService.getUserIsCarCompany());
    }
}
