package com.bell.system.controller.cus;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.cus.domain.DTO.ScenicPersonDto;
import com.bell.cus.domain.vo.ScenicPersonVo;
import com.bell.cus.service.IScenicPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 驻港人员管理
 */
@RestController
@RequestMapping("/cus/scenicPerson")
public class ScenicPersonController extends BaseController {

    @Autowired
    private IScenicPersonService scenicPersonService;

    /**
     * 获取景区工作人员列表
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:list')")
    @GetMapping("/getScenicPersonList")
    public TableDataInfo list(ScenicPersonDto scenicPersonDto) {
        startPage();
        List<ScenicPersonVo> stationedPersonList = scenicPersonService.getScenicPersonList(scenicPersonDto);
        return getDataTable(stationedPersonList);
    }

    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:export')")
    @Log(title = "景区人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScenicPersonDto scenicPersonDto) {
        List<ScenicPersonVo> list = scenicPersonService.getScenicPersonList(scenicPersonDto);
        ExcelUtil<ScenicPersonVo> util = new ExcelUtil<ScenicPersonVo>(ScenicPersonVo.class);
        util.exportExcel(response, list, "景区工作人员数据");
    }

    /**
     * 修改景区工作人员状态
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:edit')")
    @PostMapping("/editState")
    public AjaxResult editState(@RequestBody ScenicPersonDto scenicPersonDto) {
        return toAjax(scenicPersonService.editState(scenicPersonDto));
    }

    /**
     * 根据id查询景区人员信息
     * @param userId
     * @return
     */
    @GetMapping("/getScenicPerson")
    public AjaxResult getScenicPerson(Long userId) {
        return success(scenicPersonService.getScenicPerson(userId));
    }

    /**
     * 新增景区人员
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:add')")
    @PostMapping("/addScenicPerson")
    public AjaxResult addScenicPerson(@RequestBody ScenicPersonDto scenicPersonDto) {
        return toAjax(scenicPersonService.addScenicPerson(scenicPersonDto));
    }

    /**
     * 编辑景区人员信息
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:edit')")
    @PostMapping("/editScenicPerson")
    public AjaxResult editScenicPerson(@RequestBody ScenicPersonDto scenicPersonDto) {
        return toAjax(scenicPersonService.editScenicPerson(scenicPersonDto));
    }

    /**
     * 删除景区人员信息
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:remove')")
    @GetMapping("/deleteScenicPerson")
    public AjaxResult deleteScenicPerson(ScenicPersonDto scenicPersonDto) {
        return toAjax(scenicPersonService.deleteScenicPerson(scenicPersonDto));
    }

    /**
     * 修改密码
     * @param scenicPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:scenicPerson:edit')")
    @PostMapping("/updatePassword")
    public AjaxResult updatePassword(@RequestBody ScenicPersonDto scenicPersonDto) {
        return toAjax(scenicPersonService.updatePassword(scenicPersonDto));
    }
}
