package com.bell.system.controller.cus;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.cus.domain.CaptainShipBind;
import com.bell.cus.domain.DTO.StationedPersonDto;
import com.bell.cus.domain.vo.StationedPersonVo;
import com.bell.cus.service.IStationedPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 驻港人员管理
 */
@RestController
@RequestMapping("/cus/stationedPerson")
public class StationedPersonController extends BaseController {

    @Autowired
    private IStationedPersonService stationedPersonService;

    /**
     * 获取驻港人员列表
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:list')")
    @GetMapping("/getStationedPersonList")
    public TableDataInfo list(StationedPersonDto stationedPersonDto) {
        startPage();
        List<StationedPersonVo> stationedPersonList = stationedPersonService.getStationedPersonList(stationedPersonDto);
        return getDataTable(stationedPersonList);
    }

    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:export')")
    @Log(title = "驻港人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StationedPersonDto stationedPersonDto) {
        List<StationedPersonVo> list = stationedPersonService.getStationedPersonList(stationedPersonDto);
        ExcelUtil<StationedPersonVo> util = new ExcelUtil<StationedPersonVo>(StationedPersonVo.class);
        util.exportExcel(response, list, "驻港人员数据");
    }

    /**
     * 修改驻港人员状态
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:edit')")
    @PostMapping("/editState")
    public AjaxResult editState(@RequestBody StationedPersonDto stationedPersonDto) {
        return toAjax(stationedPersonService.editState(stationedPersonDto));
    }

    /**
     * 根据id查询驻港人员信息
     * @param userId
     * @return
     */
    @GetMapping("/getStationedPerson")
    public AjaxResult getStationedPerson(Long userId) {
        return success(stationedPersonService.getStationedPerson(userId));
    }

    /**
     * 新增港务人员
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:add')")
    @PostMapping("/addStationedPerson")
    public AjaxResult addStationedPerson(@RequestBody StationedPersonDto stationedPersonDto) {
        return stationedPersonService.addStationedPerson(stationedPersonDto);
    }

    /**
     * 编辑驻港人员信息
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:edit')")
    @PostMapping("/editStationedPerson")
    public AjaxResult editStationedPerson(@RequestBody StationedPersonDto stationedPersonDto) {
        return stationedPersonService.editStationedPerson(stationedPersonDto);
    }

    /**
     * 删除驻港人员信息
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:remove')")
    @GetMapping("/deleteStationedPerson")
    public AjaxResult deleteStationedPerson(StationedPersonDto stationedPersonDto) {
        return toAjax(stationedPersonService.deleteStationedPerson(stationedPersonDto));
    }

    /**
     * 修改密码
     * @param stationedPersonDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('cus:stationedPerson:edit')")
    @PostMapping("/updatePassword")
    public AjaxResult updatePassword(@RequestBody StationedPersonDto stationedPersonDto) {
        return toAjax(stationedPersonService.updatePassword(stationedPersonDto));
    }
}
