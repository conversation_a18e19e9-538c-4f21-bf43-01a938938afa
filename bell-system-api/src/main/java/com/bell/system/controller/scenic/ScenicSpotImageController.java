package com.bell.system.controller.scenic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.scenic.domain.ScenicSpotImage;
import com.bell.scenic.service.IScenicSpotImageService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 景区图片Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenic/image")
public class ScenicSpotImageController extends BaseController
{
    @Autowired
    private IScenicSpotImageService scenicSpotImageService;

    /**
     * 查询景区图片列表
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScenicSpotImage scenicSpotImage)
    {
        startPage();
        List<ScenicSpotImage> list = scenicSpotImageService.selectScenicSpotImageList(scenicSpotImage);
        return getDataTable(list);
    }

    /**
     * 导出景区图片列表
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:export')")
    @Log(title = "景区图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScenicSpotImage scenicSpotImage)
    {
        List<ScenicSpotImage> list = scenicSpotImageService.selectScenicSpotImageList(scenicSpotImage);
        ExcelUtil<ScenicSpotImage> util = new ExcelUtil<ScenicSpotImage>(ScenicSpotImage.class);
        util.exportExcel(response, list, "景区图片数据");
    }

    /**
     * 获取景区图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:query')")
    @GetMapping(value = "/{imageId}")
    public AjaxResult getInfo(@PathVariable("imageId") Long imageId)
    {
        return success(scenicSpotImageService.selectScenicSpotImageByImageId(imageId));
    }

    /**
     * 新增景区图片
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:add')")
    @Log(title = "景区图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScenicSpotImage scenicSpotImage)
    {
        return toAjax(scenicSpotImageService.insertScenicSpotImage(scenicSpotImage));
    }

    /**
     * 修改景区图片
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:edit')")
    @Log(title = "景区图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScenicSpotImage scenicSpotImage)
    {
        return toAjax(scenicSpotImageService.updateScenicSpotImage(scenicSpotImage));
    }

    /**
     * 删除景区图片
     */
    @PreAuthorize("@ss.hasPermi('scenic:image:remove')")
    @Log(title = "景区图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{imageIds}")
    public AjaxResult remove(@PathVariable List<Long> imageIds)
    {
        return toAjax(scenicSpotImageService.deleteScenicSpotImageByImageIds(imageIds));
    }
}
