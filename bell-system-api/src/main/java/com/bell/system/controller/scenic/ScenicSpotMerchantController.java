package com.bell.system.controller.scenic;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.scenic.domain.ScenicSpotMerchant;
import com.bell.scenic.service.IScenicSpotMerchantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 景区商户Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenic/merchant")
public class ScenicSpotMerchantController extends BaseController
{
    @Autowired
    private IScenicSpotMerchantService scenicSpotMerchantService;

    /**
     * 查询景区商户列表
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScenicSpotMerchant scenicSpotMerchant)
    {
        startPage();
        List<ScenicSpotMerchant> list = scenicSpotMerchantService.selectScenicSpotMerchantList(scenicSpotMerchant);
        return getDataTable(list);
    }

    /**
     * 导出景区商户列表
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:export')")
    @Log(title = "景区商户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScenicSpotMerchant scenicSpotMerchant)
    {
        List<ScenicSpotMerchant> list = scenicSpotMerchantService.selectScenicSpotMerchantList(scenicSpotMerchant);
        ExcelUtil<ScenicSpotMerchant> util = new ExcelUtil<ScenicSpotMerchant>(ScenicSpotMerchant.class);
        util.exportExcel(response, list, "景区商户数据");
    }

    /**
     * 获取景区商户详细信息
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:query')")
    @GetMapping(value = "/{merchantId}")
    public AjaxResult getInfo(@PathVariable("merchantId") Long merchantId)
    {
        return success(scenicSpotMerchantService.selectScenicSpotMerchantByMerchantId(merchantId));
    }

    /**
     * 新增景区商户
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:add')")
    @Log(title = "景区商户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScenicSpotMerchant scenicSpotMerchant)
    {
        return scenicSpotMerchantService.insertScenicSpotMerchant(scenicSpotMerchant);
    }

    /**
     * 修改景区商户
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:edit')")
    @Log(title = "景区商户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScenicSpotMerchant scenicSpotMerchant)
    {
        return scenicSpotMerchantService.updateScenicSpotMerchant(scenicSpotMerchant);
    }

    /**
     * 删除景区商户
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:remove')")
    @Log(title = "景区商户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{merchantIds}")
    public AjaxResult remove(@PathVariable List<Long> merchantIds)
    {
        return scenicSpotMerchantService.deleteScenicSpotMerchantByMerchantIds(merchantIds);
    }

    /**
     * 景区修改结算比例
     *
     * @param scenicSpotMerchant 数据
     * @return 修改的结果
     */
    @PreAuthorize("@ss.hasPermi('scenic:merchant:edit')")
    @Log(title = "景区修改结算比例", businessType = BusinessType.UPDATE)
    @PostMapping("/settlement")
    public AjaxResult updateSettlement(@RequestBody ScenicSpotMerchant scenicSpotMerchant) {
        return toAjax(scenicSpotMerchantService.updateSettlement(scenicSpotMerchant));
    }
}
