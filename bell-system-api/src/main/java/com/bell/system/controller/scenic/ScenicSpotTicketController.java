package com.bell.system.controller.scenic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.scenic.domain.Dto.ScenicSpotTicketDto;
import com.bell.scenic.domain.ScenicSpotTicket;
import com.bell.scenic.domain.VO.ScenicSpotTicketListVo;
import com.bell.scenic.service.IScenicSpotTicketService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 景区门票Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenic/ticket")
public class ScenicSpotTicketController extends BaseController
{
    @Autowired
    private IScenicSpotTicketService scenicSpotTicketService;

    /**
     * 查询景区门票列表
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScenicSpotTicketDto scenicSpotTicketDto)
    {
        startPage();
        List<ScenicSpotTicketListVo> list = scenicSpotTicketService.selectScenicSpotTicketList(scenicSpotTicketDto);
        return getDataTable(list);
    }

    /**
     * 获取景区门票详细信息
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:query')")
    @GetMapping(value = "/{ticketId}")
    public AjaxResult getInfo(@PathVariable("ticketId") Long ticketId)
    {
        return success(scenicSpotTicketService.selectScenicSpotTicketByTicketId(ticketId));
    }

    /**
     * 新增景区门票
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:add')")
    @Log(title = "景区门票", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScenicSpotTicket scenicSpotTicket)
    {
        return toAjax(scenicSpotTicketService.insertScenicSpotTicket(scenicSpotTicket));
    }

    /**
     * 修改景区门票
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:edit')")
    @Log(title = "景区门票", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScenicSpotTicket scenicSpotTicket)
    {
        return toAjax(scenicSpotTicketService.updateScenicSpotTicket(scenicSpotTicket));
    }

    /**
     * 删除景区门票
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:remove')")
    @Log(title = "景区门票", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ticketIds}")
    public AjaxResult remove(@PathVariable List<Long> ticketIds)
    {
        return toAjax(scenicSpotTicketService.deleteScenicSpotTicketByTicketIds(ticketIds));
    }

    /**
     * 上架
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:edit')")
    @GetMapping(value = "/putAway/{ticketId}")
    public AjaxResult putAway(@PathVariable("ticketId") Long ticketId)
    {
        return success(scenicSpotTicketService.goOnOrOff(ticketId, 1));
    }

    /**
     * 下架
     */
    @PreAuthorize("@ss.hasPermi('scenic:ticket:edit')")
    @GetMapping(value = "/soldOut/{ticketId}")
    public AjaxResult soldOut(@PathVariable("ticketId") Long ticketId)
    {
        return success(scenicSpotTicketService.goOnOrOff(ticketId, 0));
    }
}
