package com.bell.system.controller.scenic;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.scenic.domain.ScenicSpotTicketOrderPartner;
import com.bell.scenic.service.IScenicSpotTicketOrderPartnerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 景区同行人Controller
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/scenic/partner")
public class ScenicSpotTicketOrderPartnerController extends BaseController
{
    @Autowired
    private IScenicSpotTicketOrderPartnerService scenicSpotTicketOrderPartnerService;

    /**
     * 查询景区同行人列表
     */
    @PreAuthorize("@ss.hasPermi('system:partner:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScenicSpotTicketOrderPartner scenicSpotTicketOrderPartner)
    {
        startPage();
        List<ScenicSpotTicketOrderPartner> list = scenicSpotTicketOrderPartnerService.selectScenicSpotTicketOrderPartnerList(scenicSpotTicketOrderPartner);
        return getDataTable(list);
    }

    /**
     * 导出景区同行人列表
     */
    @PreAuthorize("@ss.hasPermi('system:partner:export')")
    @Log(title = "景区同行人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScenicSpotTicketOrderPartner scenicSpotTicketOrderPartner)
    {
        List<ScenicSpotTicketOrderPartner> list = scenicSpotTicketOrderPartnerService.selectScenicSpotTicketOrderPartnerList(scenicSpotTicketOrderPartner);
        ExcelUtil<ScenicSpotTicketOrderPartner> util = new ExcelUtil<ScenicSpotTicketOrderPartner>(ScenicSpotTicketOrderPartner.class);
        util.exportExcel(response, list, "景区同行人数据");
    }

    /**
     * 获取景区同行人详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:partner:query')")
    @GetMapping(value = "/{partnerId}")
    public AjaxResult getInfo(@PathVariable("partnerId") Long partnerId)
    {
        return success(scenicSpotTicketOrderPartnerService.selectScenicSpotTicketOrderPartnerByPartnerId(partnerId));
    }

    /**
     * 新增景区同行人
     */
    @PreAuthorize("@ss.hasPermi('system:partner:add')")
    @Log(title = "景区同行人", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScenicSpotTicketOrderPartner scenicSpotTicketOrderPartner)
    {
        return success(scenicSpotTicketOrderPartnerService.insertScenicSpotTicketOrderPartner(scenicSpotTicketOrderPartner));
    }

    /**
     * 修改景区同行人
     */
    @PreAuthorize("@ss.hasPermi('system:partner:edit')")
    @Log(title = "景区同行人", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScenicSpotTicketOrderPartner scenicSpotTicketOrderPartner)
    {
        return toAjax(scenicSpotTicketOrderPartnerService.updateScenicSpotTicketOrderPartner(scenicSpotTicketOrderPartner));
    }

}
