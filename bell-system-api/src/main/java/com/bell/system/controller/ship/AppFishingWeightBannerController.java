package com.bell.system.controller.ship;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.ship.domain.FishingWeightBanner;
import com.bell.ship.domain.dto.FishingWeightBannerDto;
import com.bell.ship.domain.vo.FishingWeightBannerVo;
import com.bell.ship.service.IFishingWeightBannerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 广告管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/app/basic/weightBanner")
public class AppFishingWeightBannerController extends BaseController
{
    @Autowired
    private IFishingWeightBannerService fishingWeightBannerService;

    /**
     * 查询广告管理列表
     */
    @GetMapping("/list")
    public AjaxResult list(FishingWeightBannerDto fishingWeightBannerDto)
    {
        List<FishingWeightBannerVo> list = fishingWeightBannerService.getFishingWeightBannerList(fishingWeightBannerDto);
        List<String> imageUrls = list.stream().map(FishingWeightBannerVo::getBannerImgUrl).collect(Collectors.toList());
        return AjaxResult.success(imageUrls);
    }
}
