package com.bell.system.controller.ship;

import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.ship.domain.dto.ShipOrderDto;
import com.bell.ship.service.IAppInsuranceShipOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 2025/05/20海钓订单业务修改controller
 */
@RestController
@RequestMapping("/app")
public class AppInsuranceShipOrderController extends BaseController {

    @Autowired
    private IAppInsuranceShipOrderService appInsuranceShipOrderService;

    /**
     * 根据船舶id获取价格信息
     * @param shipOrderDto
     * @return
     */
    @GetMapping("/getBoatPriceAndInsurancePrice")
    public AjaxResult getBoatPriceAndInsurancePrice(ShipOrderDto shipOrderDto) {
        return success(appInsuranceShipOrderService.getBoatPriceAndInsurancePrice(shipOrderDto));
    }

    /**
     * 获取约船订单退单申请
     * @return
     */
    @GetMapping("/getShipOrderRefundApply")
    public AjaxResult getShipOrderRefundApply(ShipOrderDto shipOrderDto) {
        return success(appInsuranceShipOrderService.getShipOrderRefundApply(shipOrderDto));
    }

    /**
     * 计算个人原因退款金额（已接单订单取消页数据）
     * @param shipOrderDto
     * @return
     */
    @GetMapping("/getPersonalReasonRefundAmount")
    public AjaxResult getPersonalReasonRefundAmount(ShipOrderDto shipOrderDto) {
        return success(appInsuranceShipOrderService.getPersonalReasonRefundAmount(shipOrderDto));
    }

    /**
     * 获取非个人原因信息（已出港订单取消页数据）
     * @param shipOrderDto
     * @return
     */
    @GetMapping("/getUnPersonalReasonRefundInfo")
    public AjaxResult getUnPersonalReasonRefundInfo(ShipOrderDto shipOrderDto) {
        return success(appInsuranceShipOrderService.getUnPersonalReasonRefundInfo(shipOrderDto));
    }

    /**
     * 小程序 获取订单状态
     * @param shipOrderDto
     * @return
     */
    @GetMapping("/getShipOrderState")
    public AjaxResult getShipOrderState(ShipOrderDto shipOrderDto) {
        return success(appInsuranceShipOrderService.getShipOrderState(shipOrderDto));
    }
}
