package com.bell.system.controller.ship;

import com.bell.basic.domain.ShipDock;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.domain.entity.SysUser;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.utils.StringUtils;
import com.bell.ship.domain.ShipOrder;
import com.bell.ship.domain.dto.AppShipOrderDto;
import com.bell.ship.domain.dto.ShipOrderDto;
import com.bell.ship.domain.vo.AppShipOrderDetailVo;
import com.bell.ship.domain.vo.AppShipOrderVo;
import com.bell.ship.domain.vo.SettlementOrderVO;
import com.bell.ship.domain.vo.ShipOrderVo;
import com.bell.ship.service.IAppInsuranceShipOrderService;
import com.bell.ship.service.IShipOrderService;
import com.bell.system.service.ISmsService;
import com.bell.system.service.ISysConfigService;
import com.bell.wx.service.WxService;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 海钓预约订单
 */
@RestController
@RequestMapping("/app/appShipOrder")
public class AppShipOrderController extends BaseController {

    @Autowired
    private IShipOrderService shipOrderService;

    @Autowired
    private WxService wxService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISmsService iSmsService;

    @Autowired
    private IAppInsuranceShipOrderService appInsuranceShipOrderService;

    /**
     * 海钓预约订单详情
     * @param orderId
     * @return
     */
    @GetMapping("/getShipOrderDetail")
    public AjaxResult getShipOrderDetail(Long orderId) {
        AppShipOrderDetailVo shipOrderDetail = shipOrderService.getShipOrderDetail(orderId);
        return success(shipOrderDetail);
    }

    /**
     * 海钓预约订单列表
     * @param appShipOrderDto
     * @return
     */
    @GetMapping("/getShipOrderList")
    public TableDataInfo getShipOrderList(AppShipOrderDto appShipOrderDto) {
        startPage();
        appShipOrderDto.setUserId(getUserId().intValue());
        List<AppShipOrderVo> shipOrderList = shipOrderService.getShipOrderList(appShipOrderDto);
        return getDataTable(shipOrderList);
    }

    /**
     * 获取 平台出港报备+出港登记+出港码+离船码页面  需要的订单信息
     *
     * @param orderId 订单id
     * @return 订单信息
     */
    @GetMapping("/getShipOrderInfo/{orderId}")
    public AjaxResult getShipOrderInfo(@PathVariable("orderId") Long orderId) {
        return success(shipOrderService.getShipOrderInfo(orderId));
    }

    /**
     * 获取 非平台订单的船长以及船舶信息
     *
     * @return 船舶信息
     */
    @GetMapping("/getNonPlatformShipInfo")
    public AjaxResult getNonPlatformShipInfo() {
        return success(shipOrderService.getNonPlatformShipInfo());
    }

    /**
     * 海钓首页获取订单列表
     *
     * @param appShipOrderDto 筛选条件
     * @return 订单列表
     */
    @PostMapping("/getSeaFishingShipOrderList")
    public TableDataInfo getSeaFishingShipOrderList(@RequestBody AppShipOrderDto appShipOrderDto) {
        // startPage();
        PageHelper.startPage(appShipOrderDto.getPageNum(), appShipOrderDto.getPageSize());
        List<ShipOrderVo> seaFishingShipOrderList = shipOrderService.getSeaFishingShipOrderList(appShipOrderDto);
        return getDataTable(seaFishingShipOrderList);
    }

    /**
     * 小程序创建海钓约船待支付订单
     *
     * @param shipOrderDto 信息
     * @return 结果
     */
    @PostMapping("/createOrder")
    public AjaxResult createOrder(@RequestBody ShipOrderDto shipOrderDto) {
        shipOrderDto.setUserId(getUserId());
        // 生成订单
        return success(shipOrderService.createSeaFishingOrder(shipOrderDto));
    }

    /**
     * 根据订单号查询订单支付状态
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/{orderNo}")
    public AjaxResult getOrderStateByOrderNo(@PathVariable("orderNo") Long orderNo) {
        return success(shipOrderService.getOrderStateByOrderNo(orderNo));
    }

    /**
     * 根据订单id修改订单状态
     * @param orderId
     * @param state
     * @return
     */
    @GetMapping("/updateOrderStateByOrderId")
    public AjaxResult updateOrderStateByOrderId(Long orderId, String state) {
        return success(shipOrderService.updateOrderStateByOrderId(orderId, state));
    }

    /**
     * 小程序支付海钓约船待支付订单
     *
     * @param shipOrderDto 信息
     * @return 结果
     */
    @PostMapping("/updateOrder")
    public AjaxResult updateOrder(@RequestBody ShipOrderDto shipOrderDto) {
        shipOrderDto.setUserId(getUserId());
        // 待支付订单支付
        return success(shipOrderService.updateSeaFishingOrder(shipOrderDto));
    }

    /**
     * 根据订单id获取海钓订单同行人列表
     * @param shipOrderDto
     * @return
     */
    @GetMapping("/getShipOrderMemberListByOrderId")
    public AjaxResult getShipOrderMemberListByOrderId(ShipOrderDto shipOrderDto) {
        shipOrderDto.setUserId(getUserId());
        return success(shipOrderService.getShipOrderMemberListByOrderId(shipOrderDto));
    }

    /**
     * 根据成员id删除海钓订单成员
     * @param userId
     * @return
     */
    @GetMapping("/deleteShipOrderMemberByUserId")
    public AjaxResult deleteShipOrderMemberByUserId(Long userId, Long orderId) {
        return toAjax(shipOrderService.deleteShipOrderMemberByUserId(userId, orderId));
    }

    /**
     * 新增订单同行人
     * @param shipOrderDto
     * @return
     */
    @PostMapping("/addShipOrderMember")
    public AjaxResult addShipOrderMember(@RequestBody ShipOrderDto shipOrderDto) {
        return toAjax(shipOrderService.addShipOrderMember(shipOrderDto));
    }

    /**
     * 小程序 海钓订单详情数据
     * @param orderId
     * @return
     */
    @GetMapping("/appGetShipOrderDetail")
    public AjaxResult appGetShipOrderDetail(Long orderId) {
        return success(shipOrderService.appGetShipOrderDetail(orderId));
    }

    /**
     * 根据船舶id获取船舶对应船长可以接单的船舶数量
     * @param shipId
     * @return
     */
    @GetMapping("/getShipCountByShipId")
    public AjaxResult getShipCountByShipId(Long shipId) {
        return success(shipOrderService.getShipCountByShipId(shipId));
    }

    /**
     * 根据订单编号查询订单支付状态
     * @param orderNo
     * @return
     */
    @GetMapping("/getOrderInfoByOrderNo")
    public AjaxResult getOrderInfoByOrderNo(String orderNo) {
        return success(shipOrderService.getOrderInfoByOrderNo(orderNo));
    }

    /**
     * 小程序 修改已支付订单
     * @return
     */
    @PostMapping("/editShipOrder")
    public AjaxResult editShipOrder(@RequestBody ShipOrderDto shipOrderDto) {
        return success(shipOrderService.editShipOrder(shipOrderDto));
    }

    /**
     * 取消订单并退款
     * @param shipOrderDto
     * @return
     */
    @PostMapping("/updateOrderAndRefund")
    public AjaxResult updateOrderAndRefund(@RequestBody ShipOrderDto shipOrderDto) {
        String shipOrderState = appInsuranceShipOrderService.getShipOrderState(shipOrderDto);
        if (!shipOrderDto.getState().equals(shipOrderState)) {
            return AjaxResult.error(408,"订单状态已变更，请刷新页面");
        }
        // 小程序 待接单状态取消订单
        if ("paid".equals(shipOrderDto.getState())) {
            return appInsuranceShipOrderService.updatePaidOrderRefund(shipOrderDto);
        }
        //小程序 已接单状态取消订单
        if ("accepted".equals(shipOrderDto.getState())) {
            //如果订单编号没有传过来，就反查一下，保险
            if (shipOrderDto.getOrderNo() == null) {
                ShipOrder orderInfoByOrderId = shipOrderService.getOrderInfoByOrderId(shipOrderDto.getOrderId());
                shipOrderDto.setOrderNo(orderInfoByOrderId.getOrderNo());
            }
            return appInsuranceShipOrderService.updateAcceptedOrderRefund(shipOrderDto);
        }
        //小程序 已出港状态订单取消订单
        if ("out_dock".equals(shipOrderDto.getState())) {
            return appInsuranceShipOrderService.updateOutDockOrderRefund(shipOrderDto);
        }

//        try {
//            BigDecimal fen = new BigDecimal(100);
//            shipOrder.setRefundAmount(fen.multiply(shipOrder.getRefundAmount()));
//        } catch (ArithmeticException e) {
//            throw new RuntimeException("金额计算错误");
//        }
//        shipOrder.setUserId(getUserId());
//        int refund = wxService.refundShipOrderByCustomer(shipOrder);
//        shipOrderService.updateOrderAndRefund(shipOrder);
        return error("当前订单状态为"+ shipOrderDto.getState() + "退款失败");
    }

    /**
     * 根据订单id获取订单退款状态
     * @param orderId
     * @return
     */
    @GetMapping("/getOrderInfoByOrderId")
    public AjaxResult getOrderInfoByOrderId(Long orderId) {
        return success(shipOrderService.getOrderInfoByOrderId(orderId));
    }

    /**
     * 当订单船舰成功或订单修改成功，把订单信息存入redis
     * @param orderId
     * @return
     */
    @GetMapping("/editOrderRedisInfo")
    public AjaxResult editOrderRedisInfo(Long orderId) {
        return success(shipOrderService.editOrderRedisInfo(orderId));
    }

    /**
     * 根据出港码头获取订单价格
     * @param shipDock
     * @return
     */
    @PostMapping("/getPriceByDockId")
    public AjaxResult getPriceByDockId(@RequestBody ShipDock shipDock) {
        return success(shipOrderService.getPriceByDockId(shipDock));
    }

    /**
     * 获取当前船长有没有没完成的订单List
     *
     * @return 订单List
     */
    @GetMapping("/incompleteOrder")
    public AjaxResult incompleteOrder() {
        return success(shipOrderService.incompleteOrder(getUserId()));
    }

    /**
     * 小程序 退款计算扣款后的退款金额
     * @param refundAmount
     * @param refundProportion
     * @return
     */
    @GetMapping("/getRefundAmount")
    public AjaxResult getRefundAmount(BigDecimal refundAmount, BigDecimal refundProportion) {
        BigDecimal divisor = new BigDecimal("100");
        BigDecimal proportion = refundProportion.divide(divisor, 10, RoundingMode.HALF_UP); // 20/100 = 0.2
        BigDecimal multiplier = BigDecimal.ONE.subtract(proportion); // 1 - 0.2 = 0.8
        BigDecimal result = refundAmount.multiply(multiplier); // 100 * 0.8 = 80
        return success(result);
    }

    /**
     * 获取当前是否有赛季
     * @return
     */
    @GetMapping("/getFishingCompetitionSeasonDate")
    public AjaxResult getFishingCompetitionSeasonDate() {
        return success(shipOrderService.getFishingCompetitionSeasonDate());
    }

    /**
     * 获取退款比例
     * @param configId
     * @return
     */
    @GetMapping(value = "/getSysConfig")
    public AjaxResult getInfo(Long configId)
    {
        return success(configService.selectConfigById(configId));
    }

    @GetMapping("/{orderId}/{type}")
    public AjaxResult text(@PathVariable("orderId") Long orderId, @PathVariable("type") Integer type) {
        shipOrderService.text(orderId, type);
        return success();
    }

    /**
     * 小程序 根据船只id获取船长的状态信息
     * @param shipId
     * @return
     */
    @GetMapping("/getCaptainStateByShipId/{shipId}")
    public AjaxResult getCaptainStateByShipId(@PathVariable("shipId") Long shipId) {
        return success(shipOrderService.getCaptainStateByShipId(shipId));
    }

    /**
     * 小程序 根据订单id获取船长的状态信息
     * @param orderId
     * @return
     */
    @GetMapping("/getCaptainStateByOrderId/{orderId}")
    public AjaxResult getCaptainStateByOrderId(@PathVariable("orderId") Long orderId) {
        return success(shipOrderService.getCaptainStateByOrderId(orderId));
    }

    /**
     * 小程序 船长结算列表查询
     * @return
     */
    @GetMapping("/selectSettlementOrders")
    public TableDataInfo selectSettlementOrders() {
        startPage();
        List<SettlementOrderVO> list = shipOrderService.selectSettlementOrders();
        return getDataTable(list);
    }

    /**
     * 校验同行人身份是否合法
     * @param sysUser
     * @return
     */
    @PostMapping("/checkPanter")
    public AjaxResult checkPanter(@RequestBody SysUser sysUser) {
        return shipOrderService.checkPanter(sysUser);
    }
}
