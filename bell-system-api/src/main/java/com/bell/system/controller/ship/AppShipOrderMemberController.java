package com.bell.system.controller.ship;

import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.ship.domain.dto.ShipOrderMemberDto;
import com.bell.ship.domain.vo.ShipOrderMemberVo;
import com.bell.ship.service.IShipOrderMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 订单成员Controller
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app")
public class AppShipOrderMemberController extends BaseController {
    @Autowired
    private IShipOrderMemberService shipOrderMemberService;

    /**
     * 出港登记页面——添加乘客
     *
     * @return 结果
     */
    @PostMapping("/appAddPassenger")
    public AjaxResult addPassenger(@RequestBody ShipOrderMemberDto shipOrderMemberDto) {
        return toAjax(shipOrderMemberService.addPassenger(shipOrderMemberDto));
    }

    /**
     * 出港登记页面——删除乘客
     *
     * @return 结果
     */
    @DeleteMapping("/deletePassenger")
    public AjaxResult deletePassenger(@RequestBody ShipOrderMemberDto shipOrderMemberDto) {
        return toAjax(shipOrderMemberService.deletePassenger(shipOrderMemberDto));
    }

    /**
     * 出港登记页面——查询乘客是否满足被添加的条件
     *
     * @return 结果
     */
    @PostMapping("/appSelectPassenger")
    public AjaxResult appSelectPassenger(@RequestBody ShipOrderMemberDto shipOrderMemberDto) {
        return shipOrderMemberService.appSelectPassenger(shipOrderMemberDto);
    }

    /**
     * 出港登记页面——提交出港登记
     *
     * @param shipOrderMemberDto 出港登记信息
     * @return 结果
     */
    @PostMapping("/appOutboundRegistration")
    public AjaxResult outboundRegistration(@RequestBody ShipOrderMemberDto shipOrderMemberDto) {
        return toAjax(shipOrderMemberService.outboundRegistration(shipOrderMemberDto));
    }

    /**
     * 乘客出港登记页面——查询用户免责协议
     *
     * @return 信息
     */
    @GetMapping("/article/{articleId}")
    public AjaxResult getArticle(@PathVariable("articleId") Long articleId) {
        return success(shipOrderMemberService.getArticle(articleId));

    }

    /**
     * 乘客提交出港登记
     *
     * @param shipOrderMemberVo 信息
     * @return 结果
     */
    @PostMapping("/passengerDepartureRegistration")
    public AjaxResult passengerDepartureRegistration(@RequestBody ShipOrderMemberVo shipOrderMemberVo) {
        return toAjax(shipOrderMemberService.updatePassengerDepartureRegistration(shipOrderMemberVo));
    }

    /**
     * 乘客提交离船登记
     *
     * @param shipOrderMemberVo 信息
     * @return 结果
     */
    @PostMapping("/passengerDisembarkationRegistration")
    public AjaxResult passengerDisembarkationRegistration(@RequestBody ShipOrderMemberVo shipOrderMemberVo) {
        return toAjax(shipOrderMemberService.updatePassengerDisembarkationRegistration(shipOrderMemberVo));
    }

    /**
     * 根据订单号获取订单详细信息，如预约人等
     *
     * @param orderId 订单id
     * @return 预约人
     */
    @GetMapping("/getOrderInfo/{orderId}")
    public AjaxResult getOrderInfo(@PathVariable("orderId") Long orderId) {
        return success(shipOrderMemberService.getOrderInfo(orderId));
    }

    /**
     * 获取乘客未离船登记的list
     *
     * @param orderId 订单id
     * @return 未出港登记的list
     */
    @GetMapping("/getNotInFlagList/{orderId}")
    public AjaxResult getNotInFlagList(@PathVariable("orderId") Long orderId) {
        return success(shipOrderMemberService.getNotInFlagList(orderId));
    }

    /**
     * 获取乘客未出港登记的list
     *
     * @param orderId 订单id
     * @return 未出港登记的list
     */
    @GetMapping("/getNotOutFlagList/{orderId}")
    public AjaxResult getNotOutFlagList(@PathVariable("orderId") Long orderId) {
        return success(shipOrderMemberService.getNotOutFlagList(orderId));
    }

    /**
     * 根据登陆人的id和订单号，判断这个人是不是，订单成员
     *
     * @param orderId 订单id
     * @return 判断这个人是不是，订单成员
     */
    @GetMapping("/getIsOrderMember/{orderId}")
    public AjaxResult getIsOrderMember(@PathVariable("orderId") Long orderId) {
        return success(shipOrderMemberService.getIsOrderMember(orderId));
    }


}
