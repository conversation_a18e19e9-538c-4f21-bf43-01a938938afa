package com.bell.system.controller.ship;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.competition.domain.DTO.AppCompetitionFishAllIdDto;
import com.bell.ship.domain.UserFishingWeight;
import com.bell.ship.domain.vo.AppUserFishingWeightVo;
import com.bell.ship.service.IUserFishingWeightService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 称重记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/app/fishingWeight")
public class AppUserFishingWeightController extends BaseController
{
    @Autowired
    private IUserFishingWeightService userFishingWeightService;

    /**
     * 查询称重记录列表
     */
    @GetMapping("/head")
    public AjaxResult head(AppCompetitionFishAllIdDto appCompetitionFishAllIdDto)
    {
        return success(userFishingWeightService.selectAppUserFishingWeightHead(appCompetitionFishAllIdDto));
    }


    /**
     * 新增称重记录
     */
    @Log(title = "称重记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserFishingWeight userFishingWeight)
    {
        return userFishingWeightService.insertAppUserFishingWeight(userFishingWeight);
    }
    /**
     * 查询称重记录详情
     */
    @GetMapping("/list/detail/{weightId}")
    public AjaxResult listDetail(@PathVariable("weightId") Long weightId)
    {
        return success(userFishingWeightService.selectAppUserFishingWeightListDetail(weightId));
    }

    /**
     * 查询称重记录列表
     */
    @GetMapping("/list")
    public AjaxResult list(UserFishingWeight userFishingWeight)
    {
        return success(userFishingWeightService.selectAppUserFishingWeightList(userFishingWeight));
    }
}
