package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.ship.domain.FishingWeightBanner;
import com.bell.ship.domain.dto.FishingWeightBannerDto;
import com.bell.ship.domain.vo.FishingWeightBannerVo;
import com.bell.ship.service.IFishingWeightBannerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 广告管理Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@RequestMapping("/basic/weightBanner")
public class FishingWeightBannerController extends BaseController
{
    @Autowired
    private IFishingWeightBannerService fishingWeightBannerService;

    /**
     * 查询广告管理列表
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:list')")
    @GetMapping("/list")
    public TableDataInfo list(FishingWeightBannerDto fishingWeightBannerDto)
    {
        startPage();
        List<FishingWeightBannerVo> list = fishingWeightBannerService.getFishingWeightBannerList(fishingWeightBannerDto);
        return getDataTable(list);
    }

    /**
     * 导出广告管理列表
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:export')")
    @Log(title = "广告管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FishingWeightBanner fishingWeightBanner)
    {
        List<FishingWeightBanner> list = fishingWeightBannerService.selectFishingWeightBannerList(fishingWeightBanner);
        ExcelUtil<FishingWeightBanner> util = new ExcelUtil<FishingWeightBanner>(FishingWeightBanner.class);
        util.exportExcel(response, list, "广告管理数据");
    }

    /**
     * 获取广告管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:query')")
    @GetMapping(value = "/{bannerId}")
    public AjaxResult getInfo(@PathVariable("bannerId") Long bannerId)
    {
        return success(fishingWeightBannerService.getFishingWeightBannerByBannerId(bannerId));
    }

    /**
     * 新增广告管理
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:add')")
    @Log(title = "广告管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FishingWeightBannerDto fishingWeightBannerDto)
    {
        return fishingWeightBannerService.addFishingWeightBanner(fishingWeightBannerDto);
    }

    /**
     * 修改广告管理
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:edit')")
    @Log(title = "广告管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FishingWeightBannerDto fishingWeightBannerDto)
    {
        return fishingWeightBannerService.editFishingWeightBanner(fishingWeightBannerDto);
    }

    /**
     * 删除广告管理
     */
    @PreAuthorize("@ss.hasPermi('basic:weightBanner:remove')")
    @Log(title = "广告管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bannerId}")
    public AjaxResult remove(@PathVariable Long bannerId)
    {
        return toAjax(fishingWeightBannerService.deleteFishingWeightBanner(bannerId));
    }

    /**
     * 获取设备列表
     * @return
     */
    @GetMapping("/getScaleList")
    public AjaxResult getScaleList() {
        return success(fishingWeightBannerService.getScaleList());
    }
}
