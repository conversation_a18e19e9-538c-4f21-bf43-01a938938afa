package com.bell.system.controller.ship;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.StringUtils;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.ship.domain.ShipOrder;
import com.bell.ship.domain.ShipOrderRefundApply;
import com.bell.ship.domain.dto.ShipOrderDto;
import com.bell.ship.domain.vo.ShipOrderListVo;
import com.bell.ship.service.IShipOrderService;
import com.bell.wx.service.WxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 租船订单Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/ship/shipOrder")
public class ShipOrderController extends BaseController
{
    @Autowired
    private IShipOrderService shipOrderService;

    @Autowired
    private WxService wxService;

    /**
     * 查询租船订单列表
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipOrderDto shipOrderDto) {
        startPage();
        List<ShipOrderListVo> list = shipOrderService.getManagerShipOrderList(shipOrderDto);
        return getDataTable(list);
    }

    /**
     * 导出租船订单列表
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:export')")
    @Log(title = "租船订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipOrderDto shipOrderDto) {
        List<ShipOrderListVo> list = shipOrderService.getManagerShipOrderList(shipOrderDto);
        ExcelUtil<ShipOrderListVo> util = new ExcelUtil<ShipOrderListVo>(ShipOrderListVo.class);
        util.exportExcel(response, list, "租船订单数据");
    }

    /**
     * 获取租船订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:query')")
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return success(shipOrderService.selectShipOrderInfoByOrderId(orderId));
    }

    /**
     * 新增租船订单
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:add')")
    @Log(title = "租船订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOrder shipOrder)
    {
        return toAjax(shipOrderService.insertShipOrder(shipOrder));
    }

    /**
     * 修改租船订单
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:edit')")
    @Log(title = "租船订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipOrder shipOrder)
    {
        return toAjax(shipOrderService.updateShipOrder(shipOrder));
    }

    /**
     * 删除租船订单
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:remove')")
    @Log(title = "租船订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable List<Long> orderIds)
    {
        return toAjax(shipOrderService.deleteShipOrderByOrderIds(orderIds));
    }

    /**
     * 退款
     * @param shipOrderDto
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:edit')")
    @PostMapping("/shipOrderRefund")
    public AjaxResult shipOrderRefund(@RequestBody ShipOrderDto shipOrderDto) {
        if (shipOrderDto.getOrderNo() == null) {
            return AjaxResult.error("订单编号不能为空");
        }
        if (shipOrderDto.getRefundAmount() == null) {
            return AjaxResult.error("退款金额不能为空");
        }
        if (StringUtils.isEmpty(shipOrderDto.getRefundReason())) {
            return AjaxResult.error("退款理由不能为空");
        }
        //元转换为分
        BigDecimal fen = new BigDecimal(100);
        ShipOrder shipOrder = new ShipOrder();
        shipOrder.setRefundAmount(fen.multiply(shipOrderDto.getRefundAmount()));
        shipOrder.setOrderNo(shipOrderDto.getOrderNo());
        shipOrder.setRefundReason(shipOrderDto.getRefundReason());
        shipOrder.setOrderId(shipOrderDto.getOrderId());
        shipOrder.setRefundBoatPrice(shipOrderDto.getRefundBoatPrice());
        shipOrder.setRefundInsurancePrice(shipOrderDto.getRefundInsurancePrice());
        shipOrder.setRefundType(shipOrderDto.getRefundType());
        shipOrder.setRefundUserId(getUserId());
        try {
            wxService.refundShipOrderByManager(shipOrder);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return success();
    }

    /**
     * 已打款
     * @return
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrder:edit')")
    @PostMapping("/shipOrderPay")
    public AjaxResult shipOrderPay(@RequestBody ShipOrderDto shipOrderDto) {
        shipOrderService.updateShipOrderRefundState(shipOrderDto);
        return success();
    }

    /**
     * 获取当前船长有没有没完成的订单List
     *
     * @return 订单List
     */
    @GetMapping("/incompleteOrder/{userId}")
    public AjaxResult incompleteOrder(@PathVariable("userId") Long userId) {
        return success(shipOrderService.incompleteOrder(userId));
    }

    /**
     * 根据订单id查询约船订单退单申请
     * @param orderId
     * @return
     */
    @GetMapping("/getShipOrderRefundApplyList")
    public TableDataInfo getShipOrderRefundApplyList(Long orderId) {
        startPage();
        List<ShipOrderRefundApply> shipOrderRefundApplyList = shipOrderService.getShipOrderRefundApplyList(orderId);
        return getDataTable(shipOrderRefundApplyList);
    }
}
