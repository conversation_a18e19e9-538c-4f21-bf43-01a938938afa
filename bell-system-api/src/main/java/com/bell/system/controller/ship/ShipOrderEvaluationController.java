package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.ship.domain.dto.ShipOrderEvaluationListDto;
import com.bell.ship.domain.vo.ShipOrderEvaluationListVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.ship.domain.ShipOrderEvaluation;
import com.bell.ship.service.IShipOrderEvaluationService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 船艇订单评价Controller
 * 
 * <AUTHOR>
 * @date 2025-03-15
 */
@RestController
@RequestMapping("/ship/evaluation")
public class ShipOrderEvaluationController extends BaseController
{
    @Autowired
    private IShipOrderEvaluationService shipOrderEvaluationService;

    /**
     * 查询船艇订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipOrderEvaluationListDto shipOrderEvaluationListDto)
    {
        startPage();
        List<ShipOrderEvaluationListVo> list = shipOrderEvaluationService.selectShipOrderEvaluationList(shipOrderEvaluationListDto);
        return getDataTable(list);
    }

    /**
     * 导出船艇订单评价列表
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:export')")
    @Log(title = "船艇订单评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipOrderEvaluationListDto shipOrderEvaluationListDto)
    {
        List<ShipOrderEvaluationListVo> list = shipOrderEvaluationService.selectShipOrderEvaluationList(shipOrderEvaluationListDto);
        ExcelUtil<ShipOrderEvaluationListVo> util = new ExcelUtil<ShipOrderEvaluationListVo>(ShipOrderEvaluationListVo.class);
        util.exportExcel(response, list, "船艇订单评价数据");
    }

    /**
     * 获取船艇订单评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:query')")
    @GetMapping(value = "/{evaluationId}")
    public AjaxResult getInfo(@PathVariable("evaluationId") Long evaluationId)
    {
        return success(shipOrderEvaluationService.selectShipOrderEvaluationByEvaluationId(evaluationId));
    }

    /**
     * 新增船艇订单评价
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:add')")
    @Log(title = "船艇订单评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOrderEvaluation shipOrderEvaluation)
    {
        return toAjax(shipOrderEvaluationService.insertShipOrderEvaluation(shipOrderEvaluation));
    }

    /**
     * 修改船艇订单评价
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:edit')")
    @Log(title = "船艇订单评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipOrderEvaluation shipOrderEvaluation)
    {
        return toAjax(shipOrderEvaluationService.updateShipOrderEvaluation(shipOrderEvaluation));
    }

    /**
     * 删除船艇订单评价
     */
    @PreAuthorize("@ss.hasPermi('ship:evaluation:remove')")
    @Log(title = "船艇订单评价", businessType = BusinessType.DELETE)
	@DeleteMapping("/{evaluationIds}")
    public AjaxResult remove(@PathVariable List<Long> evaluationIds)
    {
        return toAjax(shipOrderEvaluationService.deleteShipOrderEvaluationByEvaluationIds(evaluationIds));
    }
}
