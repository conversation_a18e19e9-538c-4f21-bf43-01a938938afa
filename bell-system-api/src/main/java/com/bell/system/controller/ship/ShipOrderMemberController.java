package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.ship.domain.ShipOrderMember;
import com.bell.ship.service.IShipOrderMemberService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 订单成员Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/ship/shipOrderMember")
public class ShipOrderMemberController extends BaseController
{
    @Autowired
    private IShipOrderMemberService shipOrderMemberService;

    /**
     * 查询订单成员列表
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipOrderMember shipOrderMember)
    {
        startPage();
        List<ShipOrderMember> list = shipOrderMemberService.selectShipOrderMemberList(shipOrderMember);
        return getDataTable(list);
    }

    /**
     * 导出订单成员列表
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:export')")
    @Log(title = "订单成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipOrderMember shipOrderMember)
    {
        List<ShipOrderMember> list = shipOrderMemberService.selectShipOrderMemberList(shipOrderMember);
        ExcelUtil<ShipOrderMember> util = new ExcelUtil<ShipOrderMember>(ShipOrderMember.class);
        util.exportExcel(response, list, "订单成员数据");
    }

    /**
     * 获取订单成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:query')")
    @GetMapping(value = "/{memberId}")
    public AjaxResult getInfo(@PathVariable("memberId") Long memberId)
    {
        return success(shipOrderMemberService.selectShipOrderMemberByMemberId(memberId));
    }

    /**
     * 新增订单成员
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:add')")
    @Log(title = "订单成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOrderMember shipOrderMember)
    {
        return toAjax(shipOrderMemberService.insertShipOrderMember(shipOrderMember));
    }

    /**
     * 修改订单成员
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:edit')")
    @Log(title = "订单成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipOrderMember shipOrderMember)
    {
        return toAjax(shipOrderMemberService.updateShipOrderMember(shipOrderMember));
    }

    /**
     * 删除订单成员
     */
    @PreAuthorize("@ss.hasPermi('ship:shipOrderMember:remove')")
    @Log(title = "订单成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{memberIds}")
    public AjaxResult remove(@PathVariable List<Long> memberIds)
    {
        return toAjax(shipOrderMemberService.deleteShipOrderMemberByMemberIds(memberIds));
    }
}
