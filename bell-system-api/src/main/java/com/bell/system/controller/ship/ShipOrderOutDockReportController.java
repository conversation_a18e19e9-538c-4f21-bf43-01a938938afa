package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.ship.domain.dto.ShipOrderOutDockReportDto;
import com.bell.ship.domain.vo.ShipOrderOutDockReportListVo;
import com.bell.ship.domain.vo.ShipOrderOutDockReportVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.ship.domain.ShipOrderOutDockReport;
import com.bell.ship.service.IShipOrderOutDockReportService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 出港报备Controller
 * 
 * <AUTHOR>
 * @date 2025-03-15
 */
@RestController
@RequestMapping("/ship/outReport")
public class ShipOrderOutDockReportController extends BaseController
{
    @Autowired
    private IShipOrderOutDockReportService shipOrderOutDockReportService;

    /**
     * 查询出港报备列表
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipOrderOutDockReportDto shipOrderOutDockReportDto) {
        startPage();
        List<ShipOrderOutDockReportListVo> list = shipOrderOutDockReportService.selectShipOrderOutDockReportList(shipOrderOutDockReportDto);
        return getDataTable(list);
    }

    /**
     * 导出出港报备列表
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:export')")
    @Log(title = "出港报备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipOrderOutDockReportDto shipOrderOutDockReportDto) {
        shipOrderOutDockReportDto.setPageNum(null);
        shipOrderOutDockReportDto.setPageSize(null);
        List<ShipOrderOutDockReportListVo> list = shipOrderOutDockReportService.selectShipOrderOutDockReportExportExcel(shipOrderOutDockReportDto);
        ExcelUtil<ShipOrderOutDockReportListVo> util = new ExcelUtil<ShipOrderOutDockReportListVo>(ShipOrderOutDockReportListVo.class);
        util.exportExcel(response, list, "出港报备数据");
    }

    /**
     * 获取出港报备详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:query')")
    @GetMapping(value = "/getOutDockReportInfo")
    public AjaxResult getInfo(Long reportId, Integer reportType)
    {
        return success(shipOrderOutDockReportService.selectShipOrderOutDockReportByReportId(reportId, reportType));
    }

    /**
     * 新增出港报备
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:add')")
    @Log(title = "出港报备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOrderOutDockReport shipOrderOutDockReport)
    {
        return toAjax(shipOrderOutDockReportService.insertShipOrderOutDockReport(shipOrderOutDockReport));
    }

    /**
     * 修改出港报备
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:edit')")
    @Log(title = "出港报备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipOrderOutDockReport shipOrderOutDockReport)
    {
        return toAjax(shipOrderOutDockReportService.updateShipOrderOutDockReport(shipOrderOutDockReport));
    }

    /**
     * 删除出港报备
     */
    @PreAuthorize("@ss.hasPermi('ship:outReport:remove')")
    @Log(title = "出港报备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable List<Long> reportIds)
    {
        return toAjax(shipOrderOutDockReportService.deleteShipOrderOutDockReportByReportIds(reportIds));
    }
}
