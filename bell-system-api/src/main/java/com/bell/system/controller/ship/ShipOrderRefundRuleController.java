package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.ship.domain.ShipOrderRefundRule;
import com.bell.ship.domain.dto.ShipOrderRefundReasonDto;
import com.bell.ship.domain.vo.ShipOrderRefundReasonVo;
import com.bell.ship.domain.vo.ShipOrderRefundRuleVo;
import com.bell.ship.service.IShipOrderRefundRuleService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 海钓订单退款规则Controller
 * 
 * <AUTHOR>
 * @date 2025-05-22
 */
@RestController
@RequestMapping("/shipOrder/shipOrderRefundRule")
public class ShipOrderRefundRuleController extends BaseController
{
    @Autowired
    private IShipOrderRefundRuleService shipOrderRefundRuleService;

    /**
     * 查询海钓订单退款规则列表
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:list')")
    @GetMapping("/list")
    public AjaxResult list() {
        List<ShipOrderRefundRuleVo> list = shipOrderRefundRuleService.getShipOrderRefundRuleList();
        return AjaxResult.success(list);
    }

    /**
     * 获取海钓订单退款规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:query')")
    @GetMapping(value = "/{refundRuleId}")
    public AjaxResult getInfo(@PathVariable("refundRuleId") Long refundRuleId)
    {
        return success(shipOrderRefundRuleService.selectShipOrderRefundRuleByRefundRuleId(refundRuleId));
    }

    /**
     * 新增海钓订单退款规则
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:add')")
    @Log(title = "海钓订单退款规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOrderRefundRule shipOrderRefundRule)
    {
        return toAjax(shipOrderRefundRuleService.insertShipOrderRefundRule(shipOrderRefundRule));
    }

    /**
     * 修改海钓订单退款规则
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:edit')")
    @Log(title = "海钓订单退款规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody List<ShipOrderRefundRuleVo> shipOrderRefundRuleList)
    {
        return toAjax(shipOrderRefundRuleService.editShipOrderRefundRule(shipOrderRefundRuleList));
    }

    /**
     * 删除海钓订单退款规则
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:remove')")
    @Log(title = "海钓订单退款规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{refundRuleIds}")
    public AjaxResult remove(@PathVariable Long[] refundRuleIds)
    {
//        return toAjax(shipOrderRefundRuleService.deleteShipOrderRefundRuleByRefundRuleIds(refundRuleIds));
        return success();
    }

    /**
     * 查询退款原因配置列表
     */
    @PreAuthorize("@ss.hasPermi('shipOrder:shipOrderRefundRule:list')")
    @GetMapping("/listReason")
    public TableDataInfo listReason(ShipOrderRefundReasonDto shipOrderRefundReasonDto) {
        startPage();
        List<ShipOrderRefundReasonVo> list = shipOrderRefundRuleService.getShipOrderReasonList(shipOrderRefundReasonDto);
        return getDataTable(list);
    }

    /**
     * 新增退款原因配置
     * @param shipOrderRefundReasonDto
     * @return
     */
    @PostMapping("/addRefundReason")
    public AjaxResult addRefundReason(@RequestBody ShipOrderRefundReasonDto shipOrderRefundReasonDto) {
        return shipOrderRefundRuleService.addRefundReason(shipOrderRefundReasonDto);
    }

    /**
     * 获取约船退单理由详情
     * @param refundReasonId
     * @return
     */
    @GetMapping("/getRefundReasonInfo/{refundReasonId}")
    public AjaxResult getRefundReasonInfo(@PathVariable Long refundReasonId) {
        return success(shipOrderRefundRuleService.getShipOrderReasonInfo(refundReasonId));
    }

    /**
     * 更新约船退单理由详情
     * @param shipOrderRefundReasonDto
     * @return
     */
    @PostMapping("/updateRefundReason")
    public AjaxResult updateRefundReason(@RequestBody ShipOrderRefundReasonDto shipOrderRefundReasonDto) {
        return shipOrderRefundRuleService.updateRefundReason(shipOrderRefundReasonDto);
    }

    /**
     * 删除约船退单理由详情
     * @param refundReasonId
     * @return
     */
    @GetMapping("/delShipOrderRefundReasonInfo/{refundReasonId}")
    public AjaxResult delShipOrderRefundReasonInfo(@PathVariable Long refundReasonId) {
        return toAjax(shipOrderRefundRuleService.delShipOrderRefundReasonInfo(refundReasonId));
    }
}
