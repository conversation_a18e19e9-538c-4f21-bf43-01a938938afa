package com.bell.system.controller.ship;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.ship.domain.ShipOwner;
import com.bell.ship.service.IShipOwnerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 船主Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/ship/owner")
public class ShipOwnerController extends BaseController
{
    @Autowired
    private IShipOwnerService shipOwnerService;

    /**
     * 查询船主列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ShipOwner shipOwner)
    {
        startPage();
        List<ShipOwner> list = shipOwnerService.selectShipOwnerList(shipOwner);
        return getDataTable(list);
    }
    @GetMapping("/listWithShip")
    public TableDataInfo listWithShip(ShipOwner shipOwner)
    {
        startPage();
        List<ShipOwner> list = shipOwnerService.selectShipOwnerListWithShip(shipOwner);
        return getDataTable(list);
    }
    /**
     * 导出船主列表
     */
    @PreAuthorize("@ss.hasPermi('ship:owner:export')")
    @Log(title = "船主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipOwner shipOwner)
    {
        List<ShipOwner> list = shipOwnerService.selectShipOwnerListWithShip(shipOwner);
        ExcelUtil<ShipOwner> util = new ExcelUtil<ShipOwner>(ShipOwner.class);
        util.exportExcel(response, list, "船主数据");
    }

    /**
     * 获取船主详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:owner:query')")
    @GetMapping(value = "/{ownerId}")
    public AjaxResult getInfo(@PathVariable("ownerId") Long ownerId)
    {
        return success(shipOwnerService.selectShipOwnerByOwnerId(ownerId));
    }

    /**
     * 新增船主
     */
    @PreAuthorize("@ss.hasPermi('ship:owner:add')")
    @Log(title = "船主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipOwner shipOwner)
    {
        return shipOwnerService.insertShipOwner(shipOwner);
    }

    /**
     * 修改船主
     */
    @PreAuthorize("@ss.hasPermi('ship:owner:edit')")
    @Log(title = "船主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipOwner shipOwner)
    {
        return shipOwnerService.updateShipOwner(shipOwner);
    }

    /**
     * 删除船主
     */
    @PreAuthorize("@ss.hasPermi('ship:owner:remove')")
    @Log(title = "船主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ownerIds}")
    public AjaxResult remove(@PathVariable List<Long> ownerIds)
    {
        return toAjax(shipOwnerService.deleteShipOwnerByOwnerIds(ownerIds));
    }
}
