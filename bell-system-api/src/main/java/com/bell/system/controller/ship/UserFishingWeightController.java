package com.bell.system.controller.ship;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.bell.ship.domain.dto.UserFishingWeightDto;
import com.bell.ship.domain.vo.AppUserFishingWeightVo;
import com.bell.ship.domain.vo.UserFishingWeightVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.ship.domain.UserFishingWeight;
import com.bell.ship.service.IUserFishingWeightService;
import com.bell.common.utils.poi.ExcelUtil;
import com.bell.common.core.page.TableDataInfo;

/**
 * 称重记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/ship/fishingWeight")
public class UserFishingWeightController extends BaseController
{
    @Autowired
    private IUserFishingWeightService userFishingWeightService;

    /**
     * 查询称重记录列表
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserFishingWeightDto userFishingWeight)
    {
        startPage();
        List<UserFishingWeightVo> list = userFishingWeightService.selectUserFishingWeightList(userFishingWeight);
        return getDataTable(list);
    }

    /**
     * 导出称重记录列表
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:export')")
    @Log(title = "称重记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserFishingWeightDto userFishingWeight)
    {
        List<UserFishingWeightVo> list = userFishingWeightService.selectUserFishingWeightList(userFishingWeight);
        ExcelUtil<UserFishingWeightVo> util = new ExcelUtil<UserFishingWeightVo>(UserFishingWeightVo.class);
        util.exportExcel(response, list, "称重记录数据");
    }

    /**
     * 获取称重记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:query')")
    @GetMapping(value = "/{weightId}")
    public AjaxResult getInfo(@PathVariable("weightId") Long weightId)
    {
        return success(userFishingWeightService.selectUserFishingWeightByWeightId(weightId));
    }

    /**
     * 新增称重记录
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:add')")
    @Log(title = "称重记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserFishingWeight userFishingWeight)
    {
        return toAjax(userFishingWeightService.insertUserFishingWeight(userFishingWeight));
    }

    /**
     * 修改称重记录
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:edit')")
    @Log(title = "称重记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserFishingWeight userFishingWeight)
    {
        return userFishingWeightService.updateUserFishingWeight(userFishingWeight);
    }

    /**
     * 删除称重记录
     */
    @PreAuthorize("@ss.hasPermi('ship:fishingWeight:remove')")
    @Log(title = "称重记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{weightIds}")
    public AjaxResult remove(@PathVariable List<Long> weightIds)
    {
        return toAjax(userFishingWeightService.deleteUserFishingWeightByWeightIds(weightIds));
    }
}
