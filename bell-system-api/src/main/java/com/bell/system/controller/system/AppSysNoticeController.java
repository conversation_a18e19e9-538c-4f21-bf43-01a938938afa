package com.bell.system.controller.system;

import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.core.page.TableDataInfo;
import com.bell.common.enums.BusinessType;
import com.bell.system.domain.DTO.SysNoticeDto;
import com.bell.system.domain.SysNotice;
import com.bell.system.domain.vo.SysNoticeVo;
import com.bell.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/system/notice")
public class AppSysNoticeController extends BaseController
{
    @Autowired
    private ISysNoticeService noticeService;

    /**
     * 获取通知公告列表
     */
    @GetMapping("/listMain")
    public TableDataInfo listMain(SysNoticeDto sysNoticeDto)
    {
        startPage();
        List<SysNoticeVo> list = noticeService.appSelectNoticeList(sysNoticeDto);
        return getDataTable(list);
    }

    /**
     * 获取通知公告列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysNoticeDto sysNoticeDto)
    {
        startPage();
        sysNoticeDto.setUserId(getUserId());
        List<SysNoticeVo> list = noticeService.appSelectNoticeList(sysNoticeDto);
        return getDataTable(list);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId)
    {
        return success(noticeService.appSelectNoticeById(noticeId));
    }

    /**
     * 全部设为已读
     */
    @PutMapping
    public AjaxResult readAll(@Validated @RequestBody SysNoticeDto sysNoticeDto)
    {
        return toAjax(noticeService.readAll(sysNoticeDto));
    }
}
