package com.bell.system.controller.system;

import java.util.List;

import com.bell.common.core.domain.entity.SysSetting;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bell.common.annotation.Log;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.common.enums.BusinessType;
import com.bell.system.service.ISysSettingService;
import com.bell.common.core.page.TableDataInfo;

/**
 * 系统设置Controller
 *
 * <AUTHOR>
 * @date 2023-08-19
 */
@RestController
@RequestMapping("/system/setting")
public class SysSettingController extends BaseController {
    @Autowired
    private ISysSettingService sysSettingService;

    /**
     * 查询系统设置列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SysSetting sysSetting) {
        startPage();
        List<SysSetting> list = sysSettingService.selectSysSettingList(sysSetting);
        return getDataTable(list);
    }

    /**
     * 获取系统设置信息
     */
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo() {
        return success(sysSettingService.selectSysSetting());
    }

    /**
     * 新增系统设置
     */
    @PreAuthorize("@ss.hasPermi('system:setting:add')")
    @Log(title = "系统设置", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody SysSetting sysSetting) {
        return toAjax(sysSettingService.insertSysSetting(sysSetting));
    }

    /**
     * 修改系统设置
     */
    @PreAuthorize("@ss.hasPermi('system:setting:edit')")
    @Log(title = "系统设置", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody SysSetting sysSetting) {
        return toAjax(sysSettingService.updateSysSetting(sysSetting));
    }

    /**
     * 删除系统设置
     */
    @PreAuthorize("@ss.hasPermi('system:setting:remove')")
    @Log(title = "系统设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{settingIds}")
    public AjaxResult remove(@PathVariable List<Long> settingIds) {
        return toAjax(sysSettingService.deleteSysSettingBySettingIds(settingIds));
    }
}
