package com.bell.system.controller.wx;

import com.bell.common.annotation.Anonymous;
import com.bell.common.core.controller.BaseController;
import com.bell.common.core.domain.AjaxResult;
import com.bell.scenic.domain.ScenicSpotTicketOrder;
import com.bell.ship.domain.ShipOrder;
import com.bell.wx.domain.DTO.PayRequestDTO;
import com.bell.wx.service.WxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
@RestController
@RequestMapping("/app/wxpay")
public class WxPayController extends BaseController {

    @Autowired
    private WxService wxService;
    /**
     * 退款回调
     */
    @PostMapping("/notify/refund")
    public AjaxResult notifyRefund(@RequestBody String refundInfoData,
                                   @RequestHeader("Wechatpay-Timestamp") String timestamp,
                                   @RequestHeader("Wechatpay-Nonce") String nonce,
                                   @RequestHeader("Wechatpay-Serial") String serial,
                                   @RequestHeader("Wechatpay-Signature") String signature)
    {
        return success(wxService.notifyRefund(refundInfoData, timestamp, nonce, serial, signature));
    }
    /**
     * 支付回调
     */
    @PostMapping("/notify/pay")
    public AjaxResult notifyPay(@RequestBody String payInfoData,
                                @RequestHeader("Wechatpay-Timestamp") String timestamp,
                                @RequestHeader("Wechatpay-Nonce") String nonce,
                                @RequestHeader("Wechatpay-Serial") String serial,
                                @RequestHeader("Wechatpay-Signature") String signature)
    {
        return success(wxService.notifyPay(payInfoData, timestamp, nonce, serial, signature));
    }
    @PutMapping("/refund")
    public AjaxResult refund(@RequestBody ShipOrder shipOrder)
    {
        return toAjax(wxService.refund(shipOrder));
    }

}
