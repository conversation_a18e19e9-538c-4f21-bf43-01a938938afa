package com.bell.car.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.bell.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 车企对象 car_company
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_company")
public class CarCompany extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long companyId;

    /** 车企名称 */
    @Excel(name = "车企名称")
    private String companyName;

    /** 手机号 */
    @Excel(name = "手机号")
    private String tel;

    /** 银行账号 */
    @Excel(name = "银行账号")
    private String bankNo;

    /** 开户行名称 */
    @Excel(name = "开户行名称")
    private String bankName;

    /** 银行开户名 */
    @Excel(name = "银行开户名")
    private String accountName;

    /** 1删除 */
    private Integer isDel;

    /**
     * 车企管理员用户ID
     */
    private Long companyUserId;

    /**
     * 车企佣金
     */
    @Excel(name = "车企佣金(%)")
    private BigDecimal companyCommission;

    /**
     * 平台佣金
     */
    @Excel(name = "平台佣金(%)")
    private BigDecimal platformCommission;

    /**
     * 管理人员佣金
     */
    @Excel(name = "管理人员佣金(%)")
    private BigDecimal managementCommission;
}
