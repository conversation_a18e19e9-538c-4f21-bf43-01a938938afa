package com.bell.car.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.bell.common.core.domain.BaseEntity;
import lombok.Data;
/**
 * 车辆配置对象 car_facility
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_facility")
public class CarFacility extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long facilityId;

    /** 设施名称 */
    @Excel(name = "设施名称")
    private String facilityName;

    /** 1删除 */
    @Excel(name = "1删除")
    private Integer isDel;

    /**
     * 关联车企id
     */
    private Long companyId;
}
