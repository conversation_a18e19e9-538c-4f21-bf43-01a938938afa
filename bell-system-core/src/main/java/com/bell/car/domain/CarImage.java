package com.bell.car.domain;

import com.bell.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.bell.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
/**
 * 车辆图片对象 car_image
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_image")
public class CarImage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long imageId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long carId;

    /** 1图片2视频 */
    @Excel(name = "1图片2视频")
    private Integer fileType;

    /** 文件地址 */
    @Excel(name = "文件地址")
    private String imageUrl;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("imageId", getImageId())
            .append("carId", getCarId())
            .append("fileType", getFileType())
            .append("imageUrl", getImageUrl())
            .append("fileName", getFileName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
