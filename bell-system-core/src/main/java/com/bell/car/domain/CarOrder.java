package com.bell.car.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.bell.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.bell.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
/**
 * 租车订单对象 car_order
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_order")
public class CarOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @TableId(type = IdType.AUTO)
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 下单人 */
    @Excel(name = "下单人")
    private Long userId;

    /** 状态，待付款=created，待取车=paid，使用中=driving，已完成=completed，已评价=evaluate，已取消=cancel，已关闭=closed */
    @Excel(name = "状态，待付款=created，待取车=paid，使用中=driving，已完成=completed，已评价=evaluate，已取消=cancel，已关闭=closed")
    private String state;

    /** 车辆id */
    @Excel(name = "车辆id")
    private Long carId;

    /** 车辆名称 */
    @Excel(name = "车辆名称")
    private String carName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNo;

    /** 车企 */
    @Excel(name = "车企")
    private String companyName;

    /** 车型 */
    @Excel(name = "车型")
    private String modelName;

    /** 乡镇id */
    @Excel(name = "乡镇id")
    private Long areaId;

    /** 日租金 */
    @Excel(name = "日租金")
    private BigDecimal price;

    /** 总租金 */
    @Excel(name = "总租金")
    private BigDecimal totalPrice;

    /** 押金总额 */
    @Excel(name = "押金总额")
    private BigDecimal deposit;

    /** 排量 */
    @Excel(name = "排量")
    private String volume;

    /** 燃油类型 */
    @Excel(name = "燃油类型")
    private String oilType;

    /** 排挡 */
    @Excel(name = "排挡")
    private String transmission;

    /** 邮箱容积 */
    @Excel(name = "邮箱容积")
    private String tankCapacity;

    /** 座位个数 */
    @Excel(name = "座位个数")
    private Integer seatQuantity;

    /** 车门数 */
    @Excel(name = "车门数")
    private Integer doorQuantity;

    /** 百公里油耗 */
    @Excel(name = "百公里油耗")
    private String fuelConsumption;

    /** 车龄 */
    @Excel(name = "车龄")
    private Integer carAge;

    /** 能源类型 */
    @Excel(name = "能源类型")
    private String powerType;

    /** 0未退款1已退款2退款中 */
    @Excel(name = "0未退款1已退款2退款中")
    private Integer refundState;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /** 退款人id */
    @Excel(name = "退款人id")
    private Long refundUserId;

    /** 退单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundDate;

    /** 退单号 */
    @Excel(name = "退单号")
    private String refundNo;

    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "付款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 取车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "取车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date takeCarTime;

    /** 还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "还车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarTime;

    /** 租期（天） */
    @Excel(name = "租期", readConverterExp = "天=")
    private Long orderDay;

    /** 上传合同人ID */
    @Excel(name = "上传合同人ID")
    private Long contractUserId;

    /** 合同上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "合同上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date contractTime;

    /** 验车人ID */
    @Excel(name = "验车人ID")
    private Long checkUserId;

    /** 验车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "验车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /** 退还押金 */
    @Excel(name = "退还押金")
    private BigDecimal returnDesposit;

    /** 退押金时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退押金时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnDespositTime;

    /** 退押金原因 */
    @Excel(name = "退押金原因")
    private String despositReason;

    /** 1删除 */
    @Excel(name = "1删除")
    private Integer isDel;

    private Integer modelId;

    private Integer companyId;

    private String tackCarName;

    private String returnCarName;

    /** 订单原始总租金 */
    private BigDecimal orderTotalPrice;

    //取车地址
    private String areaParentName;

    //还车地址
    private String areaName;

    //车封面
    private String coverUrl;

    /**
     * 平台退款原因
     */
    private String platformRefundReason;

    /** 修改订单次数 */
    private Integer updateNumber;

    /** 最新还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnNewCarTime;

    /**
     * 全部订单退款状态（0未退款1已退款2部分退款）
     */
    private Integer allOrderRefundState;

    /**
     * 总退款租金
     */
    private BigDecimal refundTotalPrice;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "验车时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("state", getState())
            .append("carId", getCarId())
            .append("carName", getCarName())
            .append("carNo", getCarNo())
            .append("companyName", getCompanyName())
            .append("modelName", getModelName())
            .append("areaId", getAreaId())
            .append("price", getPrice())
            .append("totalPrice", getTotalPrice())
            .append("deposit", getDeposit())
            .append("volume", getVolume())
            .append("oilType", getOilType())
            .append("transmission", getTransmission())
            .append("tankCapacity", getTankCapacity())
            .append("seatQuantity", getSeatQuantity())
            .append("doorQuantity", getDoorQuantity())
            .append("fuelConsumption", getFuelConsumption())
            .append("carAge", getCarAge())
            .append("powerType", getPowerType())
            .append("refundState", getRefundState())
            .append("refundAmount", getRefundAmount())
            .append("refundUserId", getRefundUserId())
            .append("refundDate", getRefundDate())
            .append("refundNo", getRefundNo())
            .append("payTime", getPayTime())
            .append("takeCarTime", getTakeCarTime())
            .append("returnCarTime", getReturnCarTime())
            .append("orderDay", getOrderDay())
            .append("contractUserId", getContractUserId())
            .append("contractTime", getContractTime())
            .append("checkUserId", getCheckUserId())
            .append("checkTime", getCheckTime())
            .append("returnDesposit", getReturnDesposit())
            .append("returnDespositTime", getReturnDespositTime())
            .append("despositReason", getDespositReason())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDel", getIsDel())
            .toString();
    }
}
