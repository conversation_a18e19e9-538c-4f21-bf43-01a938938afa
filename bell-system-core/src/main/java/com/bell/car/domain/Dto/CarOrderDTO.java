package com.bell.car.domain.Dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 租车订单请求DTO
 * 
 * <AUTHOR>
 * @date 2025-05-17
 */
@Data
public class CarOrderDTO {
    
    /**
     * 车辆ID
     */
    @NotNull(message = "车辆ID不能为空")
    private Long carId;

    private Integer modelId;

    private Long userId;

    private Long orderId;

    private Integer companyId;

    //取车码头
    private String tackCarName;

    //还车码头
    private String returnCarName;

    //取车地区
    private String areaParentName;

    //取车村镇
    private String areaName;

    /**
     * 租车天数
     */
    @NotNull(message = "租车天数不能为空")
    @Min(value = 1, message = "租车天数必须大于0")
    private Long rentalDays;
    
    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 押金
     */
    private BigDecimal deposit;
    
    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    
    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    
    /**
     * 乡镇ID
     */
    private Long areaId;
    
    /**
     * 车辆名称
     */
    private String carName;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车企
     */
    private String companyName;

    /**
     * 车型
     */
    private String modelName;

    /**
     * 日租金
     */
    private BigDecimal price;

    /**
     * 排量
     */
    private String volume;

    /**
     * 燃油类型
     */
    private String oilType;

    /**
     * 排挡
     */
    private String transmission;

    /**
     * 邮箱容积
     */
    private String tankCapacity;

    /**
     * 座位个数
     */
    private Integer seatQuantity;

    /**
     * 车门数
     */
    private Integer doorQuantity;

    private String orderNo;

    private String customerName;

    private String customerPhoneNum;

    private String state;

    private Integer refundState;
}