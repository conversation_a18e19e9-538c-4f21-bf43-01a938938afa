package com.bell.car.domain.Dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 退款与已打款接参实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarOrderPayDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单状态
     */
    private String state;

    /**
     * 退款状态
     */
    private Integer refundState;

    /**
     * 退款金额
     */
    private BigDecimal refundPrice;

    /**
     * 退款人id
     */
    private Long refundUserId;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 变更人
     */
    private String updateBy;

    /**
     * 平台退款理由
     */
    private String platformRefundReason;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单类型1原始单2续租单
     */
    private Integer orderType;

    /**
     * 订单支付金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 主键id
     */
    private Long orderId;

    /**
     * 全部订单退款状态（0未退款1已退款2部分退款）
     */
    private Integer allOrderRefundState;

    /**
     * 还车时间
     */
    private Date returnCarTime;

    /**
     * 总退款租金
     */
    private BigDecimal refundTotalPrice;

    /** 最新还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnNewCarTime;
}
