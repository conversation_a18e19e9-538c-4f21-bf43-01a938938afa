package com.bell.car.domain.VO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AppCarModelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车型id
     */
    private Long modelId;

    /**
     * 车型名称
     */
    private String modelName;

    /**
     * 车型对应车辆最低价格
     */
    private BigDecimal price;
}
