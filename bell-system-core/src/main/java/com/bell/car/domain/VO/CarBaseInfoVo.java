package com.bell.car.domain.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarBaseInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 车辆名称
     */
    private String carName;

    /**
     * 封面
     */
    private String coverUrl;

    /**
     * 车企
     */
    private String companyName;

    /**
     * 车型
     */
    private String modelName;

    /**
     * 地区名
     */
    private String areaName;

    /**
     * 乡镇名
     */
    private String townName;

    /**
     * 取车码头
     */
    private String takeDockName;

    /**
     * 还车港口
     */
    private String returnDockName;

    /**
     * 日租金
     */
    private BigDecimal price;

    /**
     * 原始库存
     */
    private Integer quantity;

    /**
     * 库存
     */
    private Integer lostQuantity;

    /**
     * 车辆联系人
     */
    private String carUserName;

    /**
     * 车辆联系人电话
     */
    private String carUserPhone;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 是否可用（1：可用，0：不可用）
     */
    private Integer isAvailable;

    private Long companyId;

    private Long modelId;

    private Long areaId;

    private Long takeDockId;

    private Long returnDockId;

    private Long volume;

    private String oilType;

    private String transmission;

    private String tankCapacity;

    private Integer seatQuantity;

    private Integer doorQuantity;

    private String fuelConsumption;

    private Integer carAge;

    private String powerType;

    private Long userId;

    private List<Long> facilityIdList = new ArrayList<>();

    private List<String> carImageList = new ArrayList<>();
}
