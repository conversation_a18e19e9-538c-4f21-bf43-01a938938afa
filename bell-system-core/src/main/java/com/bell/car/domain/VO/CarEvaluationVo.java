package com.bell.car.domain.VO;

import com.bell.car.domain.CarOrderEvalutionFile;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarEvaluationVo  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 评价id
     */
    private Long evaluationId;

    /**
     * 评价内容
     */
    private String evaluationContent;

    /**
     * 评分
     */
    private BigDecimal evaluationScore;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 昵称
     */
    private String customerName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 评价图片集合
     */
    private String fileUrl;
}
