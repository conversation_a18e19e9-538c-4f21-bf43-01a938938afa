package com.bell.car.domain.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 车辆配置对象 car_facility
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_facility")
public class CarFacilityVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.AUTO)
    private Long facilityId;

    /**
     * 设施名称
     */
    @Excel(name = "设施名称")
    private String facilityName;

    /**
     * 1删除
     */
    @Excel(name = "1删除")
    private Integer isDel;

    /**
     * 关联车企id
     */
    private Long companyId;

    /**
     * 是否有权限操作 0 没有权限，1有操作权限
     */
    private Integer isOperate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
