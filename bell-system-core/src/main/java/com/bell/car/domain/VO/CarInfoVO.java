package com.bell.car.domain.VO;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CarInfoVO {
    private String takeDock;      // 取车码头名称
    private String returnDock;    // 还车码头名称
    private String carName;       // 车辆名称
    private Integer seatQuantity; // 座位数量
    private String transmission; // 变速箱类型
    private Double volume;       // 排量
    private BigDecimal price;    // 价格
    private String childrenName;
    private String parentName;
    private Integer companyId;
    private Integer modelId;
    private String coverUrl;
}
