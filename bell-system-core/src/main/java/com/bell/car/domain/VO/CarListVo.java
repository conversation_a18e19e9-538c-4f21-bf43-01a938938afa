package com.bell.car.domain.VO;

import com.bell.basic.domain.BellDock;
import com.bell.basic.domain.BellSeaArea;
import com.bell.basic.domain.vo.BannerVo;
import com.bell.car.domain.CarBaseInfoEntity;
import com.bell.car.domain.CarFacility;
import com.bell.car.domain.CarModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * banner图列表
     */
    private List<BannerVo> carBannerList = new ArrayList<>();

    /**
     * 二级地区列表
     */
    private List<BellSeaArea> areaList = new ArrayList<>();

    /**
     * 码头列表
     */
    private List<BellDock> dockList = new ArrayList<>();

    /**
     * 车型集合
     */
    private List<AppCarModelVo> carModelList;

    /**
     * 车辆列表
     */
    private List<CarBaseInfoEntity> carBaseInfoList;

    /**
     * 车联配置集合
     */
    private List<CarFacility> carFacilityList;

    /**
     * 最小取车时间
     */
    private String startTime;

    /**
     * 最大还车时间
     */
    private String endTime;
}
