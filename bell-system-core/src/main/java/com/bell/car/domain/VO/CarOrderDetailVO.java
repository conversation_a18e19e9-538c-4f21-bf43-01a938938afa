package com.bell.car.domain.VO;

import com.bell.car.domain.CarOrderImage;
import com.bell.car.domain.CarOrderTenancyTerm;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Data
public class CarOrderDetailVO implements Serializable
{
    private static final long serialVersionUID = 1L;

    private Long orderId;

    /** 状态，待付款=created，待取车=paid，使用中=driving，已完成=completed，已评价=evaluate，已取消=cancel，已关闭=closed */
    private String state;

    /** 订单编号 */
    private String orderNo;

    /** 车辆名称 */
    private String carName;

    /** 排量 */
    private String volume;
    /** 座位个数 */
    private Integer seatQuantity;

    /** 排挡 */
    private String transmission;

    /** 车龄 */
    private Integer carAge;

    /** 车型 */
    private String modelName;

    /** 总租金 */
    private BigDecimal totalPrice;

    /** 退款金额 */
    private BigDecimal refundAmount;

    /** 退款人id */
    private Long refundUserId;

    /** 退单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundDate;

    /** 最新还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnNewCarTime;


    /** 退单号 */
    private String refundNo;

    /** 退款状态 */
    private Integer refundState;

    /** 取车地点 */
    private String tackCarName;

    /** 还车地点 */
    private String returnCarName;

    /** 取车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date takeCarTime;

    /** 还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarTime;

    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 订单原始总租金 */
    private BigDecimal orderTotalPrice;

    /** 续租数据 */
    private List<CarOrderTenancyTerm> tenancies;

    /** 合同/验车图片 */
    private List<CarOrderImage> fileList;

    /** 押金总额 */
    private BigDecimal deposit;

    /** 退还押金 */
    private BigDecimal returnDesposit;

    /** 退押金时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnDespositTime;

    /** 退押金原因 */
    private String despositReason;

    /** 租期（天） */
    private Long orderDay;

    /** 上传合同人ID */
    private Long contractUserId;

    /** 合同上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractTime;

    /** 验车人ID */
    private Long checkUserId;

    /** 验车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")

    /** 下单人 */
    private Long userId;

    /** 车辆ID */
    private Long carId;

    /** 车牌号 */
    private String carNo;

    private String nickName;

    private Long phonenumber;

    private Integer modelId;

    private Integer companyId;

    private String companyName;

    private String coverUrl;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    private Integer allOrderRefundState;

    private BigDecimal refundTotalPrice;

    private String platformRefundReason;

    private Integer updateNumber;

    //取车码头照片
    private String tackCarImage;
    //还车码头照片
    private String returnCarImage;
    //车企联系人员电话
    private String contactInfo;

}
