package com.bell.car.domain.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆订单详情VO
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class CarOrderDetailsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 区域名称 */
    private String areaName;
    
    /** 父区域名称 */
    private String areaParentName;
    
    /** 取车地点 */
    private String tackCarName;
    
    /** 还车地点 */
    private String returnCarName;
    
    /** 取车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date takeCarTime;
    
    /** 价格 */
    private BigDecimal price;
    
    /** 还车日期（如果有续租则取最新的续租日期，否则取订单的还车时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarDate;
}