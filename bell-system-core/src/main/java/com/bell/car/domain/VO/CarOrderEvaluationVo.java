package com.bell.car.domain.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.car.domain.CarOrderEvalutionFile;
import com.bell.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 车辆订单评价对象 car_order_evaluation
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@TableName("car_order_evaluation")
public class CarOrderEvaluationVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.AUTO)
    private Long evaluationId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long userId;

    /**
     * 订单ID
     */
    @Excel(name = "订单ID")
    private Long orderId;

    /**
     * 评价分数
     */
    @Excel(name = "评价分数")
    private Integer evaluationScore;

    /**
     * 评价内容
     */
    @Excel(name = "评价内容")
    private String evaluationContent;

    /**
     * 1删除
     */
    @Excel(name = "1删除")
    private Integer isDel;

    // 订单编号
    private String orderNo;
    // 评价人
    private String nickName;
    // 评价人电话号码
    private String phonenumber;
    // 车辆名称
    private String carName;
    // 车企名称
    private String companyName;
    // 区域名称
    private String areaParentName;
    // 乡镇名称
    private String areaName;
    // 评价时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    // 评价图片list
    private List<CarOrderEvalutionFile> evalutionFilelist;
}
