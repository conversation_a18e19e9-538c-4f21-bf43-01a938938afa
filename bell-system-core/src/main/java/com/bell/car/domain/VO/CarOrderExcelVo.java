package com.bell.car.domain.VO;

import com.bell.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarOrderExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "订单编号")
    private String orderNo;

    @Excel(name = "订单状态", readConverterExp = "created=待付款,paid=待取车,driving=使用中,completed=已完成,evaluate=已评价,cancel=已取消,closed=已关闭")
    private String state;

    @Excel(name = "下单时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "下单人信息")
    private String customerInfo;

    @Excel(name = "车辆名称")
    private String carName;

    @Excel(name = "车企")
    private String companyName;

    @Excel(name = "取车时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date takeCarTime;

    @Excel(name = "还车时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date nowReturnCarTime;

    @Excel(name = "原始订单还车时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarTime;

    @Excel(name = "租期")
    private Integer orderDay;

    @Excel(name = "单价（元/天）")
    private BigDecimal price;

    @Excel(name = "总价（元）")
    private BigDecimal totalPrice;

    @Excel(name = "原始订单总价（元）")
    private BigDecimal orderTotalPrice;

    @Excel(name = "车牌号")
    private String carNo;

    @Excel(name = "取车地点")
    private String takeCarName;

    @Excel(name = "还车地点")
    private String returnCarName;

    @Excel(name = "续订单信息")
    private String orderInfo;

    @Excel(name = "原始订单退款状态", readConverterExp = "0=未退款,1=已退款,2=退款中,3=退款失败")
    private Integer refundState;

    @Excel(name = "原始订单退款日期", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundDate;

    @Excel(name = "原始订单退款金额")
    private BigDecimal refundAmount;

    @Excel(name = "原始订单平台退款理由")
    private String platFormRefundReason;

    @Excel(name = "续订单退款信息")
    private String refundOrderInfo;

    @Excel(name = "合同上传时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date contractTime;

    @Excel(name = "合同上传人")
    private String contractRealName;

    @Excel(name = "合同上传人账号")
    private String contractUserName;

    @Excel(name = "合同文件地址")
    private String contractUrl;

    @Excel(name = "验车时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    @Excel(name = "验车人")
    private String checkRealName;

    @Excel(name = "验车人账号")
    private String checkUserName;

    @Excel(name = "验车文件地址")
    private String checkUrl;

    @Excel(name = "预授权冻结金额")
    private BigDecimal deposit;

    @Excel(name = "预授权已退还金额")
    private BigDecimal returnDeposit;

    @Excel(name = "退还时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date returnDepositTime;

    @Excel(name = "扣除原因")
    private String despositReason;
}
