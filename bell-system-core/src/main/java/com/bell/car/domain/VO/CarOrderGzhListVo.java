package com.bell.car.domain.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bell.common.annotation.Excel;
import com.bell.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 租车订单对象 car_order
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
public class CarOrderGzhListVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long orderId;

    /** 订单编号 */
    private String orderNo;

    /** 下单人 */
    private Long userId;

    /** 状态，待付款=created，待取车=paid，使用中=driving，已完成=completed，已评价=evaluate，已取消=cancel，已关闭=closed */
    private String state;

    /** 车图 */
    private String coverUrl;

    /** 车辆id */
    private Long carId;

    /** 车辆名称 */
    private String carName;

    /** 车牌号 */
    private String carNo;

    /** 车企 */
    private String companyName;

    /** 车型 */
    private String modelName;

    /** 乡镇id */
    private Long areaId;

    /** 日租金 */
    private BigDecimal price;

    /** 总租金 */
    private BigDecimal totalPrice;

    /** 押金总额 */
    private BigDecimal deposit;

    /** 排量 */
    private String volume;

    /** 燃油类型 */
    private String oilType;

    /** 排挡 */
    private String transmission;

    /** 邮箱容积 */
    private String tankCapacity;

    /** 座位个数 */
    private Integer seatQuantity;

    /** 车门数 */
    private Integer doorQuantity;

    /** 百公里油耗 */
    private String fuelConsumption;

    /** 车龄 */
    private Integer carAge;

    /** 能源类型 */
    private String powerType;

    /** 0未退款1已退款2退款中 */
    private Integer refundState;

    /** 全部订单退款状态 */
    private Integer allOrderRefundState;

    /** 退款金额 */
    private BigDecimal refundAmount;

    /** 退款总金额 */
    private BigDecimal refundTotalPrice;

    /** 退款人id */
    private Long refundUserId;

    /** 退单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date refundDate;

    /** 退单号 */
    @Excel(name = "退单号")
    private String refundNo;

    /** 付款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date payTime;

    /** 取车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date takeCarTime;

    /** 还车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarTime;

    /** 还车时间（计算续租） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnCarTimeReal;

    /** 租期（天） */
    private Long orderDay;

    /** 上传合同人ID */
    private Long contractUserId;

    /** 合同上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractTime;

    /** 验车人ID */
    private Long checkUserId;

    /** 验车时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;

    /** 退还押金 */
    private BigDecimal returnDesposit;

    /** 退押金时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date returnDespositTime;

    /** 退押金原因 */
    private String despositReason;

    /** 1删除 */
    private Integer isDel;

    private Integer modelId;

    private Integer companyId;

    /** 客户姓名 */
    private String realName;

    /** 客户手机号 */
    private String phonenumber;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("orderId", getOrderId())
            .append("orderNo", getOrderNo())
            .append("userId", getUserId())
            .append("state", getState())
            .append("carId", getCarId())
            .append("carName", getCarName())
            .append("carNo", getCarNo())
            .append("companyName", getCompanyName())
            .append("modelName", getModelName())
            .append("areaId", getAreaId())
            .append("price", getPrice())
            .append("totalPrice", getTotalPrice())
            .append("deposit", getDeposit())
            .append("volume", getVolume())
            .append("oilType", getOilType())
            .append("transmission", getTransmission())
            .append("tankCapacity", getTankCapacity())
            .append("seatQuantity", getSeatQuantity())
            .append("doorQuantity", getDoorQuantity())
            .append("fuelConsumption", getFuelConsumption())
            .append("carAge", getCarAge())
            .append("powerType", getPowerType())
            .append("refundState", getRefundState())
            .append("refundAmount", getRefundAmount())
            .append("refundUserId", getRefundUserId())
            .append("refundDate", getRefundDate())
            .append("refundNo", getRefundNo())
            .append("payTime", getPayTime())
            .append("takeCarTime", getTakeCarTime())
            .append("returnCarTime", getReturnCarTime())
            .append("orderDay", getOrderDay())
            .append("contractUserId", getContractUserId())
            .append("contractTime", getContractTime())
            .append("checkUserId", getCheckUserId())
            .append("checkTime", getCheckTime())
            .append("returnDesposit", getReturnDesposit())
            .append("returnDespositTime", getReturnDespositTime())
            .append("despositReason", getDespositReason())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("isDel", getIsDel())
            .toString();
    }
}
