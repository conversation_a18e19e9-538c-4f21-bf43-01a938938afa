package com.bell.car.domain.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderNo;

    /**
     * 车辆名称
     */
    private String carName;

    /**
     * 车企名称
     */
    private String companyName;

    /**
     * 乡镇名称
     */
    private String townName;

    /**
     * 地区名称
     */
    private String areaName;

    /**
     * 下单人名称
     */
    private String customerName;

    /**
     * 下单人电话
     */
    private String customerPhoneNum;

    /**
     * 取车时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Date takeCarTime;

    /**
     * 还车时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private Date returnCarTime;

    /**
     * 租期
     */
    private Integer orderDay;

    /**
     * 日租金
     */
    private BigDecimal price;

    /**
     * 订单金额
     */
    private BigDecimal orderTotalPrice;

    /**
     * 订单总金额（包含续期订单）
     */
    private BigDecimal totalPrice;

    /**
     * 下单日
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 订单状态
     */
    private String state;

    /**
     * 退款状态
     */
    private Integer refundState;

    /**
     * 是否包含续期订单
     */
    private Boolean hasChildren;

    /**
     * 全部订单退款状态（0未退款1已退款2部分退款）
     */
    private Integer allOrderRefundState;

    /**
     * 续订单序号
     */
    private Integer orderIndex;
}
