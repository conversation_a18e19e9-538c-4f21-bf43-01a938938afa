package com.bell.car.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bell.car.domain.CarCompany;
import com.bell.car.domain.Dto.CarCompanyDto;
import com.bell.car.domain.VO.CarCompanyVo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 车企Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Repository
public interface CarCompanyMapper extends BaseMapper<CarCompany>
{
    /**
     * 修改密码
     *
     * @param carCompanyDto 数据
     * @return 信息
     */
    boolean updatePassword(CarCompanyDto carCompanyDto);

    /**
     * 查询车企列表
     *
     * @param carCompany 车企
     * @return 车企
     */
    List<CarCompanyVo> selectCarCompanyListInfo(CarCompany carCompany);

    /**
     * 车企修改结算比例
     *
     * @param carCompany 数据
     * @return 修改的结果
     */
    boolean updateSettlement(CarCompany carCompany);

    /**
     * 查询要结算的车企列表
     *
     * @return
     */
    List<CarCompanyVo> selectSettlementCarCompanyList();
}
