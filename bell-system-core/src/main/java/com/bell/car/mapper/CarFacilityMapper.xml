<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bell.car.mapper.CarFacilityMapper">
    
    <resultMap type="CarFacility" id="CarFacilityResult">
        <result property="facilityId"    column="facility_id"    />
        <result property="facilityName"    column="facility_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectCarFacilityVo">
        select facility_id, facility_name, create_by, create_time, update_by, update_time, is_del from car_facility
    </sql>

    <select id="selectCarFacilityList" parameterType="CarFacility" resultMap="CarFacilityResult">
        <include refid="selectCarFacilityVo"/>
        <where>  
            <if test="facilityName != null  and facilityName != ''"> and facility_name like concat('%', #{facilityName}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCarFacilityByFacilityId" parameterType="Long" resultMap="CarFacilityResult">
        <include refid="selectCarFacilityVo"/>
        where facility_id = #{facilityId}
    </select>
        
    <insert id="insertCarFacility" parameterType="CarFacility" useGeneratedKeys="true" keyProperty="facilityId">
        insert into car_facility
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="facilityName != null">facility_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="facilityName != null">#{facilityName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateCarFacility" parameterType="CarFacility">
        update car_facility
        <trim prefix="SET" suffixOverrides=",">
            <if test="facilityName != null">facility_name = #{facilityName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where facility_id = #{facilityId}
    </update>

    <delete id="deleteCarFacilityByFacilityId" parameterType="Long">
        delete from car_facility where facility_id = #{facilityId}
    </delete>

    <delete id="deleteCarFacilityByFacilityIds" parameterType="String">
        delete from car_facility where facility_id in 
        <foreach item="facilityId" collection="array" open="(" separator="," close=")">
            #{facilityId}
        </foreach>
    </delete>
    <!--查询车辆配置列表-->
    <select id="getCarFacilityPersonList" resultType="CarFacilityVo">
        select facility_id, facility_name,company_id, create_by, create_time,1 as isOperate
        from car_facility
        where
        is_del = 0
        <if test="facilityName != null  and facilityName != ''">and facility_name like concat('%', #{facilityName},
            '%')
        </if>
        <if test="createBy != null  and createBy != ''">and create_by like concat('%', #{createBy}, '%')</if>
        order by create_time desc
    </select>
</mapper>