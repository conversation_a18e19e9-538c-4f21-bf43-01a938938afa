<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bell.car.mapper.CarOrderEvaluationMapper">
    
    <resultMap type="CarOrderEvaluation" id="CarOrderEvaluationResult">
        <result property="evaluationId"    column="evaluation_id"    />
        <result property="userId"    column="user_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="evaluationScore"    column="evaluation_score"    />
        <result property="evaluationContent"    column="evaluation_content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <resultMap id="CarOrderEvaluationVoMap" type="CarOrderEvaluationVo">
        <id property="evaluationId" column="evaluationId"/>
        <result property="orderNo" column="order_no"/>
        <result property="createTime" column="create_time"/>
        <result property="carName" column="car_name"/>
        <result property="companyName" column="company_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="evaluationScore" column="evaluation_score"/>
        <result property="evaluationContent" column="evaluation_content"/>
        <collection property="evalutionFilelist" ofType="CarOrderEvalutionFile">
            <id property="evaluationFileId" column="evaluationFileId"/>
            <result property="fileUrl" column="fileUrl"/>
            <result property="fileName" column="fileName"/>
        </collection>
    </resultMap>

    <sql id="selectCarOrderEvaluationVo">
        select evaluation_id, user_id, order_id, evaluation_score, evaluation_content, create_by, create_time, update_by, update_time, is_del from car_order_evaluation
    </sql>

    <!--查询车辆订单评价列表-->
    <select id="selectCarOrderEvaluationList" parameterType="CarOrderEvaluationDto" resultType="CarOrderEvaluationVo">
        SELECT co.order_no,
        co.car_name,
        co.company_name,
        co.area_parent_name,
        co.area_name,
        su.nick_name,
        su.phonenumber,
        coe.evaluation_score,
        coe.create_time,
        coe.evaluation_id
        FROM car_order_evaluation coe
        LEFT JOIN car_order co
        ON coe.order_id = co.order_id
        LEFT JOIN sys_user su ON coe.user_id = su.user_id
        LEFT JOIN (SELECT bsa.area_id AS parentId,
        bsa.area_name AS parentName,
        bsa1.area_id AS childrenId,
        bsa1.area_name AS childrenName
        FROM bell_sea_area bsa
        LEFT JOIN bell_sea_area bsa1 ON bsa.area_id = bsa1.parent_id
        AND bsa1.area_level = 2
        WHERE bsa.area_level = 1
        AND bsa.is_del = 0) area ON area.childrenId = co.area_id
        WHERE
        coe.is_del = 0
        <if test="nickName != null and nickName != ''">AND su.nick_name = #{nickName}</if>
        <if test="phonenumber != null and phonenumber != ''">AND su.phonenumber = #{phonenumber}</if>
        <if test="orderNo != null and orderNo != ''">AND co.order_no = #{orderNo}</if>
        <if test="companyId != null and companyId != ''">AND co.company_id = #{companyId}</if>
        <if test="carName != null and carName != ''">AND co.car_name = #{carName}</if>
        <if test="companyName != null and companyName != ''">AND co.company_name = #{companyName}</if>
        <if test="areaId != null and villageId != null">AND co.area_id =#{villageId}</if>
        <if test="areaId != null and villageId == null">AND area.parentId = #{areaId}</if>
        <if test="evaluationTimeStart != null and evaluationTimeEnd != null">
            AND coe.create_time BETWEEN #{evaluationTimeStart} AND #{evaluationTimeEnd}
        </if>
        order by coe.create_time desc
    </select>
    
    <select id="selectCarOrderEvaluationByEvaluationId" parameterType="Long" resultMap="CarOrderEvaluationResult">
        <include refid="selectCarOrderEvaluationVo"/>
        where evaluation_id = #{evaluationId}
    </select>
        
    <insert id="insertCarOrderEvaluation" parameterType="CarOrderEvaluation" useGeneratedKeys="true" keyProperty="evaluationId">
        insert into car_order_evaluation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="evaluationScore != null">evaluation_score,</if>
            <if test="evaluationContent != null">evaluation_content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="evaluationScore != null">#{evaluationScore},</if>
            <if test="evaluationContent != null">#{evaluationContent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateCarOrderEvaluation" parameterType="CarOrderEvaluation">
        update car_order_evaluation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="evaluationScore != null">evaluation_score = #{evaluationScore},</if>
            <if test="evaluationContent != null">evaluation_content = #{evaluationContent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where evaluation_id = #{evaluationId}
    </update>

    <delete id="deleteCarOrderEvaluationByEvaluationId" parameterType="Long">
        delete from car_order_evaluation where evaluation_id = #{evaluationId}
    </delete>

    <delete id="deleteCarOrderEvaluationByEvaluationIds" parameterType="String">
        delete from car_order_evaluation where evaluation_id in 
        <foreach item="evaluationId" collection="array" open="(" separator="," close=")">
            #{evaluationId}
        </foreach>
    </delete>

    <!--查询车辆订单评价-->
    <select id="selectCarOrderEvaluationById" parameterType="Long" resultMap="CarOrderEvaluationVoMap">
        select coe.evaluation_id       as evaluationId,
               coe.evaluation_score,
               coe.evaluation_content,
               coe.create_time,
               su.nick_name,
               su.phonenumber,
               co.order_no,
               co.car_name,
               co.company_name,
               coef.evaluation_file_id as evaluationFileId,
               coef.file_name          as fileName,
               coef.file_url           as fileUrl
        from car_order_evaluation coe
                 LEFT JOIN car_order_evalution_file coef
                           on coe.evaluation_id = coef.evaluation_id
                 left join sys_user su on su.user_id = coe.user_id
                 left join car_order co on co.order_id = coe.order_id
        where coe.evaluation_id = #{evaluationId}
    </select>
</mapper>