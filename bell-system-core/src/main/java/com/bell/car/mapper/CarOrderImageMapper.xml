<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bell.car.mapper.CarOrderImageMapper">
    
    <resultMap type="CarOrderImage" id="CarOrderImageResult">
        <result property="imageId"    column="image_id"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectCarOrderImageVo">
        select image_id, file_type, file_url, file_name, create_by, create_time, update_by, update_time, is_del from car_order_image
    </sql>

    <select id="selectCarOrderImageList" parameterType="CarOrderImage" resultMap="CarOrderImageResult">
        <include refid="selectCarOrderImageVo"/>
        <where>  
            <if test="fileType != null "> and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCarOrderImageByImageId" parameterType="Long" resultMap="CarOrderImageResult">
        <include refid="selectCarOrderImageVo"/>
        where image_id = #{imageId}
    </select>
        
    <insert id="insertCarOrderImage" parameterType="CarOrderImage" useGeneratedKeys="true" keyProperty="imageId">
        insert into car_order_image
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileType != null">file_type,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileType != null">#{fileType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateCarOrderImage" parameterType="CarOrderImage">
        update car_order_image
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deleteCarOrderImageByImageId" parameterType="Long">
        delete from car_order_image where image_id = #{imageId}
    </delete>

    <delete id="deleteCarOrderImageByImageIds" parameterType="String">
        delete from car_order_image where image_id in 
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <insert id="addOrderImageBatch" parameterType="CarOrderImage">
        insert into car_order_image
            (order_id, file_type, file_url, file_name, create_by, create_time, is_del)
        values
            <foreach collection="images" index="index" item="item" separator=",">
                (#{item.orderId},
                #{item.fileType},
                #{item.fileUrl},
                #{item.fileName},
                #{item.createBy},
                now(),
                0)
            </foreach>
    </insert>
</mapper>