package com.bell.car.mapper;

import com.bell.car.domain.CarOrder;
import com.bell.car.domain.CarOrderImage;
import com.bell.car.domain.CarOrderTenancyTerm;
import com.bell.car.domain.Dto.CarOrderDTO;
import com.bell.car.domain.Dto.CarOrderPayDto;
import com.bell.car.domain.VO.CarOrderExcelVo;
import com.bell.car.domain.VO.CarOrderRefundVo;
import com.bell.car.domain.VO.CarOrderVo;
import com.bell.common.core.domain.entity.SysUser;
import com.bell.scenic.domain.VO.ScenicSpotVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 后台 - 租车订单mapper
 */
@Repository
public interface CarOrderManagerMapper {

    /**
     * 后台 获取租车订单列表
     * @param carOrderDTO
     * @return
     */
    List<CarOrderVo> getCarOrderList(CarOrderDTO carOrderDTO);

    /**
     * 后台 查询续租订单列表
     * @param orderId
     * @return
     */
    List<CarOrderVo> getTenancyTermList(@Param("orderId") Long orderId);

    /**
     * 导出excel用
     * @param carOrderDTO
     * @return
     */
    List<CarOrderExcelVo> exportExcelInfo(CarOrderDTO carOrderDTO);

    /**
     * 后台 获取租车订单基本信息
     * @param orderId
     * @return
     */
    CarOrder getCarOrderBaseInfo(@Param("orderId") Long orderId);

    /**
     * 后天 获取用户信息
     * @param userId
     * @return
     */
    SysUser getCarOrderUserInfo(@Param("userId") Long userId);

    /**
     * 后台 获取车辆原始订单信息
     * @param orderId
     * @return
     */
    CarOrder getOriginalCarOrderInfo(@Param("orderId") Long orderId);

    /**
     * 后台 获取车辆续租订单信息
     * @param orderId
     * @return
     */
    List<CarOrderTenancyTerm> getEffectiveCarOrderInfo(@Param("orderId") Long orderId);

    /**
     * 后台 获取取消订单数据
     * @param orderId
     * @return
     */
    List<CarOrderRefundVo> getRefundOrderList(@Param("orderId") Long orderId);

    /**
     * 后台 根据类型和订单id查询车辆订单图片
     * @param carOrderImage
     * @return
     */
    List<CarOrderImage> getCarOrderImageList(CarOrderImage carOrderImage);

    /**
     * 后台 退款按钮及已打款按钮数据回显
     * @param orderNo
     * @return
     */
    CarOrderRefundVo getCarOrderInfo(@Param("orderNo") String orderNo);

    /**
     * 后台 原始订单退款
     * @param carOrderPayDto
     * @return
     */
    int refundCarOrder(CarOrderPayDto carOrderPayDto);

    /**
     * 后台 续租订单退款
     * @param carOrderPayDto
     * @return
     */
    int refundCarOrderTenancyTerm(CarOrderPayDto carOrderPayDto);

    /**
     * 后台 根据退单号查询订单信息
     * @param refundNo
     * @return
     */
    CarOrder getCarOrderInfoByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 后台 根据退单号查询续租订单信息
     * @param refundNo
     * @return
     */
    CarOrder getCarOrderTenancyTermInfoByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 后台 原始订单退款，根据退单号查询所有租车订单信息
     * @param refundNo
     * @return
     */
    List<CarOrderPayDto> getCarOrderListByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 后台 续租订单退款，根据退单号查询所有租车订单信息
     * @param refundNo
     * @return
     */
    List<CarOrderPayDto> getCarOrderTenancyTermListByRefundNo(@Param("refundNo") String refundNo);

    /**
     * 后台 原始订单退单回调更新状态
     * @param carOrder
     * @return
     */
    int updateCarOrderRefundInfo(CarOrder carOrder);

    /**
     * 后台 续租单退单回调更新状态
     * @param carOrder
     * @return
     */
    int updateCarOrderTenancyTermRefundInfo(CarOrder carOrder);

    /**
     * 后台 续租单退款回调同时更新原始订单退款状态
     * @param carOrder
     * @return
     */
    int updateCarOrderRefundState(CarOrder carOrder);

    /**
     * 后台 原始订单线下退款，根据订单No查询所有租车订单信息
     * @param orderNo
     * @return
     */
    List<CarOrderPayDto> getCarOrderListByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 后台 续租订单线下退款，根据订单No查询所有租车订单信息
     * @param orderNo
     * @return
     */
    List<CarOrderPayDto> getCarOrderTenancyTermListByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 后台 原始订单线下退款更新状态
     * @param carOrder
     * @return
     */
    int updateCarOrderRefundByOrderId(CarOrder carOrder);

    /**
     * 后台 续租单线下退款更新状态
     * @param carOrder
     * @return
     */
    int updateCarOrderTenancyTermRefundByOrderId(CarOrder carOrder);

    /**
     * 后台 根据订单id获取总退款金额
     * @param orderId
     * @return
     */
    BigDecimal getrefundTotalPriceByOrderId(@Param("orderId") Long orderId);

    /**
     * 后台 获取当前订单id下所有订单的还车时间
     * @param orderId
     * @return
     */
    Date getReturnCarTimeByOrderId(@Param("orderId") Long orderId);

    /**
     * 后台 修改原始订单总订单状态
     * @param carOrderPayDto
     * @return
     */
    int updateCarOrderAllRefundState(CarOrderPayDto carOrderPayDto);

    /**
     * 后台 获取原始订单状态
     * @param orderNo
     * @return
     */
    CarOrderPayDto getCarOrderStateByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 后台 获取续订单状态
     * @param orderNo
     * @return
     */
    CarOrderPayDto getCarOrderTenancyTermStateByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 后台 已打款更新订单状态
     * @param carOrder
     * @return
     */
    int updatePayCarOrderRefundState(CarOrder carOrder);

    /**
     * 定时任务 计算车辆综合分
     * @return
     */
    List<ScenicSpotVo> getCarOrderAvgScore();

    /**
     * 定时任务 更新车辆中和评分
     * @param scenicSpotVo
     * @return
     */
    int updateCarScore(ScenicSpotVo scenicSpotVo);
}
