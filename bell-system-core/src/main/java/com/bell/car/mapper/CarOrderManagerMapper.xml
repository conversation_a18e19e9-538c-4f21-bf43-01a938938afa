<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bell.car.mapper.CarOrderManagerMapper">

    <!-- 后台 获取租车订单列表 -->
    <select id="getCarOrderList" parameterType="CarOrderDTO" resultType="CarOrderVo">
        SELECT
            co.order_id AS orderId,
            co.order_no AS orderNo,
            co.car_name AS carName,
            co.company_name AS companyName,
            bsa.area_name AS townName,
            bsa1.area_name AS areaName,
            su.nick_name AS customerName,
            su.phonenumber AS customerPhoneNum,
            co.take_car_time AS takeCarTime,
            CASE WHEN EXISTS ( SELECT 1 FROM car_order_tenancy_term cott1 WHERE cott1.order_id = co.order_id AND cott1.is_del = 0 ) THEN
            ( SELECT return_car_date FROM car_order_tenancy_term cott2 WHERE cott2.order_id = co.order_id AND cott2.is_del = 0 ORDER BY return_car_date DESC LIMIT 1 ) ELSE co.return_car_time
            END AS returnCarTime,
            co.order_day + IFNULL((
                SELECT
                    SUM( cott3.order_day )
                FROM
                    car_order_tenancy_term cott3
                WHERE
                    cott3.order_id = co.order_id
                AND cott3.is_del = 0
                GROUP BY
                    cott3.order_id
            ), 0) AS orderDay,
            co.price AS price,
            co.order_total_price AS orderTotalPrice,
            co.total_price AS totalPrice,
            co.create_time AS createTime,
            co.state AS state,
            co.refund_state AS refundState,
            CASE WHEN EXISTS (
                SELECT 1
                FROM car_order_tenancy_term cott
                WHERE cott.order_id = co.order_id AND cott.is_del = 0 AND cott.state != 'created'
            ) THEN TRUE ELSE FALSE END AS hasChildren,
            co.all_order_refund_state AS allOrderRefundState
        FROM
            car_order co
        LEFT JOIN bell_sea_area bsa ON co.area_id = bsa.area_id
        LEFT JOIN bell_sea_area bsa1 ON bsa.parent_id = bsa1.area_id
        LEFT JOIN sys_user su ON co.user_id = su.user_id
        WHERE
            co.is_del = 0
        <if test="orderNo != null">
            AND co.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="areaId != null">
            AND (co.area_id = #{areaId} OR co.area_id IN (
                SELECT
                    bsa3.area_id
                FROM
                    bell_sea_area bsa2
                LEFT JOIN bell_sea_area bsa3 ON bsa2.area_id = bsa3.parent_id AND bsa3.area_level = 2
                WHERE
                    bsa2.area_id = #{areaId}
                AND bsa2.is_del = 0
            ))
        </if>
        <if test="companyName != null and companyName != ''">
            AND co.company_name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        <if test="carName != null and carName != ''">
            AND co.car_name LIKE CONCAT('%', #{carName}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            AND su.nick_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="customerPhoneNum != null and customerPhoneNum != ''">
            AND su.phonenumber LIKE CONCAT('%', #{customerPhoneNum}, '%')
        </if>
        <if test="state != null and state != ''">
            AND co.state = #{state}
        </if>
        <if test="refundState != null">
            AND co.refund_state = #{refundState}
        </if>
        <if test="startDate != null">
            AND DATE(co.create_time) &gt;= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            AND DATE(co.create_time) &lt;= DATE(#{endDate})
        </if>
        <if test="companyId != null">
            AND co.company_id = #{companyId}
        </if>
        ORDER BY co.create_time DESC
    </select>

    <!-- 后台 查询续租订单列表 -->
    <select id="getTenancyTermList" parameterType="java.lang.Long" resultType="CarOrderVo">
        SELECT
            cott.term_id AS orderId,
            cott.order_no AS orderNo,
            co.car_name AS carName,
            co.company_name AS companyName,
            bsa.area_name AS townName,
            bsa1.area_name AS areaName,
            su.nick_name AS customerName,
            su.phonenumber AS customerPhoneNum,
            NULL AS takeCarTime,
            cott.return_car_date AS returnCarTime,
            cott.order_day AS orderDay,
            cott.price AS price,
            cott.total_price AS orderTotalPrice,
            cott.total_price AS totalPrice,
            cott.create_time AS createTime,
            cott.state AS state,
            cott.refund_state AS refundState,
            FALSE AS hasChildren,
            NULL AS allOrderRefundState,
            ROW_NUMBER() OVER (ORDER BY cott.create_time ASC) AS orderIndex
        FROM
            car_order_tenancy_term cott
        LEFT JOIN car_order co ON cott.order_id = co.order_id
        LEFT JOIN bell_sea_area bsa ON co.area_id = bsa.area_id
        LEFT JOIN bell_sea_area bsa1 ON bsa.parent_id = bsa1.area_id
        LEFT JOIN sys_user su ON co.user_id = su.user_id
        WHERE
            cott.is_del = 0
        AND cott.state != 'created'
        AND cott.order_id = #{orderId}
        ORDER BY cott.create_time ASC
    </select>

    <!-- 导出excel用 -->
    <select id="exportExcelInfo" parameterType="CarOrderDTO" resultType="CarOrderExcelVo">
        SELECT
            co.order_no AS orderNo,
            co.state AS state,
            co.create_time AS createTime,
            CONCAT(su.real_name,' ',su.phonenumber, ' ', su.cert_no) AS customerInfo,
            co.car_name AS carName,
            co.company_name AS companyName,
            co.take_car_time AS takeCarTime,
            CASE WHEN EXISTS ( SELECT 1 FROM car_order_tenancy_term cott1 WHERE cott1.order_id = co.order_id AND cott1.is_del = 0 ) THEN
                     ( SELECT return_car_date FROM car_order_tenancy_term cott2 WHERE cott2.order_id = co.order_id AND cott2.is_del = 0 ORDER BY return_car_date DESC LIMIT 1 ) ELSE co.return_car_time
            END AS nowReturnCarTime,
            co.return_car_time AS returnCarTime,
            co.order_day + IFNULL((
                SELECT
                    SUM( cott5.order_day )
                FROM
                    car_order_tenancy_term cott5
                WHERE
                    cott5.order_id = co.order_id
                AND cott5.is_del = 0
                GROUP BY
                    cott5.order_id
            ), 0) AS orderDay,
            co.price AS price,
            co.total_price AS totalPrice,
            co.order_total_price AS orderTotalPrice,
            co.car_no AS carNo,
            co.tack_car_name AS takeCarName,
            co.return_car_name AS returnCarName,
            ( SELECT GROUP_CONCAT(CONCAT('续单下单日：', cott3.create_time, '，续单还车日：',cott3.return_car_date, '，续单租期：', cott3.order_day, '，续单总价：', cott3.total_price), ';')
                FROM car_order_tenancy_term cott3 WHERE cott3.order_id = co.order_id AND cott3.is_del = 0 GROUP BY cott3.order_id) AS orderInfo,
            co.refund_state AS refundState,
            co.refund_date AS refundDate,
            co.refund_amount AS refundAmount,
            co.platform_refund_reason AS platFormRefundReason,
            ( SELECT GROUP_CONCAT(CONCAT('退款状态：', (CASE cott4.refund_state WHEN 0 THEN '未退款' WHEN 1 THEN '已退款' WHEN 2 THEN '退款中' WHEN 3 THEN '退款失败' ELSE '' END),
                '，取消时间：',cott4.refund_time, '，退款金额：', cott4.refund_amount, '，平台退款原因：', cott4.platform_refund_reason), ';')
                FROM car_order_tenancy_term cott4 WHERE cott4.order_id = co.order_id AND cott4.is_del = 0 GROUP BY cott4.order_id) AS refundOrderInfo,
            co.contract_time AS contractTime,
            su1.real_name AS contractRealName,
            su1.user_name AS contractUserName,
            (SELECT GROUP_CONCAT(coi1.file_url, ';') FROM car_order_image coi1 WHERE coi1.order_id = co.order_id AND coi1.file_type = 1 AND coi1.is_del = 0 GROUP BY coi1.order_id) AS contractUrl,
            co.check_time AS checkTime,
            su2.real_name AS checkRealName,
            su2.user_name AS checkUserName,
            (SELECT GROUP_CONCAT(coi2.file_url, ';') FROM car_order_image coi2 WHERE coi2.order_id = co.order_id AND coi2.file_type = 2 AND coi2.is_del = 0 GROUP BY coi2.order_id) AS checkUrl,
            co.deposit AS deposit,
            co.return_desposit AS returnDeposit,
            co.return_desposit_time AS returnDepositTime,
            co.desposit_reason AS despositReason
        FROM
            car_order co
        LEFT JOIN sys_user su ON co.user_id = su.user_id
        LEFT JOIN sys_user su1 ON co.contract_user_id = su1.user_id
        LEFT JOIN sys_user su2 ON co.check_user_id = su2.user_id
        WHERE
            co.is_del = 0
        <if test="orderNo != null">
            AND co.order_no LIKE CONCAT('%', #{orderNo}, '%')
        </if>
        <if test="areaId != null">
            AND (co.area_id = #{areaId} OR co.area_id IN (
                SELECT
                    bsa3.area_id
                FROM
                    bell_sea_area bsa2
                LEFT JOIN bell_sea_area bsa3 ON bsa2.area_id = bsa3.parent_id AND bsa3.area_level = 2
                WHERE
                    bsa2.area_id = #{areaId}
                AND bsa2.is_del = 0
            ))
        </if>
        <if test="companyName != null and companyName != ''">
            AND co.company_name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        <if test="carName != null and carName != ''">
            AND co.car_name LIKE CONCAT('%', #{carName}, '%')
        </if>
        <if test="customerName != null and customerName != ''">
            AND su.nick_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="customerPhoneNum != null and customerPhoneNum != ''">
            AND su.phonenumber LIKE CONCAT('%', #{customerPhoneNum}, '%')
        </if>
        <if test="state != null and state != ''">
            AND co.state = #{state}
        </if>
        <if test="refundState != null">
            AND co.refund_state = #{refundState}
        </if>
        <if test="startDate != null">
            AND DATE(co.create_time) &lt;= DATE(#{startDate})
        </if>
        <if test="endDate != null">
            AND DATE(co.create_time) &gt;= DATE(#{endDate})
        </if>
        <if test="companyId != null">
            AND co.company_id = #{companyId}
        </if>
        ORDER BY co.create_time DESC
    </select>

    <!-- *********************************后台 订单详情sql start********************************* -->
    <!-- 获取租车订单基本信息 -->
    <select id="getCarOrderBaseInfo" parameterType="java.lang.Long" resultType="CarOrder">
        SELECT
            co.order_id AS orderId,
            co.order_no AS orderNo,
            co.state AS state,
            co.create_time AS createTime,
            co.contract_time AS contractTime,
            co.contract_user_id AS contractUserId,
            co.check_user_id AS checkUserId,
            co.check_time AS checkTime,
            co.deposit AS deposit,
            co.return_desposit AS returnDesposit,
            co.return_desposit_time AS returnDespositTime,
            co.desposit_reason AS despositReason,
            co.user_id AS userId,
            co.car_name AS carName,
            co.company_name AS companyName,
            co.take_car_time AS takeCarTime,
            CASE WHEN EXISTS ( SELECT 1 FROM car_order_tenancy_term cott1 WHERE cott1.order_id = co.order_id AND cott1.is_del = 0 ) THEN
            ( SELECT cott2.return_car_date FROM car_order_tenancy_term cott2 WHERE cott2.order_id = co.order_id AND cott2.is_del = 0 ORDER BY cott2.return_car_date DESC LIMIT 1 ) ELSE co.return_car_time
            END AS returnCarTime,
            co.order_day + IFNULL((
                SELECT
                    SUM( cott3.order_day )
                FROM
                    car_order_tenancy_term cott3
                WHERE
                    cott3.order_id = co.order_id
                AND cott3.is_del = 0
                GROUP BY
                    cott3.order_id
            ), 0) AS orderDay,
            co.price AS price,
            co.total_price AS totalPrice,
            co.car_no AS carNo,
            co.tack_car_name AS tackCarName,
            co.return_car_name AS returnCarName,
            co.all_order_refund_state AS allOrderRefundState
        FROM
            car_order co
        WHERE
            is_del = 0
        AND order_id = #{orderId}
    </select>

    <!-- 后台 获取用户信息 -->
    <select id="getCarOrderUserInfo" parameterType="java.lang.Long" resultType="SysUser">
        SELECT
            user_name AS userName,
            nick_name AS nickName,
            real_name AS realName,
            phonenumber AS phonenumber,
            cert_no AS certNo
        FROM
            sys_user
        WHERE
            del_flag = 0
        AND user_id = #{userId}
    </select>

    <!-- 后台 获取车辆原始订单信息 -->
    <select id="getOriginalCarOrderInfo" parameterType="java.lang.Long" resultType="CarOrder">
        SELECT
            take_car_time AS takeCarTime,
            return_car_time AS returnCarTime,
            order_day AS orderDay,
            order_total_price AS orderTotalPrice,
            create_time AS createTime
        FROM
            car_order
        WHERE
            is_del = 0
        AND order_id = #{orderId}
    </select>

    <!-- 后台 获取车辆续租订单信息 -->
    <select id="getEffectiveCarOrderInfo" parameterType="java.lang.Long" resultType="CarOrderTenancyTerm">
        SELECT
            return_car_date AS returnCarDate,
            order_day AS orderDay,
            total_price AS totalPrice,
            create_time AS createTime
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
        AND order_id = #{orderId}
        ORDER BY create_time ASC
    </select>

    <!-- 后台 获取取消订单数据 -->
    <select id="getRefundOrderList" parameterType="java.lang.Long" resultType="CarOrderRefundVo">
        SELECT
            1 AS orderType,
            co.refund_state AS refundState,
            co.refund_date AS cancelTime,
            co.refund_amount AS refundPrice,
            co.platform_refund_reason AS platformRefundReason
        FROM
            car_order co
        WHERE
            co.is_del = 0
        AND co.order_id = #{orderId}
        AND co.refund_state != 0
        UNION ALL
        SELECT
            2 AS orderType,
            cott.refund_state AS refundState,
            cott.refund_time AS cancelTime,
            cott.refund_amount AS refundPrice,
            cott.platform_refund_reason AS platformRefundReason
        FROM
            car_order_tenancy_term cott
        WHERE
            cott.is_del = 0
        AND cott.order_id = #{orderId}
        AND cott.refund_state != 0
    </select>

    <!-- 后台 查询租车图片 -->
    <select id="getCarOrderImageList" parameterType="CarOrderImage" resultType="CarOrderImage">
        SELECT
            image_id AS imageId,
            order_id AS orderId,
            file_type AS fileType,
            file_url AS fileUrl
        FROM
            car_order_image
        WHERE
            is_del = 0
        AND order_id = #{orderId}
        AND file_type = #{fileType}
    </select>
    <!-- **********************************后台 订单详情sql end********************************** -->
    <!-- 后台 退款按钮及已打款按钮数据回显 -->
    <select id="getCarOrderInfo" parameterType="java.lang.String" resultType="CarOrderRefundVo">
        SELECT
            1 AS orderType,
            co.order_id AS orderId,
            co.order_no AS orderNo,
            co.take_car_time AS takeCarTime,
            co.return_car_time AS returnCarTime,
            co.order_day AS orderDay,
            co.order_total_price AS orderTotalPrice,
            co.refund_amount AS refundPrice,
            co.refund_state AS refundState,
            co.platform_refund_reason AS platformRefundReason
        FROM
            car_order co
        WHERE
            co.is_del = 0
        AND co.order_no = #{orderNo}
        UNION ALL
        SELECT
            2 AS orderType,
            cott.term_id AS orderId,
            cott.order_no AS orderNo,
            NULL AS takeCarTime,
            cott.return_car_date AS returnCarTime,
            cott.order_day AS orderDay,
            cott.total_price AS orderTotalPrice,
            cott.refund_amount AS refundPrice,
            cott.refund_state AS refundState,
            cott.platform_refund_reason AS platformRefundReason
        FROM
            car_order_tenancy_term cott
        WHERE
            cott.is_del = 0
        AND cott.order_no = #{orderNo}
    </select>

    <!-- 后台 原始订单退款 -->
    <update id="refundCarOrder" parameterType="CarOrderPayDto">
        UPDATE car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="refundState != null">refund_state = #{refundState},</if>
            <if test="refundPrice != null">refund_amount = #{refundPrice},</if>
            <if test="refundUserId != null">refund_user_id = #{refundUserId},</if>
            refund_date = NOW(),
            <if test="refundNo != null and refundNo != ''">refund_no = #{refundNo},</if>
            update_time = NOW(),
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="platformRefundReason != null and platformRefundReason != ''">platform_refund_reason = #{platformRefundReason},</if>
            <if test="allOrderRefundState != null">all_order_refund_state = #{allOrderRefundState},</if>
            <if test="refundTotalPrice != null">refund_total_price = #{refundTotalPrice},</if>
        </trim>
        WHERE
            order_no = #{orderNo}
    </update>

    <!-- 后台 续租订单退款 -->
    <update id="refundCarOrderTenancyTerm" parameterType="CarOrderPayDto">
        UPDATE car_order_tenancy_term
        <trim prefix="SET" suffixOverrides=",">
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="refundState != null">refund_state = #{refundState},</if>
            <if test="refundPrice != null">refund_amount = #{refundPrice},</if>
            <if test="refundUserId != null">refund_user_id = #{refundUserId},</if>
            refund_time = NOW(),
            <if test="refundNo != null and refundNo != ''">refund_no = #{refundNo},</if>
            update_time = NOW(),
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="platformRefundReason != null and platformRefundReason != ''">platform_refund_reason = #{platformRefundReason},</if>
        </trim>
        WHERE
            order_no = #{orderNo}
    </update>

    <!-- 后台 根据退单号查询租车原始订单信息 -->
    <select id="getCarOrderInfoByRefundNo" parameterType="java.lang.String" resultType="CarOrder">
        SELECT
            co.order_id AS orderId,
            co.order_no AS orderNo,
            co.refund_state AS refundState,
            co.state AS state
        FROM
            car_order co
        WHERE
            co.is_del = 0
        AND co.refund_no = #{refundNo}
    </select>

    <!-- 后台 根据退单号查询租车续租订单信息 -->
    <select id="getCarOrderTenancyTermInfoByRefundNo" parameterType="java.lang.String" resultType="CarOrder">
        SELECT
            cott.term_id AS orderId,
            cott.order_no AS orderNo,
            cott.refund_state AS refundState,
            cott.state AS state
        FROM
            car_order_tenancy_term cott
        WHERE
            cott.is_del = 0
        AND cott.refund_no = #{refundNo}
    </select>

    <!-- 后台 原始订单退款，根据退单号查询所有租车订单信息 -->
    <select id="getCarOrderListByRefundNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            1 AS orderType,
            order_id AS orderId,
            order_no AS orderNo,
            refund_state AS refundState,
            state AS state
        FROM
            car_order
        WHERE
            is_del = 0
        AND refund_no = #{refundNo}
        UNION ALL
        SELECT
            2  AS orderType,
            order_id AS orderId,
            order_no AS order_no,
            refund_state AS refundState,
            state AS state
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
        AND order_id IN (
            SELECT order_id FROM car_order
            WHERE refund_no = #{refundNo} AND is_del = 0
        )
    </select>

    <!-- 后台 续租订单退款，根据退单号查询所有租车订单信息 -->
    <select id="getCarOrderTenancyTermListByRefundNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            1 AS orderType,
            order_id AS orderId,
            order_no AS orderNo,
            refund_state AS refundState,
            state AS state
        FROM
            car_order
        WHERE
            is_del = 0
        AND order_id IN (
            SELECT order_id FROM car_order_tenancy_term
            WHERE refund_no = #{refundNo} AND is_del = 0
        )
        UNION ALL
        SELECT
            2  AS orderType,
            order_id AS orderId,
            order_no AS order_no,
            refund_state AS refundState,
            state AS state
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
        AND order_id IN ( SELECT order_id FROM car_order_tenancy_term WHERE refund_no = #{refundNo} AND is_del = 0 )
    </select>

    <!-- 后台 原始订单退单回调更新状态 -->
    <update id="updateCarOrderRefundInfo" parameterType="CarOrder">
        UPDATE car_order SET
            refund_date = NOW(),
            refund_amount = #{refundAmount},
            state = #{state},
            refund_state = #{refundState},
            update_by = #{updateBy},
            update_time = NOW(),
            all_order_refund_state = #{allOrderRefundState},
            refund_total_price = #{refundTotalPrice},
            new_refund_date = NOW()
        WHERE
            order_id = #{orderId}
    </update>

    <!-- 后台 续租单退单回调更新状态 -->
    <update id="updateCarOrderTenancyTermRefundInfo" parameterType="CarOrder">
        UPDATE car_order_tenancy_term SET
            refund_time = NOW(),
            refund_amount = #{refundAmount},
            state = #{state},
            refund_state = #{refundState},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE
            term_id = #{orderId}
    </update>

    <!-- 后台 续租单退款回调同时更新原始订单退款状态 -->
    <update id="updateCarOrderRefundState" parameterType="CarOrder">
        UPDATE car_order SET
            all_order_refund_state = #{allOrderRefundState},
            refund_total_price = #{refundTotalPrice},
            new_refund_date = NOW(),
            return_new_car_time = #{returnNewCarTime}
        WHERE
            order_id = #{orderId}
    </update>

    <!-- 后台 原始订单线下退款，根据订单No查询所有租车订单信息 -->
    <select id="getCarOrderListByOrderNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            1 AS orderType,
            order_id AS orderId,
            order_no AS orderNo,
            refund_state AS refundState,
            state AS state
        FROM
            car_order
        WHERE
            is_del = 0
        AND order_no = #{orderNo}
        UNION ALL
        SELECT
            2  AS orderType,
            order_id AS orderId,
            order_no AS order_no,
            refund_state AS refundState,
            state AS state
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
          AND order_id IN (
            SELECT order_id FROM car_order
            WHERE order_no = #{orderNo} AND is_del = 0
        )
    </select>

    <!-- 后台 续租订单线下退款，根据订单No查询所有租车订单信息 -->
    <select id="getCarOrderTenancyTermListByOrderNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            1 AS orderType,
            order_id AS orderId,
            order_no AS orderNo,
            refund_state AS refundState,
            state AS state
        FROM
            car_order
        WHERE
            is_del = 0
        AND order_id IN (
            SELECT order_id FROM car_order_tenancy_term
            WHERE order_no = #{orderNo} AND is_del = 0
        )
        UNION ALL
        SELECT
            2  AS orderType,
            order_id AS orderId,
            order_no AS order_no,
            refund_state AS refundState,
            state AS state
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
        AND order_id IN (
            SELECT order_id FROM car_order_tenancy_term
            WHERE order_no = #{orderNo} AND is_del = 0
        )
    </select>

    <!-- 后台 原始订单线下退款更新状态 -->
    <update id="updateCarOrderRefundByOrderId" parameterType="CarOrder">
        UPDATE car_order SET
         refund_date = NOW(),
         refund_amount = #{refundAmount},
         refund_state = #{refundState},
         update_by = #{updateBy},
         update_time = NOW(),
         all_order_refund_state = #{allOrderRefundState}
        WHERE
            order_id = #{orderId}
    </update>

    <!-- 后台 续租单线下退款更新状态 -->
    <update id="updateCarOrderTenancyTermRefundByOrderId" parameterType="CarOrder">
        UPDATE car_order_tenancy_term SET
          refund_time = NOW(),
          refund_amount = #{refundAmount},
          refund_state = #{refundState},
          update_by = #{updateBy},
          update_time = NOW()
        WHERE
            term_id = #{orderId}
    </update>

    <!-- 后台 根据订单id获取总退款金额 -->
    <select id="getrefundTotalPriceByOrderId" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        SELECT
            refund_total_price AS refundTotalPrice
        FROM
            car_order
        WHERE
            order_id = #{orderId}
        AND is_del = 0
    </select>

    <!-- 后台 获取当前订单id下所有订单的还车时间 -->
    <select id="getReturnCarTimeByOrderId" parameterType="java.lang.Long" resultType="java.util.Date">
        SELECT MAX(returnCarTime) AS latestReturnTime FROM (
           SELECT return_car_time AS returnCarTime
           FROM car_order
           WHERE is_del = 0 AND order_id = #{orderId}
           UNION ALL
           SELECT return_car_date AS returnCarTime
           FROM car_order_tenancy_term
           WHERE is_del = 0 AND order_id = #{orderId} AND state != 'cancel'
        ) AS all_times
    </select>

    <!-- 后台 修改原始订单总订单状态 -->
    <update id="updateCarOrderAllRefundState" parameterType="CarOrderPayDto">
        UPDATE car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="allOrderRefundState != null">all_order_refund_state = #{allOrderRefundState},</if>
            <if test="refundTotalPrice != null">refund_total_price = #{refundTotalPrice},</if>
            <if test="returnNewCarTime != null">return_new_car_time = #{returnNewCarTime},</if>
        </trim>
        WHERE order_no = #{orderNo}
    </update>

    <!-- 后台 获取原始订单状态 -->
    <select id="getCarOrderStateByOrderNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            1 AS orderType,
            order_id AS orderId,
            order_no AS orderNo,
            refund_state AS refundState,
            state AS state
        FROM
            car_order
        WHERE
            is_del = 0
        AND order_no = #{orderNo}
    </select>

    <!-- 后台 获取续订单状态 -->
    <select id="getCarOrderTenancyTermStateByOrderNo" parameterType="java.lang.String" resultType="CarOrderPayDto">
        SELECT
            2  AS orderType,
            order_id AS orderId,
            order_no AS order_no,
            refund_state AS refundState,
            state AS state
        FROM
            car_order_tenancy_term
        WHERE
            is_del = 0
        AND order_no = #{orderNo}
    </select>

    <!-- 后台 已打款更新订单状态 -->
    <update id="updatePayCarOrderRefundState" parameterType="CarOrder">
        UPDATE car_order SET
            all_order_refund_state = #{allOrderRefundState},
            new_refund_date = NOW()
        WHERE
            order_id = #{orderId}
    </update>

    <!-- 定时任务 计算车辆综合分 -->
    <select id="getCarOrderAvgScore" resultType="ScenicSpotVo">
        SELECT
            co.car_id AS scenicSpotId,
            AVG(coe.evaluation_score) AS evaluationScore
        FROM
            car_order_evaluation coe
        LEFT JOIN car_order co ON coe.order_id = co.order_id
        WHERE
            coe.is_del = 0
        GROUP BY co.car_id
    </select>

    <!-- 定时任务 更新车辆评分 -->
    <update id="updateCarScore" parameterType="ScenicSpotVo">
        UPDATE car_base_info SET evaluation_score = #{evaluationScore} WHERE car_id = #{scenicSpotId}
    </update>
</mapper>