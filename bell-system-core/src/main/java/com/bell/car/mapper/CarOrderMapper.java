package com.bell.car.mapper;

import java.util.List;

import com.bell.car.domain.CarBaseInfo;
import com.bell.car.domain.CarOrder;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bell.car.domain.VO.*;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;
import com.bell.car.domain.Dto.CarAvailabilityRequest;
import org.springframework.stereotype.Repository;

/**
 * 租车订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Repository
public interface CarOrderMapper extends MPJBaseMapper<CarOrder>
{
    List<String> checkCarAvailability(CarAvailabilityRequest carAvailabilityRequest);

    /**
     * 检查车辆可用性（排除指定订单）
     * @param carAvailabilityRequest 检查请求参数
     * @return 不可用日期列表
     */
    List<String> checkCarAvailabilityExcludeOrder(CarAvailabilityRequest carAvailabilityRequest);

    /**
     * 公众号 查询租车订单列表
     */
    public List<CarOrderGzhListVo> getOrderList(@Param("userId") Long userId, @Param("carBaseInfos") List<CarBaseInfo> carBaseInfos, @Param("state") String state);

    /**
     * 公众号 查询租车订单详情
     */
    public CarOrderGzhDetailVo selectGzhCarOrderByOrderId(@Param("orderId") Long orderId);

    CarOrderDetailVO selectAppCarOrderByOrderId(@Param("orderId") Long orderId, @Param("userId") Long userId);
    
    /**
     * 获取车辆订单详情
     * 
     * @param orderId 订单ID
     * @return 车辆订单详情
     */
    CarOrderDetailsVO getCarOrderDetails(@Param("orderId") Long orderId);

    /**
     * 租车下单查询短信信息
     */
    CarOrderSmsVO getCarOrderSmsInfo(@Param("orderNo") String orderNo);

    /**
     * 租车下单批量查询短信信息
     */
    List<CarOrderSmsVO> getCarOrderSmsInfoList(@Param("orderIds") List<Long> orderIds);
}
