<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bell.car.mapper.CarOrderMapper">
    
    <resultMap type="CarOrder" id="CarOrderResult">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="state"    column="state"    />
        <result property="carId"    column="car_id"    />
        <result property="carName"    column="car_name"    />
        <result property="carNo"    column="car_no"    />
        <result property="companyName"    column="company_name"    />
        <result property="modelName"    column="model_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="price"    column="price"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="deposit"    column="deposit"    />
        <result property="volume"    column="volume"    />
        <result property="oilType"    column="oil_type"    />
        <result property="transmission"    column="transmission"    />
        <result property="tankCapacity"    column="tank_capacity"    />
        <result property="seatQuantity"    column="seat_quantity"    />
        <result property="doorQuantity"    column="door_quantity"    />
        <result property="fuelConsumption"    column="fuel_consumption"    />
        <result property="carAge"    column="car_age"    />
        <result property="powerType"    column="power_type"    />
        <result property="refundState"    column="refund_state"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundUserId"    column="refund_user_id"    />
        <result property="refundDate"    column="refund_date"    />
        <result property="refundNo"    column="refund_no"    />
        <result property="payTime"    column="pay_time"    />
        <result property="takeCarTime"    column="take_car_time"    />
        <result property="returnCarTime"    column="return_car_time"    />
        <result property="orderDay"    column="order_day"    />
        <result property="contractUserId"    column="contract_user_id"    />
        <result property="contractTime"    column="contract_time"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="checkTime"    column="check_time"    />
        <result property="returnDesposit"    column="return_desposit"    />
        <result property="returnDespositTime"    column="return_desposit_time"    />
        <result property="despositReason"    column="desposit_reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectCarOrderVo">
        select order_id, order_no, user_id, state, car_id, car_name, car_no, company_name, model_name, area_id, price, total_price, deposit, volume, oil_type, transmission, tank_capacity, seat_quantity, door_quantity, fuel_consumption, car_age, power_type, refund_state, refund_amount, refund_user_id, refund_date, refund_no, pay_time, take_car_time, return_car_time, order_day, contract_user_id, contract_time, check_user_id, check_time, return_desposit, return_desposit_time, desposit_reason, create_by, create_time, update_by, update_time, is_del from car_order
    </sql>

    <select id="selectCarOrderList" parameterType="CarOrder" resultMap="CarOrderResult">
        <include refid="selectCarOrderVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="carId != null "> and car_id = #{carId}</if>
            <if test="carName != null  and carName != ''"> and car_name like concat('%', #{carName}, '%')</if>
            <if test="carNo != null  and carNo != ''"> and car_no = #{carNo}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="areaId != null "> and area_id = #{areaId}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="totalPrice != null "> and total_price = #{totalPrice}</if>
            <if test="deposit != null "> and deposit = #{deposit}</if>
            <if test="volume != null  and volume != ''"> and volume = #{volume}</if>
            <if test="oilType != null  and oilType != ''"> and oil_type = #{oilType}</if>
            <if test="transmission != null  and transmission != ''"> and transmission = #{transmission}</if>
            <if test="tankCapacity != null  and tankCapacity != ''"> and tank_capacity = #{tankCapacity}</if>
            <if test="seatQuantity != null "> and seat_quantity = #{seatQuantity}</if>
            <if test="doorQuantity != null "> and door_quantity = #{doorQuantity}</if>
            <if test="fuelConsumption != null  and fuelConsumption != ''"> and fuel_consumption = #{fuelConsumption}</if>
            <if test="carAge != null "> and car_age = #{carAge}</if>
            <if test="powerType != null  and powerType != ''"> and power_type = #{powerType}</if>
            <if test="refundState != null "> and refund_state = #{refundState}</if>
            <if test="refundAmount != null "> and refund_amount = #{refundAmount}</if>
            <if test="refundUserId != null "> and refund_user_id = #{refundUserId}</if>
            <if test="refundDate != null "> and refund_date = #{refundDate}</if>
            <if test="refundNo != null  and refundNo != ''"> and refund_no = #{refundNo}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="takeCarTime != null "> and take_car_time = #{takeCarTime}</if>
            <if test="returnCarTime != null "> and return_car_time = #{returnCarTime}</if>
            <if test="orderDay != null "> and order_day = #{orderDay}</if>
            <if test="contractUserId != null "> and contract_user_id = #{contractUserId}</if>
            <if test="contractTime != null "> and contract_time = #{contractTime}</if>
            <if test="checkUserId != null "> and check_user_id = #{checkUserId}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
            <if test="returnDesposit != null "> and return_desposit = #{returnDesposit}</if>
            <if test="returnDespositTime != null "> and return_desposit_time = #{returnDespositTime}</if>
            <if test="despositReason != null  and despositReason != ''"> and desposit_reason = #{despositReason}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCarOrderByOrderId" parameterType="Long" resultMap="CarOrderResult">
        <include refid="selectCarOrderVo"/>
        where order_id = #{orderId}
    </select>
    
    <resultMap id="CarAvailabilityResultMap" type="com.bell.car.domain.VO.CarAvailabilityResultVO">
        <result property="carId" column="car_id" />
        <result property="carName" column="car_name" />
        <result property="totalQuantity" column="total_quantity" />
        <result property="remainingQuantity" column="remaining_quantity" />
        <result property="inventoryStatus" column="inventory_status" />
        <collection property="unavailableDays" ofType="java.lang.String">
            <constructor>
                <arg column="unavailable_day" />
            </constructor>
        </collection>
    </resultMap>
    
    <select id="checkCarAvailability" parameterType="com.bell.car.domain.Dto.CarAvailabilityRequest" resultType="java.lang.String">
        WITH DailyOccupancy AS (
            SELECT
                coi.order_date,
                COUNT(DISTINCT coi.order_id) AS occupied_count
            FROM
                car_order_inventory coi
                    INNER JOIN car_order co ON coi.order_id = co.order_id
                    AND co.is_del = 0
                    AND co.state IN ('created', 'paid', 'driving')
                    LEFT JOIN car_order_tenancy_term cot ON coi.term_id = cot.term_id
                    AND cot.is_del = 0
            WHERE
                coi.car_id = #{carId}
              AND coi.is_del = 0
              AND coi.order_date BETWEEN #{startTime} AND #{endTime}
              AND (
                cot.term_id IS NULL
                    OR cot.state = 'driving'
                )
            GROUP BY
                coi.order_date
        ),
             CarQuantity AS (
                 SELECT quantity FROM car_base_info WHERE car_id = #{carId} AND is_del = 0
             )
        SELECT
            DO.order_date
        FROM
            DailyOccupancy DO
                CROSS JOIN CarQuantity CQ
        WHERE
            DO.occupied_count >= CQ.quantity
        ORDER BY
            DO.order_date
    </select>

    <select id="checkCarAvailabilityExcludeOrder" parameterType="com.bell.car.domain.Dto.CarAvailabilityRequest" resultType="java.lang.String">
        WITH DailyOccupancy AS (
            SELECT
                coi.order_date,
                COUNT(DISTINCT coi.order_id) AS occupied_count
            FROM
                car_order_inventory coi
                    INNER JOIN car_order co ON coi.order_id = co.order_id
                    AND co.is_del = 0
                    AND co.state IN ('created', 'paid', 'driving')
                    <if test="excludeOrderId != null">
                        AND co.order_id != #{excludeOrderId}
                    </if>
                    LEFT JOIN car_order_tenancy_term cot ON coi.term_id = cot.term_id
                    AND cot.is_del = 0
            WHERE
                coi.car_id = #{carId}
              AND coi.is_del = 0
              AND coi.order_date BETWEEN #{startTime} AND #{endTime}
              AND (
                cot.term_id IS NULL
                    OR cot.state = 'driving'
                )
            GROUP BY
                coi.order_date
        ),
             CarQuantity AS (
                 SELECT quantity FROM car_base_info WHERE car_id = #{carId} AND is_del = 0
             )
        SELECT
            DO.order_date
        FROM
            DailyOccupancy DO
                CROSS JOIN CarQuantity CQ
        WHERE
            DO.occupied_count >= CQ.quantity
        ORDER BY
            DO.order_date
    </select>


        
    <insert id="insertCarOrder" parameterType="CarOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into car_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">order_no,</if>
            <if test="userId != null">user_id,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="carId != null">car_id,</if>
            <if test="carName != null">car_name,</if>
            <if test="carNo != null">car_no,</if>
            <if test="companyName != null">company_name,</if>
            <if test="modelName != null">model_name,</if>
            <if test="areaId != null">area_id,</if>
            <if test="price != null">price,</if>
            <if test="totalPrice != null">total_price,</if>
            <if test="deposit != null">deposit,</if>
            <if test="volume != null">volume,</if>
            <if test="oilType != null">oil_type,</if>
            <if test="transmission != null">transmission,</if>
            <if test="tankCapacity != null">tank_capacity,</if>
            <if test="seatQuantity != null">seat_quantity,</if>
            <if test="doorQuantity != null">door_quantity,</if>
            <if test="fuelConsumption != null">fuel_consumption,</if>
            <if test="carAge != null">car_age,</if>
            <if test="powerType != null">power_type,</if>
            <if test="refundState != null">refund_state,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="refundUserId != null">refund_user_id,</if>
            <if test="refundDate != null">refund_date,</if>
            <if test="refundNo != null">refund_no,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="takeCarTime != null">take_car_time,</if>
            <if test="returnCarTime != null">return_car_time,</if>
            <if test="orderDay != null">order_day,</if>
            <if test="contractUserId != null">contract_user_id,</if>
            <if test="contractTime != null">contract_time,</if>
            <if test="checkUserId != null">check_user_id,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="returnDesposit != null">return_desposit,</if>
            <if test="returnDespositTime != null">return_desposit_time,</if>
            <if test="despositReason != null">desposit_reason,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null">#{orderNo},</if>
            <if test="userId != null">#{userId},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="carId != null">#{carId},</if>
            <if test="carName != null">#{carName},</if>
            <if test="carNo != null">#{carNo},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="areaId != null">#{areaId},</if>
            <if test="price != null">#{price},</if>
            <if test="totalPrice != null">#{totalPrice},</if>
            <if test="deposit != null">#{deposit},</if>
            <if test="volume != null">#{volume},</if>
            <if test="oilType != null">#{oilType},</if>
            <if test="transmission != null">#{transmission},</if>
            <if test="tankCapacity != null">#{tankCapacity},</if>
            <if test="seatQuantity != null">#{seatQuantity},</if>
            <if test="doorQuantity != null">#{doorQuantity},</if>
            <if test="fuelConsumption != null">#{fuelConsumption},</if>
            <if test="carAge != null">#{carAge},</if>
            <if test="powerType != null">#{powerType},</if>
            <if test="refundState != null">#{refundState},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="refundUserId != null">#{refundUserId},</if>
            <if test="refundDate != null">#{refundDate},</if>
            <if test="refundNo != null">#{refundNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="takeCarTime != null">#{takeCarTime},</if>
            <if test="returnCarTime != null">#{returnCarTime},</if>
            <if test="orderDay != null">#{orderDay},</if>
            <if test="contractUserId != null">#{contractUserId},</if>
            <if test="contractTime != null">#{contractTime},</if>
            <if test="checkUserId != null">#{checkUserId},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="returnDesposit != null">#{returnDesposit},</if>
            <if test="returnDespositTime != null">#{returnDespositTime},</if>
            <if test="despositReason != null">#{despositReason},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
         </trim>
    </insert>

    <update id="updateCarOrder" parameterType="CarOrder">
        update car_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="carId != null">car_id = #{carId},</if>
            <if test="carName != null">car_name = #{carName},</if>
            <if test="carNo != null">car_no = #{carNo},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="areaId != null">area_id = #{areaId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="totalPrice != null">total_price = #{totalPrice},</if>
            <if test="deposit != null">deposit = #{deposit},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="oilType != null">oil_type = #{oilType},</if>
            <if test="transmission != null">transmission = #{transmission},</if>
            <if test="tankCapacity != null">tank_capacity = #{tankCapacity},</if>
            <if test="seatQuantity != null">seat_quantity = #{seatQuantity},</if>
            <if test="doorQuantity != null">door_quantity = #{doorQuantity},</if>
            <if test="fuelConsumption != null">fuel_consumption = #{fuelConsumption},</if>
            <if test="carAge != null">car_age = #{carAge},</if>
            <if test="powerType != null">power_type = #{powerType},</if>
            <if test="refundState != null">refund_state = #{refundState},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundUserId != null">refund_user_id = #{refundUserId},</if>
            <if test="refundDate != null">refund_date = #{refundDate},</if>
            <if test="refundNo != null">refund_no = #{refundNo},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="takeCarTime != null">take_car_time = #{takeCarTime},</if>
            <if test="returnCarTime != null">return_car_time = #{returnCarTime},</if>
            <if test="orderDay != null">order_day = #{orderDay},</if>
            <if test="contractUserId != null">contract_user_id = #{contractUserId},</if>
            <if test="contractTime != null">contract_time = #{contractTime},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="returnDesposit != null">return_desposit = #{returnDesposit},</if>
            <if test="returnDespositTime != null">return_desposit_time = #{returnDespositTime},</if>
            <if test="despositReason != null">desposit_reason = #{despositReason},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where order_id = #{orderId}
    </update>

    <delete id="deleteCarOrderByOrderId" parameterType="Long">
        delete from car_order where order_id = #{orderId}
    </delete>

    <delete id="deleteCarOrderByOrderIds" parameterType="String">
        delete from car_order where order_id in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <select id="getOrderList" resultType="CarOrderGzhListVo">
        select
        r.order_id, r.order_no, r.user_id, r.car_id, r.car_name, r.car_no, r.company_name, r.model_name, r.area_id, r.price,
        r.total_price, r.deposit, r.volume, r.oil_type, r.transmission, r.tank_capacity, r.seat_quantity, r.door_quantity,
        r.fuel_consumption, r.car_age, r.power_type, r.refund_state, r.refund_amount, r.refund_user_id, r.refund_date, r.refund_no,
        r.pay_time, r.take_car_time, r.return_car_time, r.contract_user_id, r.contract_time, r.check_user_id,
        r.check_time, r.return_desposit, r.return_desposit_time, r.desposit_reason, r.create_by, r.create_time, r.update_by, r.update_time,
        r.order_total_price,cbi.cover_url,r.refund_total_price,r.all_order_refund_state,
               case when r.state = 'evaluate' then 'completed' else r.state end as state,
               u.real_name,
               u.phonenumber,
        CASE
        WHEN MAX( cot.return_car_date ) IS NOT NULL THEN
        MAX( cot.return_car_date ) ELSE r.return_car_time
        END AS return_car_time_real,
        CEILING(
        TIMESTAMPDIFF(
        SECOND,
        r.take_car_time,
        CASE
        WHEN MAX( cot.return_car_date ) IS NOT NULL THEN
        MAX( cot.return_car_date ) ELSE r.return_car_time
        END
        ) / 86400
        ) AS order_day
        from car_order r left join sys_user u on r.user_id = u.user_id
                         left join (
        SELECT
        order_id,
        MAX(return_car_date) AS return_car_date
        FROM car_order_tenancy_term
        WHERE state not in ('created','cancel','closed')
        AND refund_state not in (1,2,3)
        GROUP BY order_id
        ) cot ON r.order_id = cot.order_id
                         left join car_base_info cbi on cbi.car_id = r.car_id
        where r.state !='created' and r.is_del = 0
        and (
        <foreach collection="carBaseInfos" separator="or" item="item" index="index" >
            (r.car_id = #{item.carId})
        </foreach>
        )
        and r.pay_time is not null
        <if test="state != null and state != 'completed' and state != ''"> and r.state = #{state}</if>
        <if test="state != null and state == 'completed'"> and (r.state = 'completed' or r.state = 'evaluate')</if>
        GROUP BY r.order_id, r.order_no, r.user_id, r.car_id, r.car_name, r.car_no, r.company_name, r.model_name, r.area_id, r.price,
        r.total_price, r.deposit, r.volume, r.oil_type, r.transmission, r.tank_capacity, r.seat_quantity, r.door_quantity,
        r.fuel_consumption, r.car_age, r.power_type, r.refund_state, r.refund_amount, r.refund_user_id, r.refund_date, r.refund_no,
        r.pay_time, r.take_car_time, r.return_car_time, r.contract_user_id, r.contract_time, r.check_user_id,
        r.check_time, r.return_desposit, r.return_desposit_time, r.desposit_reason, r.create_by, r.create_time, r.update_by, r.update_time,
        r.order_total_price, u.real_name, u.phonenumber, cbi.cover_url
        order by r.create_time desc
    </select>


    <resultMap id="CarOrderDetailVOResult" type="com.bell.car.domain.VO.CarOrderDetailVO" extends="CarOrderResult">
        <result property="nickName" column="nick_name" />
        <result property="phonenumber" column="phonenumber" />
        <result property="coverUrl"    column="cover_url"    />
        <result property="tackCarName"    column="tack_car_name"    />
        <result property="returnCarName"    column="return_car_name"    />
        <result property="orderTotalPrice"    column="order_total_price"    />
        <result property="updateNumber"    column="update_number"    />
        <result property="tackCarImage"    column="tack_car_image"    />
        <result property="returnCarImage"    column="return_car_image"    />
        <result property="contactInfo"    column="contact_info"    />
        <result property="allOrderRefundState"    column="all_order_refund_state"    />
        <result property="refundTotalPrice"    column="refund_total_price"    />
        <result property="returnNewCarTime"    column="return_new_car_time"    />
        <result property="platformRefundReason"    column="platform_refund_reason"    />



        <collection property="tenancies" ofType="com.bell.car.domain.CarOrderTenancyTerm">
            <result property="termId" column="tenancy_term_id" />
            <result property="orderId" column="tenancy_order_id" />
            <result property="returnCarDate" column="tenancy_return_car_date" />
            <result property="orderNo" column="tenancy_order_no" />
            <result property="carNo" column="tenancy_car_no" />
            <result property="orderDay" column="tenancy_order_day" />
            <result property="price" column="tenancy_price" />
            <result property="totalPrice" column="tenancy_total_price" />
            <result property="createTime" column="tenancy_create_time" />
            <result property="payTime" column="tenancy_pay_time" />
            <result property="refundState" column="tenancy_refund_state" />
            <result property="refundAmount" column="tenancy_refund_amount" />
            <result property="refundTime" column="tenancy_refund_time" />
            <result property="platformRefundReason" column="tenancy_platform_refund_reason" />

        </collection>
        <collection property="fileList" ofType="com.bell.car.domain.CarOrderImage">
            <result property="imageId" column="image_image_id" />
            <result property="orderId" column="image_order_id" />
            <result property="fileUrl" column="image_file_url" />
            <result property="fileType" column="image_file_type" />
            <result property="createTime" column="image_create_time" />
        </collection>
    </resultMap>

    <resultMap id="CarOrderDetailGzhResult" type="CarOrderGzhDetailVo">
        <result property="orderId"    column="order_id"    />
        <result property="orderNo"    column="order_no"    />
        <result property="userId"    column="user_id"    />
        <result property="state"    column="state"    />
        <result property="carId"    column="car_id"    />
        <result property="carName"    column="car_name"    />
        <result property="carNo"    column="car_no"    />
        <result property="companyName"    column="company_name"    />
        <result property="modelName"    column="model_name"    />
        <result property="areaId"    column="area_id"    />
        <result property="price"    column="price"    />
        <result property="totalPrice"    column="total_price"    />
        <result property="orderTotalPrice"    column="order_total_price"    />
        <result property="deposit"    column="deposit"    />
        <result property="volume"    column="volume"    />
        <result property="oilType"    column="oil_type"    />
        <result property="transmission"    column="transmission"    />
        <result property="tankCapacity"    column="tank_capacity"    />
        <result property="seatQuantity"    column="seat_quantity"    />
        <result property="doorQuantity"    column="door_quantity"    />
        <result property="fuelConsumption"    column="fuel_consumption"    />
        <result property="carAge"    column="car_age"    />
        <result property="powerType"    column="power_type"    />
        <result property="refundState"    column="refund_state"    />
        <result property="allOrderRefundState"    column="all_order_refund_state"    />
        <result property="refundAmount"    column="refund_amount"    />
        <result property="refundTotalPrice"    column="refund_total_price"    />
        <result property="refundUserId"    column="refund_user_id"    />
        <result property="refundDate"    column="refund_date"    />
        <result property="refundNo"    column="refund_no"    />
        <result property="payTime"    column="pay_time"    />
        <result property="takeCarTime"    column="take_car_time"    />
        <result property="returnCarTime"    column="return_car_time"    />
        <result property="tackCarName"    column="tack_car_name"    />
        <result property="returnCarName"    column="return_car_name"    />
        <result property="tackCarImage"    column="tack_car_image"    />
        <result property="returnCarImage"    column="return_car_image"    />
        <result property="returnCarTimeReal"    column="return_car_time_real"    />
        <result property="orderDay"    column="order_day"    />
        <result property="contractUserId"    column="contract_user_id"    />
        <result property="contractUserName"    column="contract_user_name"    />
        <result property="contractUserAccount"    column="contract_user_account"    />
        <result property="contractTime"    column="contract_time"    />
        <result property="checkUserId"    column="check_user_id"    />
        <result property="checkUserName"    column="check_user_name"    />
        <result property="checkUserAccount"    column="check_user_account"    />
        <result property="checkTime"    column="check_time"    />
        <result property="returnDesposit"    column="return_desposit"    />
        <result property="returnDespositTime"    column="return_desposit_time"    />
        <result property="despositReason"    column="desposit_reason"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
        <result property="realName" column="real_name" />
        <result property="phonenumber" column="phonenumber" />
        <result property="coverUrl" column="cover_url" />
        <result property="carNo" column="car_no" />
        <result property="platformRefundReason" column="platform_refund_reason" />
        <collection property="tenancies" ofType="com.bell.car.domain.CarOrderTenancyTerm">
            <result property="termId" column="tenancy_term_id" />
            <result property="orderId" column="tenancy_order_id" />
            <result property="returnCarDate" column="tenancy_return_car_date" />
            <result property="orderNo" column="tenancy_order_no" />
            <result property="carNo" column="tenancy_car_no" />
            <result property="orderDay" column="tenancy_order_day" />
            <result property="price" column="tenancy_price" />
            <result property="totalPrice" column="tenancy_total_price" />
            <result property="createTime" column="tenancy_create_time" />
            <result property="state" column="tenancy_state" />
            <result property="refundState" column="tenancy_refund_state" />
            <result property="platformRefundReason" column="tenancy_platform_refund_reason" />
            <result property="refundTime" column="tenancy_refund_time" />
            <result property="refundAmount" column="tenancy_refund_amount" />
        </collection>
        <collection property="fileList" ofType="com.bell.car.domain.CarOrderImage">
            <result property="imageId" column="image_image_id" />
            <result property="orderId" column="image_order_id" />
            <result property="fileUrl" column="image_file_url" />
            <result property="fileType" column="image_file_type" />
        </collection>
        <collection property="checkList" ofType="com.bell.car.domain.CarOrderImage">
            <result property="imageId" column="son_image_image_id" />
            <result property="orderId" column="son_image_order_id" />
            <result property="fileUrl" column="son_image_file_url" />
            <result property="fileType" column="son_image_file_type" />
        </collection>
    </resultMap>

    <select id="selectAppCarOrderByOrderId" parameterType="Long" resultMap="CarOrderDetailVOResult">
        SELECT
            co.order_id,
            co.state,
            co.order_no,
            co.car_name,
            co.car_id,
            co.car_no,
            co.volume,
            co.seat_quantity,
            co.transmission,
            co.car_age,
            co.model_name,
            co.total_price,
            co.refund_amount,
            co.refund_user_id,
            co.refund_date,
            co.refund_no,
            co.refund_state,
            co.tack_car_name,
            co.return_car_name,
            co.take_car_time,
            co.return_car_time,
            co.pay_time,
            co.order_total_price,
            co.deposit,
            co.return_desposit,
            co.return_desposit_time,
            co.desposit_reason,
            co.order_day,
            co.contract_user_id,
            co.contract_time,
            co.check_user_id,
            co.user_id,
            u.nick_name,
            u1.contact_info,
            co.model_id,
            co.company_id,
            u.phonenumber,
            co.cover_url,
            co.create_time,
            co.all_order_refund_state,
            co.refund_total_price,
            co.platform_refund_reason,
            co.update_number,
            co.return_new_car_time,
            cot.term_id AS tenancy_term_id,
            cot.order_id AS tenancy_order_id,
            cot.return_car_date AS tenancy_return_car_date,
            cot.order_no AS tenancy_order_no,
            cot.car_no AS tenancy_car_no,
            cot.order_day AS tenancy_order_day,
            cot.price AS tenancy_price,
            cot.total_price AS tenancy_total_price,
            cot.create_time AS tenancy_create_time,
            cot.pay_time AS tenancy_pay_time,
            cot.refund_state AS tenancy_refund_state,
            cot.refund_amount AS tenancy_refund_amount,
            cot.refund_time AS tenancy_refund_time,
            cot.platform_refund_reason AS tenancy_platform_refund_reason,
            cbi.cover_url,
            co.company_name,
            co.model_name,
            coi.image_id AS image_image_id,
            coi.order_id AS image_order_id,
            coi.file_url AS image_file_url,
            coi.file_type AS image_file_type,
            coi.create_time AS image_create_time,
            take_dock.image_url AS tack_car_image,
            return_dock.image_url AS return_car_image
        FROM car_order co
        LEFT JOIN sys_user u ON co.user_id = u.user_id
        LEFT JOIN car_order_tenancy_term cot ON co.order_id = cot.order_id
        LEFT JOIN car_base_info cbi ON co.car_id = cbi.car_id
        LEFT JOIN sys_user u1 ON cbi.user_id = u1.user_id
        LEFT JOIN bell_parking_lot take_dock ON cbi.take_dock_id = take_dock.parking_lot_id
        LEFT JOIN bell_parking_lot return_dock ON cbi.return_dock_id = return_dock.parking_lot_id
        LEFT JOIN car_order_image coi ON co.order_id = coi.order_id AND coi.is_del = 0
        WHERE co.order_id = #{orderId} AND co.user_id = #{userId}
        ORDER BY co.create_time DESC, cot.create_time DESC
    </select>

<!--    公众号订单详情-->
    <select id="selectGzhCarOrderByOrderId" parameterType="Long" resultMap="CarOrderDetailGzhResult">
        SELECT
            co.order_id,
            co.state,
            co.order_no,
            co.car_name,
            co.volume,
            co.seat_quantity,
            case when co.transmission = '0' then '手动挡' else '自动挡' end as transmission,
            co.car_age,
            co.model_name,
            co.total_price,
            co.refund_amount,
            co.refund_total_price,
            co.refund_user_id,
            co.refund_date,
            co.refund_no,
            co.refund_state,
            co.tack_car_name,
            co.return_car_name,
            co.take_car_time,
            co.return_car_time,
            co.all_order_refund_state,
            CASE
                WHEN MAX( cot.return_car_date ) IS NOT NULL THEN
                    MAX( cot.return_car_date ) ELSE co.return_car_time
                END AS return_car_time_real,
            CEILING(
                        TIMESTAMPDIFF(
                            SECOND,
                                co.take_car_time,
                                CASE

                                    WHEN MAX( cot.return_car_date ) IS NOT NULL THEN
                                        MAX( cot.return_car_date ) ELSE co.return_car_time
                                    END
                        ) / 86400
            ) AS order_day,
            co.pay_time,
            co.order_total_price,
            co.deposit,
            co.return_desposit,
            co.return_desposit_time,
            co.desposit_reason,
            co.contract_user_id,
            co.create_time,
            co.order_total_price,
            co.platform_refund_reason,
            us.nick_name AS contract_user_name,
            us.user_name AS contract_user_account,
            co.contract_time,
            co.check_user_id,
            co.check_time,
            uu.nick_name AS check_user_name,
            uu.user_name AS check_user_account,
            co.user_id,
            u.real_name,
            u.phonenumber,
            co.model_id,
            co.company_id,
            co.car_no,
            cbi.cover_url,
            co.company_name,
            co.model_name,
            dock.image_url as tack_car_image,
            dbck.image_url as return_car_image,
            cotc.term_id AS tenancy_term_id,
            cotc.order_id AS tenancy_order_id,
            cotc.return_car_date AS tenancy_return_car_date,
            cotc.order_no AS tenancy_order_no,
            cotc.car_no AS tenancy_car_no,
            cotc.order_day AS tenancy_order_day,
            cotc.price AS tenancy_price,
            cotc.total_price AS tenancy_total_price,
            cotc.create_time AS tenancy_create_time,
            cotc.state AS tenancy_state,
            cotc.refund_state AS tenancy_refund_state,
            cotc.platform_refund_reason AS tenancy_platform_refund_reason,
            cotc.refund_time AS tenancy_refund_time,
            cotc.refund_amount AS tenancy_refund_amount,
            coi.image_id AS image_image_id,
            coi.order_id AS image_order_id,
            coi.file_url AS image_file_url,
            coi.file_type AS image_file_type,
            com.image_id AS son_image_image_id,
            com.order_id AS son_image_order_id,
            com.file_url AS son_image_file_url,
            com.file_type AS son_image_file_type
        FROM
            car_order co
                LEFT JOIN sys_user u ON co.user_id = u.user_id
                LEFT JOIN sys_user us ON co.contract_user_id = us.user_id
                LEFT JOIN sys_user uu ON co.check_user_id = uu.user_id
                LEFT JOIN (
                SELECT
                    order_id,
                    MAX(return_car_date) AS return_car_date
                FROM car_order_tenancy_term
                WHERE state not in ('created','cancel','closed')
                AND refund_state not in (1,2,3)
                GROUP BY order_id
            ) cot ON co.order_id = cot.order_id
                LEFT JOIN car_base_info cbi ON co.car_id = cbi.car_id
                LEFT JOIN car_order_image coi ON co.order_id = coi.order_id
                AND coi.is_del = 0
                AND coi.file_type = 1
                LEFT JOIN car_order_image com ON co.order_id = com.order_id
                AND com.is_del = 0
                AND com.file_type = 2
                LEFT JOIN car_order_tenancy_term cotc on cotc.order_id = co.order_id
                LEFT JOIN bell_parking_lot dock on cbi.take_dock_id = dock.parking_lot_id
                LEFT JOIN bell_parking_lot dbck on cbi.return_dock_id = dbck.parking_lot_id
        WHERE
            co.order_id = #{orderId}
        GROUP BY
            co.order_id,
            co.state,
            co.order_no,
            co.car_name,
            co.volume,
            co.seat_quantity,
            co.transmission,
            co.car_age,
            co.model_name,
            co.total_price,
            co.refund_amount,
            co.refund_total_price,
            co.refund_user_id,
            co.refund_date,
            co.refund_no,
            co.refund_state,
            co.tack_car_name,
            co.return_car_name,
            co.take_car_time,
            co.return_car_time,
            co.pay_time,
            co.order_total_price,
            co.deposit,
            co.return_desposit,
            co.return_desposit_time,
            co.desposit_reason,
            co.contract_user_id,
            co.create_time,
            co.order_total_price,
            co.platform_refund_reason,
            co.all_order_refund_state,
            us.real_name,
            us.user_name,
            co.contract_time,
            co.check_user_id,
            co.check_time,
            uu.real_name,
            uu.user_name,
            co.user_id,
            u.real_name,
            u.phonenumber,
            co.model_id,
            co.company_id,
            cbi.cover_url,
            co.company_name,
            co.model_name,
            co.car_no,
            dock.image_url,
            dbck.image_url,
            cotc.term_id,
            cotc.order_id,
            cotc.return_car_date,
            cotc.order_no,
            cotc.car_no,
            cotc.order_day,
            cotc.price,
            cotc.total_price,
            cotc.create_time,
            cotc.state,
            cotc.refund_state,
            cotc.platform_refund_reason,
            cotc.refund_time,
            cotc.refund_amount,
            coi.image_id,
            coi.order_id,
            coi.file_url,
            coi.file_type,
            com.image_id,
            com.order_id,
            com.file_url,
            com.file_type
        ORDER BY cotc.create_time desc, coi.image_id asc, com.image_id asc
    </select>
    
    <select id="getCarOrderDetails" resultType="com.bell.car.domain.VO.CarOrderDetailsVO">
        SELECT 
            co.area_name, 
            co.area_parent_name, 
            co.tack_car_name, 
            co.return_car_name, 
            co.take_car_time, 
            cbi.price, 
            COALESCE(
                (SELECT MAX(cott.return_car_date) 
                 FROM car_order_tenancy_term cott 
                 WHERE cott.order_id = co.order_id), 
                co.return_car_time
            ) AS return_car_date 
        FROM 
            car_order co 
            LEFT JOIN car_base_info cbi ON cbi.car_id = co.car_id 
        WHERE 
            co.is_del = 0
            AND co.order_id = #{orderId}
    </select>

    <select id="getCarOrderSmsInfo" resultType="CarOrderSmsVO">
        SELECT
            co.order_id,
            co.order_no,
            co.car_name,
            co.take_car_time,
            co.return_car_time,
            su.real_name as user_name,
            su.phonenumber as user_phone,
            suu.contact_info as car_phone,
            bpl.parking_lot_name as takeCarName
        FROM
            car_order co
                LEFT JOIN sys_user su ON co.user_id = su.user_id
                LEFT JOIN car_base_info cbi ON co.car_id = cbi.car_id
                LEFT JOIN sys_user suu ON cbi.user_id = suu.user_id
                LEFT JOIN bell_parking_lot bpl ON cbi.take_dock_id = bpl.parking_lot_id
        WHERE
            co.is_del = 0
          AND co.order_no = #{orderNo}
    </select>

    <select id="getCarOrderSmsInfoList" resultType="CarOrderSmsVO">
        SELECT
        co.order_id,
        co.order_no,
        co.car_name,
        co.take_car_time,
        co.return_car_time,
        su.real_name as user_name,
        su.phonenumber as user_phone,
        suu.contact_info as car_phone,
        bpl.parking_lot_name as takeCarName
        FROM
        car_order co
        LEFT JOIN sys_user su ON co.user_id = su.user_id
        LEFT JOIN car_base_info cbi ON co.car_id = cbi.car_id
        LEFT JOIN sys_user suu ON cbi.user_id = suu.user_id
        LEFT JOIN bell_parking_lot bpl ON cbi.take_dock_id = bpl.parking_lot_id
        WHERE
        co.is_del = 0
        AND co.order_id in (
        <foreach collection="orderIds" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>
</mapper>