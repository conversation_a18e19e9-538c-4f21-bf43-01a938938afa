package com.bell.car.service.impl;

import java.util.List;
import com.bell.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.bell.car.mapper.CarImageMapper;
import com.bell.car.domain.CarImage;
import com.bell.car.service.ICarImageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
 * 车辆图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class CarImageServiceImpl extends ServiceImpl<CarImageMapper, CarImage> implements ICarImageService
{
    @Autowired
    private CarImageMapper carImageMapper;

    /**
     * 查询车辆图片
     *
     * @param imageId 车辆图片主键
     * @return 车辆图片
     */
    @Override
    public CarImage selectCarImageByImageId(Long imageId)
    {
        return this.getById(imageId);
    }

    /**
     * 查询车辆图片列表
     *
     * @param carImage 车辆图片
     * @return 车辆图片
     */
    @Override
    public List<CarImage> selectCarImageList(CarImage carImage)
    {
        LambdaQueryWrapper<CarImage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(carImage.getCarId())) {
                lambdaQueryWrapper.eq(CarImage::getCarId
                ,carImage.getCarId());
            }
                if(ObjectUtil.isNotEmpty(carImage.getFileType())) {
                lambdaQueryWrapper.eq(CarImage::getFileType
                ,carImage.getFileType());
            }
                if(ObjectUtil.isNotEmpty(carImage.getImageUrl())) {
                lambdaQueryWrapper.eq(CarImage::getImageUrl
                ,carImage.getImageUrl());
            }
                if(ObjectUtil.isNotEmpty(carImage.getFileName())) {
                lambdaQueryWrapper.like(CarImage::getFileName
                ,carImage.getFileName());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增车辆图片
     *
     * @param carImage 车辆图片
     * @return 结果
     */
    @Override
    public boolean insertCarImage(CarImage carImage)
    {
        return this.save(carImage);
    }

    /**
     * 修改车辆图片
     *
     * @param carImage 车辆图片
     * @return 结果
     */
    @Override
    public boolean updateCarImage(CarImage carImage)
    {
        return this.updateById(carImage);
    }

    /**
     * 批量删除车辆图片
     *
     * @param imageIds 需要删除的车辆图片主键
     * @return 结果
     */
    @Override
    public boolean deleteCarImageByImageIds(List<Long> imageIds)
    {
        return this.removeByIds(imageIds);
    }

    /**
     * 删除车辆图片信息
     *
     * @param imageId 车辆图片主键
     * @return 结果
     */
    @Override
    public boolean deleteCarImageByImageId(Long imageId)
    {
        return this.removeById(imageId);
    }
}
