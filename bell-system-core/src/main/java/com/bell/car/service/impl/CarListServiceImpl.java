package com.bell.car.service.impl;

import com.bell.basic.domain.BellDock;
import com.bell.basic.domain.BellSeaArea;
import com.bell.basic.domain.vo.BannerVo;
import com.bell.car.domain.CarBaseInfo;
import com.bell.car.domain.CarBaseInfoEntity;
import com.bell.car.domain.CarFacility;
import com.bell.car.domain.CarModel;
import com.bell.car.domain.Dto.CarListDto;
import com.bell.car.domain.VO.AppCarModelVo;
import com.bell.car.domain.VO.CarDetailVo;
import com.bell.car.domain.VO.CarEvaluationVo;
import com.bell.car.domain.VO.CarListVo;
import com.bell.car.mapper.CarListMapper;
import com.bell.car.service.ICarListService;
import com.bell.common.utils.StringUtils;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小程序 车辆列表serviceImpl
 */
@Service
public class CarListServiceImpl implements ICarListService {

    @Autowired
    private CarListMapper carListMapper;

    /**
     * 小程序 租车首页信息查询
     * @param carListDto
     * @return
     */
    @Override
    public CarListVo getCarHomePageInfo(CarListDto carListDto) {
        CarListVo carListVo = new CarListVo();
        //获取租车首页banner图
        List<BannerVo> carBannerInfo = carListMapper.getCarBannerInfo(carListDto.getAreaId());
        //获取二级地区
        List<BellSeaArea> twoLevelAreaInfo = carListMapper.getTwoLevelAreaInfo(carListDto.getAreaId());
        //根据二级地区id获取码头列表
        if (StringUtils.isNotEmpty(twoLevelAreaInfo)) {
            List<BellDock> dockListByTownId = carListMapper.getDockListByTownId(twoLevelAreaInfo.get(0).getAreaId());
            carListVo.setDockList(dockListByTownId);
        }
        //获取取还车时间
        String carOrderStartTimeAndEndTime = carListMapper.getCarOrderStartTimeAndEndTime();
        //处理json字符串
        JsonObject jsonObject = JsonParser.parseString(carOrderStartTimeAndEndTime).getAsJsonObject();
        carListVo.setStartTime(jsonObject.get("startTime").getAsString());
        carListVo.setEndTime(jsonObject.get("endTime").getAsString());
        carListVo.setCarBannerList(carBannerInfo);
        carListVo.setAreaList(twoLevelAreaInfo);
        return carListVo;
    }

    /**
     * 小程序 获取二级地区及码头列表
     * @param carListDto
     * @return
     */
    public CarListVo getAreaInfo(CarListDto carListDto) {
        CarListVo carListVo = new CarListVo();
        //获取二级地区
        List<BellSeaArea> twoLevelAreaInfo = carListMapper.getTwoLevelAreaInfo(carListDto.getAreaId());
        //根据二级地区id获取码头列表
        if (StringUtils.isNotEmpty(twoLevelAreaInfo)) {
            List<BellDock> dockListByTownId = carListMapper.getDockListByTownId(twoLevelAreaInfo.get(0).getAreaId());
            carListVo.setDockList(dockListByTownId);
        }
        carListVo.setAreaList(twoLevelAreaInfo);
        return carListVo;
    }

    /**
     * 小程序 根据二级地区id获取码头列表
     * @param areaId
     * @return
     */
    @Override
    public List<BellDock> getDockListByTownId(Long areaId) {
        List<BellDock> dockListByTownId = carListMapper.getDockListByTownId(areaId);
        return dockListByTownId;
    }

    /**
     * 小程序 车辆列表
     * @param carListDto
     * @return
     */
    @Override
    public CarListVo getCarList(CarListDto carListDto) {
        CarListVo carListVo = new CarListVo();
        // 获取全部车型
        List<AppCarModelVo> modelInfo = carListMapper.getModelInfo(carListDto);
        //获取车辆配置列表
        List<CarFacility> carFacilityList = carListMapper.getCarFacilityList();
        carListVo.setCarModelList(modelInfo);
        carListVo.setCarFacilityList(carFacilityList);
        return carListVo;
    }

    /**
     * 小程序 根据查询条件查询车辆列表
     * @param carListDto
     * @return
     */
    @Override
    public List<CarBaseInfoEntity> getCarBaseInfoList(CarListDto carListDto) {
        return carListMapper.getCarBaseInfoList(carListDto);
    }

    /**
     * 小程序 刷新车型列表
     * @param carListDto
     * @return
     */
    @Override
    public List<AppCarModelVo> refreshCarModelList(CarListDto carListDto) {
        return carListMapper.refreshCarModelList(carListDto);
    }

    /**
     * 小程序 车辆详情
     * @param carId
     * @return
     */
    @Override
    public CarDetailVo getCarBaseDetail(Long carId) {
        //获取车辆介绍
        CarDetailVo carBaseDetail = carListMapper.getCarBaseDetail(carId);
        //获取租车规则
        carBaseDetail.setCarRule(carListMapper.getCarRule());
        return carBaseDetail;
    }

    /**
     * 小程序 根据车id获取评价列表
     * @param carId
     * @return
     */
    @Override
    public List<CarEvaluationVo> getCarOrderEvalution(Long carId) {
        return carListMapper.getCarOrderEvalution(carId);
    }
}
