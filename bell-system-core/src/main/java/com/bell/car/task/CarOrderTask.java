package com.bell.car.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bell.car.domain.CarOrder;
import com.bell.car.domain.VO.CarOrderSmsVO;
import com.bell.car.mapper.CarOrderMapper;
import com.bell.system.service.ICarSmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 汽车订单定时任务
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Component
public class CarOrderTask {
    private static final Logger log = LoggerFactory.getLogger(CarOrderTask.class);

    @Autowired
    private CarOrderMapper carOrderMapper;

    @Autowired
    private ICarSmsService carSmsService;

    /**
     * 关闭超期未取车订单定时任务
     * 每天23:59:59执行一次，处理下单后一直未取车且超过还车日期的订单
     */
    @Scheduled(cron = "59 59 23 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void closeOverdueUnpickedOrders() {
        log.info("开始执行关闭超期未取车订单定时任务");
        try {
            // 获取当前时间
            Date currentTime = new Date();

            // 查询条件：状态为"paid"（待取车）且还车时间已过期的订单
            LambdaQueryWrapper<CarOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CarOrder::getState, "paid")  // 待取车状态
                    .and(wrapper -> {
                        // 如果有最新还车时间，则以最新还车时间为准，否则以原还车时间为准
                        wrapper.and(subWrapper -> subWrapper
                                .isNotNull(CarOrder::getReturnNewCarTime)
                                .lt(CarOrder::getReturnNewCarTime, currentTime))
                                .or(subWrapper -> subWrapper
                                        .isNull(CarOrder::getReturnNewCarTime)
                                        .lt(CarOrder::getReturnCarTime, currentTime));
                    });

            List<CarOrder> overdueOrders = carOrderMapper.selectList(queryWrapper);

            if (overdueOrders != null && !overdueOrders.isEmpty()) {
                log.info("找到{}个超期未取车的订单", overdueOrders.size());

                // 批量更新订单状态为"closed"（已关闭）
                LambdaUpdateWrapper<CarOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(CarOrder::getOrderId,
                        overdueOrders.stream().map(CarOrder::getOrderId).toArray())
                        .set(CarOrder::getState, "closed")
                        .set(CarOrder::getCompleteTime, new Date())
                        .set(CarOrder::getUpdateTime, currentTime)
                        .set(CarOrder::getUpdateBy, -1);

                int updateCount = carOrderMapper.update(null, updateWrapper);
                log.info("成功关闭{}个超期未取车订单", updateCount);

                // 记录被关闭的订单信息
                for (CarOrder order : overdueOrders) {
                    log.info("订单{}已关闭，订单号：{}，用户ID：{}，原还车时间：{}，最新还车时间：{}",
                            order.getOrderId(),
                            order.getOrderNo(),
                            order.getUserId(),
                            order.getReturnCarTime(),
                            order.getReturnNewCarTime());
                }
                // 发送短信
                List<Long> carOrderIds = overdueOrders.stream().map(CarOrder::getOrderId).collect(Collectors.toList());
                List<CarOrderSmsVO> vos = carOrderMapper.getCarOrderSmsInfoList(carOrderIds);
                try {
                    for(CarOrderSmsVO vo: vos){
                        // 客户短信
                        carSmsService.overTimeUserSms(vo.getUserPhone(),vo.getCarName(),vo.getOrderNo());
                        // 员工短信
                        carSmsService.overTimeCompanySms(vo.getUserPhone(),vo.getUserName(),vo.getCarName(),vo.getOrderNo());
                    }
                }catch (Exception e){
                    log.error("租车订单关闭短信发送失败", e);
                }
            } else {
                log.info("未找到需要关闭的超期未取车订单");
            }

        } catch (Exception e) {
            log.error("执行关闭超期未取车订单定时任务时发生异常", e);
            throw e;
        }
        log.info("关闭超期未取车订单定时任务执行完成");
    }

}
