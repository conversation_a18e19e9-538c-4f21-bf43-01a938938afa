package com.bell.wx.service;

import com.bell.car.domain.CarOrder;
import com.bell.car.domain.Dto.CarOrderDTO;
import com.bell.competition.domain.ChallengeCompetitionOrderTeam;
import com.bell.competition.domain.FishingCompetitionOrder;
import com.bell.scenic.domain.Dto.ScenicOrderDTO;
import com.bell.scenic.domain.ScenicSpotTicketOrder;
import com.bell.ship.domain.ShipOrder;
import com.bell.ship.domain.dto.ShipOrderDto;
import com.bell.wx.domain.DTO.PayRequestDTO;

import java.math.BigDecimal;
import java.util.Map;

public interface WxService
{
    int notifyRefund(String refundInfoData, String timestamp, String nonce, String serial, String signature);

    int notifyPay(String payInfoData, String timestamp, String nonce, String serial, String signature);

    int refund(ShipOrder shipOrder);

    int refundScenicOrder(ScenicSpotTicketOrder scenicSpotTicketOrder);

    int refundAppScenicOrder(ScenicSpotTicketOrder scenicSpotTicketOrder);

    Map<String, String> createOrder(PayRequestDTO request);

    Map<String, String> createOrderNoPay(PayRequestDTO request);

    Map<String, String> createSeaFishingOrder(ShipOrderDto shipOrderDto);

    /**
     * 海钓约船，修改订单
     * @param shipOrderDto
     * @return
     */
    Map<String, String> updateSeaFishingOrder(ShipOrderDto shipOrderDto);

    Map<String, String> createScenicOrder(ScenicOrderDTO request);
    /**
     * 创建租车订单
     * @param request 租车订单请求
     * @return 支付参数
     */
    Map<String, String> createCarOrder(CarOrderDTO request);

    Map<String, String> updateCarOrder(CarOrderDTO request);

    /**
     * 海钓订单客户退款
     * @param shipOrder
     * @return
     */
    int refundShipOrderByCustomer(ShipOrder shipOrder);

    /**
     * 海钓订单平台退款
     * @param shipOrder
     * @return
     */
    int refundShipOrderByManager(ShipOrder shipOrder);

    int refundCarOrder(CarOrder carOrder);

    Map<String, String> createContinueCarOrder(CarOrderDTO request);

    /**
     * 租车订单平台退款
     * @param carOrder
     * @return
     */
    CarOrder refundCarOrderByManager(CarOrder carOrder);

    /**
     * 创建挑战赛订单
     * @param price
     * @param orderNo
     * @param microAppOpenId
     * @return
     */
    Map<String, String> createChallengeOrder(BigDecimal price, String orderNo, String microAppOpenId);

    // 积分赛退款
    int refundfishingOrder(FishingCompetitionOrder fishingCompetitionOrder);

    // 挑战赛退款
    int refundChallengeCompetitionOrder(ChallengeCompetitionOrderTeam challengeCompetitionOrderTeam);
}
