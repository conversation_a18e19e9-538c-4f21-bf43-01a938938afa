package com.bell.wx.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.bell.car.domain.*;
import com.bell.car.domain.Dto.CarAvailabilityRequest;
import com.bell.car.domain.Dto.CarOrderDTO;
import com.bell.car.domain.Dto.CarOrderPayDto;
import com.bell.car.domain.VO.CarOrderSmsVO;
import com.bell.car.mapper.*;
import com.bell.car.service.ICarOrderService;
import com.bell.common.core.domain.entity.SysUser;
import com.bell.common.utils.StringUtils;
import com.bell.common.utils.WxPayUtil;
import com.bell.competition.domain.*;
import com.bell.competition.mapper.*;
import com.bell.scenic.domain.Dto.ScenicOrderDTO;
import com.bell.scenic.domain.ScenicSpot;
import com.bell.scenic.domain.ScenicSpotTicket;
import com.bell.scenic.domain.ScenicSpotTicketOrder;
import com.bell.scenic.domain.ScenicSpotTicketOrderMember;
import com.bell.scenic.domain.VO.AppMemberVO;
import com.bell.scenic.mapper.ScenicSpotMapper;
import com.bell.scenic.mapper.ScenicSpotTicketMapper;
import com.bell.scenic.mapper.ScenicSpotTicketOrderMapper;
import com.bell.scenic.service.IScenicSpotTicketOrderMemberService;
import com.bell.ship.domain.ShipOrder;
import com.bell.ship.domain.ShipOrderMember;
import com.bell.ship.domain.dto.ShipOrderDto;
import com.bell.ship.domain.vo.ShipOrderDetailVo;
import com.bell.ship.mapper.ShipOrderMapper;
import com.bell.ship.mapper.ShipOrderMemberMapper;
import com.bell.ship.service.IShipOrderService;
import com.bell.ship.utils.ShipOrderUtil;
import com.bell.system.mapper.SysUserMapper;
import com.bell.system.service.ICarSmsService;
import com.bell.system.service.ISmsService;
import com.bell.wx.domain.DTO.PayRequestDTO;
import com.bell.wx.service.WxService;
import com.bell.wx.util.WxTemplateUtil;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.bell.common.utils.SecurityUtils.getUserId;
import static org.apache.commons.lang3.SystemUtils.getUserName;

/**
 * 船艇订单评价Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-15
 */
@Service
public class WxServiceImpl implements WxService {
    public static final String REASON_EXPIRED_REFUND = "过期退款";
    public static final String REASON_USER_REFUND = "用户退款";

    private static final Logger log = LoggerFactory.getLogger(WxServiceImpl.class);
    @Resource
    private WxPayUtil wxPayUtil;

    @Resource
    private FishingCompetitionSeasonMapper fishingCompetitionSeasonMapper;

    @Resource
    private FishingCompetitionOrderMapper fishingCompetitionOrderMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private ShipOrderMapper shipOrderMapper;

    @Resource
    private ShipOrderMemberMapper shipOrderMemberMapper;

    @Resource
    private UserScoreLogMapper userScoreLogMapper;

    @Resource
    private UserScoreRuleMapper userScoreRuleMapper;

    @Resource
    private ISmsService smsService;

    @Resource
    private ICarSmsService carSmsService;

    @Resource
    private IShipOrderService iShipOrderService;

    @Resource
    private ScenicSpotMapper scenicSpotMapper;

    @Resource
    private ScenicSpotTicketMapper scenicSpotTicketMapper;

    @Resource
    private ScenicSpotTicketOrderMapper scenicSpotTicketOrderMapper;

    @Resource
    private IScenicSpotTicketOrderMemberService scenicSpotTicketOrderMemberService;

    @Resource
    private CarOrderMapper carOrderMapper;

    @Resource
    private CarOrderInventoryMapper carOrderInventoryMapper;

    @Resource
    private WxTemplateUtil wxTemplateUtil;

    @Autowired
    private ShipOrderUtil shipOrderUtil;

    @Autowired
    private CarBaseInfoMapper carBaseInfoMapper;

    @Autowired
    private CarCompanyMapper carCompanyMapper;

    @Autowired
    private CarModelMapper carModelMapper;

    @Resource
    private CarOrderTenancyTermMapper carOrderTenancyTermMapper;

    @Autowired
    private CarOrderManagerMapper carOrderManagerMapper;

    @Autowired
    private ICarOrderService carOrderService;

    @Autowired
    private AppChallengeCompetitionMapper appChallengeCompetitionMapper;

    @Autowired
    private ChallengeCompetitionOrderTeamMapper challengeCompetitionOrderTeamMapper;


    private static final String REDIS_KEY_PREFIX = "SCENIC_ORDER_EXPIRATION:";

    @Override
    public int notifyRefund(String refundInfoData, String timestamp, String nonce, String serial, String signature) {
        log.info("退款回调开始");
        try {
            WxPayRefundNotifyV3Result wxPayRefundNotifyV3Result = wxPayUtil.parseRefundNotifyResult(refundInfoData, timestamp, nonce, serial, signature);
            WxPayRefundNotifyV3Result.DecryptNotifyResult decryptResult = wxPayRefundNotifyV3Result.getResult();
            // 商户退款单号
            String outRefundNo = decryptResult.getOutRefundNo();
            // 微信退款单号
            String wxRefundId = decryptResult.getRefundId();
            // 退款状态（ SUCCESS/CLOSED）
            String status = decryptResult.getRefundStatus();
            // 退款金额（单位：分）
            Integer amount = decryptResult.getAmount().getRefund();
            log.info("退款单号: {}, 微信退款单号: {}, 状态: {}, 金额: {} 分", outRefundNo, wxRefundId, status, amount);
            if (outRefundNo.startsWith("SO")) {
                log.info("退款类型：景区订单退款");
                return handleScenicSpotRefund(outRefundNo, status, amount, true);
            } else if (outRefundNo.startsWith("KO")) {
                log.info("退款类型：APP景区订单退款");
                return handleScenicSpotRefund(outRefundNo, status, amount, false);
            }
            else if (outRefundNo.startsWith("CO")) {
                log.info("退款类型：APP租车订单退款");
                log.info("微信返回退款状态：{}", status);
                LambdaQueryWrapper<CarOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(CarOrder::getRefundNo, outRefundNo);
                CarOrder carOrder = carOrderMapper.selectOne(lambdaQueryWrapper);
                log.info("数据库当前订单详细信息：{}", carOrder);
                if (null != carOrder && carOrder.getRefundState() != null) {
                    if (carOrder.getRefundState() == 1 && "SUCCESS".equals(status) || carOrder.getRefundState() == 3 && "CLOSED".equals(status)) {
                        log.warn("退款单 {} 已处理，忽略重复回调", outRefundNo);
                        // 返回成功状态码
                        return 200;
                    }
                    // 更新退款状态
                    CarOrder carOrderInfo = new CarOrder();
                    carOrderInfo.setOrderId(carOrder.getOrderId());
                    // 更新为实际的退款到账时间
                    carOrderInfo.setRefundDate(new Date());
                    // 将金额从分转换为元
                    BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    carOrderInfo.setRefundAmount(refundAmount);
                    carOrderInfo.setRefundTotalPrice(refundAmount);
                    // 判断退款是否成功
                    if ("SUCCESS".equals(status)) {
                        carOrderInfo.setRefundState(1);
                        carOrderInfo.setAllOrderRefundState(1);
                        log.info("租车订单退款状态：成功");
                    } else {
                        carOrderInfo.setRefundState(3);
                        carOrderInfo.setAllOrderRefundState(3);
                        log.warn("租车订单退款状态：失败");
                    }
                    log.info("更新租车订单编号:{},的订单状态为{}", carOrder.getOrderNo(), carOrder.getRefundState());
                    carOrderMapper.updateById(carOrderInfo);
                    log.info("租车订单：退款异步回调结束");
                    return 200;
                } else {
                    log.warn("租车订单退款单 {} 不存在", outRefundNo);
                    return 200;
                }
            } else if (outRefundNo.startsWith("CM")) {
                log.info("退款类型：平台租车订单退款");
                return handleCarOrderManagerRefund(outRefundNo, status, amount);
            } else if (outRefundNo.startsWith("FCO")) {
                log.info("退款类型：积分赛平台订单退款");
                return handleFishingCompetitionOrderRefund(outRefundNo, status, amount);
            } else if (outRefundNo.startsWith("CCO")) {
                log.info("退款类型：挑战赛平台订单退款");
                return handleChallengeCompetitionOrderRefund(outRefundNo, status, amount);
            }
            else {
                log.info("退款类型：海钓订单退款");
                log.info("微信返回退款状态：{}", status);
                ShipOrder shipOrder = shipOrderMapper.selectRefundStatusByRefundNo(outRefundNo);
                log.info("数据库当前订单详细信息：{}", shipOrder);
                // 查询数据库，检查退款单是否已退款
                if (null != shipOrder && shipOrder.getRefundState() != null) {
                    if (shipOrder.getRefundState() == 1 && "SUCCESS".equals(status) || shipOrder.getRefundState() == 2 && "CLOSED".equals(status)) {
                        log.warn("退款单 {} 已处理，忽略重复回调", outRefundNo);
                        // 返回成功状态码
                        return 200;
                    }
                    // 更新退款状态
                    ShipOrder shipOrderInfo = new ShipOrder();
                    shipOrderInfo.setOrderId(shipOrder.getOrderId());
                    // 更新为实际的退款到账时间
                    shipOrderInfo.setRefundDate(new Date());
                    shipOrderInfo.setWxRefundId(wxRefundId);
                    // 将金额从分转换为元
                    BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    shipOrderInfo.setRefundAmount(refundAmount);
                    // 判断退款是否成功
                    if ("SUCCESS".equals(status)) {
                        shipOrderInfo.setRefundState(1);
                        log.info("海钓订单退款状态：成功");
                    } else {
                        shipOrderInfo.setRefundState(2);
                        log.warn("海钓订单退款状态：失败");
                    }
                    log.info("更新海钓订单编号:{},的订单状态为{}", shipOrder.getOrderNo(), shipOrderInfo.getRefundState());
                    shipOrderMapper.updateById(shipOrderInfo);
                    log.info("海钓订单：退款异步回调结束");
                    return 200;
                } else {
                    log.warn("海钓订单退款单 {} 不存在", outRefundNo);
                    return 200;
                }
            }
        } catch (Exception e) {
            log.error("退款回调处理异常", e);
            // 返回 HTTP 500 表示处理失败（微信会重试）
            return 500;
        }
    }

    @Override
    public int notifyPay(String payInfoData, String timestamp, String nonce, String serial, String signature) {
        log.info("支付回调开始");
        try {
            // 解析回调数据
            WxPayNotifyV3Result result = wxPayUtil.parseOrderNotifyResult(payInfoData, timestamp, nonce, serial, signature);
            if (result == null || !"SUCCESS".equals(result.getResult().getTradeState())) {
                log.error("支付回调解析失败或交易未成功");
                return 0;
            }
            // 获取订单号和微信返回金额（分）
            int wxTotalFee = result.getResult().getAmount().getTotal();
            // 订单号
            String outTradeNo = result.getResult().getOutTradeNo();
            Map<String, Object> tempOrderData = getFromRedis(outTradeNo, Map.class);

            // 根据Attach的值区分是海钓约船订单的回调，还是赛事订单回调
            if ("shipOrder".equals(result.getResult().getAttach())) {
                // 约船订单的回调
                ShipOrder shipOrder = new ShipOrder();
                Long orderNo = Long.parseLong(outTradeNo);
                shipOrder.setOrderNo(orderNo);
                shipOrder.setState("paid");
                // 根据订单编号更新订单状态为paid 已付款待接单
                shipOrderMapper.updateByOrderNo(shipOrder);
                // 清理Redis临时数据
                deleteFromRedis("SHIP_ORDER:" + outTradeNo);
                // 跟船长发送消息
                sendCaptainSmsForNewOrder(outTradeNo);
                return 1;
            } else if ("fishingCompetition".equals(result.getResult().getAttach())) {
                // 赛事订单回调
                if (tempOrderData == null) {
                    log.error("订单{}不存在或已过期", outTradeNo);
                    return 0;
                }

                // 处理订单数据
                Integer totalFee = (Integer) tempOrderData.get("totalFee");
                // 校验金额一致性
                if (wxTotalFee != totalFee) {
                    log.error("订单{}金额不一致，微信回调：{}，系统预存：{}", outTradeNo, wxTotalFee, totalFee);
                    return 0;
                }

                QueryWrapper<FishingCompetitionOrder> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("order_no", outTradeNo);
                FishingCompetitionOrder existingOrder = fishingCompetitionOrderMapper.selectOne(queryWrapper);
                LambdaUpdateWrapper<FishingCompetitionOrder> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                lambdaUpdateWrapper.set(FishingCompetitionOrder::getState, "completed")
                        .set(FishingCompetitionOrder::getCompleteTime, new Date())
                        .eq(FishingCompetitionOrder::getOrderNo, outTradeNo);
                int update = fishingCompetitionOrderMapper.update(null, lambdaUpdateWrapper);
                if (update == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
                UserScoreRule userScoreRule = userScoreRuleMapper.selectById(9);
                UserScoreLog userScoreLog = new UserScoreLog();
                userScoreLog.setUserId(existingOrder.getUserId());
                userScoreLog.setScore(userScoreRule.getScore());
                userScoreLog.setScoreFrom(userScoreRule.getRuleName());
                userScoreLog.setRuleId(Long.valueOf(9));
                userScoreLog.setSeasonId(existingOrder.getSeasonId());
                userScoreLog.setDataId(existingOrder.getOrderId());
                userScoreLog.setState(1);
                userScoreLog.setScoreType(1);
                userScoreLogMapper.insert(userScoreLog);
                sysUserMapper.updateUserMaxFishScore(existingOrder.getUserId(), Math.toIntExact(userScoreRule.getScore()));
                sysUserMapper.updateUserFishScore(existingOrder.getUserId(), Math.toIntExact(userScoreRule.getScore()));
                // 清理Redis临时数据
                deleteFromRedis(outTradeNo);

                log.info("订单{}支付成功并入库", outTradeNo);
                return 1;
            } else if ("scenicOrder".equals(result.getResult().getAttach())) {

                if (tempOrderData == null) {
                    log.error("订单{}不存在或已过期", outTradeNo);
                    return 0;
                }

                // 处理订单数据
                Integer totalFee = (Integer) tempOrderData.get("totalFee");
                // 校验金额一致性
                if (wxTotalFee != totalFee) {
                    log.error("订单{}金额不一致，微信回调：{}，系统预存：{}", outTradeNo, wxTotalFee, totalFee);
                    return 0;
                }

                // 更新订单状态为已支付
                LambdaUpdateWrapper<ScenicSpotTicketOrder> scenicSpotTicketOrderQuery = new LambdaUpdateWrapper<>();
                scenicSpotTicketOrderQuery.eq(ScenicSpotTicketOrder::getOrderNo, result.getResult().getOutTradeNo())
                        .set(ScenicSpotTicketOrder::getState, "paid")
                        .set(ScenicSpotTicketOrder::getPayTime, new Date());
                scenicSpotTicketOrderMapper.update(null, scenicSpotTicketOrderQuery);

                // 查询订单详情，检查是否为随时退类型
                LambdaQueryWrapper<ScenicSpotTicketOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
                orderQueryWrapper.eq(ScenicSpotTicketOrder::getOrderNo, outTradeNo);
                ScenicSpotTicketOrder order = scenicSpotTicketOrderMapper.selectOne(orderQueryWrapper);

                // 如果是随时退类型（refundRule=1），在Redis中存储有效期信息
                if (order != null && order.getRefundRule() != null && order.getRefundRule() == 1 && order.getEffectEndDate() != null) {
                    // 使用ScenicRefundTask设置订单过期时间
                    setScenicOrderExpiration(outTradeNo, order.getOrderId(), order.getEffectEndDate());
                }

                // 清理Redis临时数据
                deleteFromRedis(outTradeNo);
                return 1;
            } else if ("carOrder".equals(result.getResult().getAttach())) {
                // 更新租车订单状态为已支付
                LambdaUpdateWrapper<CarOrder> carOrderQuery = new LambdaUpdateWrapper<>();
                carOrderQuery.eq(CarOrder::getOrderNo, outTradeNo)
                        .set(CarOrder::getState, "paid")
                        .set(CarOrder::getPayTime, new Date());
                carOrderMapper.update(null, carOrderQuery);

                // 获取临时订单数据，清理并发锁
                Map<String, Object> carOrderTempData = getFromRedis("CAR_ORDER:" + outTradeNo, Map.class);
                if (carOrderTempData != null && carOrderTempData.containsKey("lockKey")) {
                    String lockKey = (String) carOrderTempData.get("lockKey");
                    deleteFromRedis(lockKey);
                    log.info("支付成功，清理并发锁: {}", lockKey);
                }

                // 清理Redis临时数据
                deleteFromRedis("CAR_ORDER:" + outTradeNo);
                // 发短信
                sendCarSmsForNewOrder(outTradeNo);
                return 1;
            } else if ("carContinueOrder".equals(result.getResult().getAttach())) {
                log.info("Redis回调单号: {}", outTradeNo);
                log.info("Redis回调参数: {}", tempOrderData);
                if (tempOrderData == null) {
                    log.error("订单{}不存在或已过期", outTradeNo);
                    return 0;
                }

                // 安全的类型转换
                Long orderId = convertToLong(tempOrderData.get("orderId"));
                Long rentalDays = convertToLong(tempOrderData.get("rentalDays"));
                Date endDate = convertToDate(tempOrderData.get("endDate"));
                Long carId = convertToLong(tempOrderData.get("carId"));
                BigDecimal price = convertToBigDecimal(tempOrderData.get("price"));
                BigDecimal totalAmount = convertToBigDecimal(tempOrderData.get("totalAmount"));

                if (orderId == null || rentalDays == null || endDate == null || carId == null || price == null || totalAmount == null) {
                    log.error("续租订单参数不完整: orderId={}, rentalDays={}, endDate={}, carId={}, price={}, totalAmount={}",
                             orderId, rentalDays, endDate, carId, price, totalAmount);
                    return 0;
                }

                // 验证支付金额是否一致
                if (wxTotalFee != convertToLong(tempOrderData.get("totalFee")).intValue()) {
                    log.error("支付金额不一致: 微信回调金额={}, Redis保存金额={}", wxTotalFee, tempOrderData.get("totalFee"));
                    return 0;
                }

                CarOrder carOrder = carOrderMapper.selectById(orderId);
                if (carOrder == null) {
                    log.error("原始订单不存在: orderId={}", orderId);
                    return 0;
                }
                // 创建订单
                CarOrderTenancyTerm carOrderTenancyTerm = new CarOrderTenancyTerm();
                try {
                    carOrderTenancyTerm.setOrderId(orderId);
                    carOrderTenancyTerm.setOrderNo(outTradeNo);
                    carOrderTenancyTerm.setReturnCarDate(endDate);
                    carOrderTenancyTerm.setCarNo(carOrder.getCarNo());
                    carOrderTenancyTerm.setOrderDay(rentalDays);
                    carOrderTenancyTerm.setPrice(price); // 使用Redis中保存的价格
                    carOrderTenancyTerm.setTotalPrice(totalAmount); // 使用Redis中保存的总金额
                    carOrderTenancyTerm.setCreateBy(String.valueOf(carOrder.getUserId()));
                    carOrderTenancyTerm.setCreateTime(new Date());
                    carOrderTenancyTerm.setState("driving");
                    carOrderTenancyTerm.setRefundState(0);
                    carOrderTenancyTerm.setPayTime(new Date());
                    // 插入订单
                    int insertResult = carOrderTenancyTermMapper.insert(carOrderTenancyTerm);
                    if(insertResult == 0) {
                        throw new RuntimeException("订单创建失败");
                    }

                    List<CarOrderInventory> inventoryList = new ArrayList<>();
                    Calendar calendar = Calendar.getInstance();


                    calendar.setTime(carOrder.getReturnCarTime());
                    calendar.add(Calendar.DAY_OF_MONTH, 1);

                    log.info("续租库存计算开始日期: {}, 续租天数: {}", calendar.getTime(), rentalDays);

                    for (int i = 0; i < rentalDays; i++) {
                        CarOrderInventory inventory = new CarOrderInventory();
                        inventory.setCarId(carId);
                        inventory.setOrderId(orderId);
                        inventory.setTermId(carOrderTenancyTerm.getTermId());
                        inventory.setOrderDate(calendar.getTime());
                        inventory.setIsDel(0);

                        inventoryList.add(inventory);
                        log.debug("生成续租库存记录: 日期={}, termId={}", calendar.getTime(), carOrderTenancyTerm.getTermId());
                        calendar.add(Calendar.DAY_OF_MONTH, 1);
                    }

                    // 验证生成的日期数量与租期天数一致
                    if(inventoryList.size() != rentalDays){
                        throw new RuntimeException("租期天数与生成记录数不匹配");
                    }

                    // 插入库存记录并检查并发冲突
                    try {
                        for (CarOrderInventory inventory : inventoryList) {
                            carOrderInventoryMapper.insert(inventory);
                        }

                        // 插入库存记录后，再次检查是否超卖（排除当前订单的续租记录）
                        CarAvailabilityRequest checkRequest = new CarAvailabilityRequest();
                        checkRequest.setCarId(carId);
                        checkRequest.setStartTime(carOrder.getReturnCarTime());
                        checkRequest.setEndTime(endDate);
                        checkRequest.setExcludeOrderId(carOrder.getOrderId()); // 排除当前订单

                        List<String> unavailableDatesAfterInsert = carOrderService.checkCarAvailabilityExcludeOrder(checkRequest);
                        if(!unavailableDatesAfterInsert.isEmpty()) {
                            // 如果检测到超卖，回滚事务
                            throw new RuntimeException("续租期间车辆库存不足，续租失败");
                        }

                        carOrderMapper.update(null,
                                new LambdaUpdateWrapper<CarOrder>()
                                        .eq(CarOrder::getOrderId, orderId)
                                        .set(CarOrder::getReturnNewCarTime, endDate)
                                        .setSql("total_price = total_price + " + totalAmount));

                        // 清理续租订单的并发锁
                        if (tempOrderData.containsKey("lockKey")) {
                            String lockKey = (String) tempOrderData.get("lockKey");
                            deleteFromRedis(lockKey);
                            log.info("续租支付成功，清理并发锁: {}", lockKey);
                        }

                        deleteFromRedis(outTradeNo);
                        // 短信发送
                        carContinueOrderSms(endDate,carOrder);
                        return 1;
                    } catch (Exception e) {
                        // 如果插入库存记录失败（可能是并发冲突），清理并发锁并抛出异常回滚事务
                        log.error("续租库存记录插入失败: {}", e.getMessage());
                        if (tempOrderData.containsKey("lockKey")) {
                            String lockKey = (String) tempOrderData.get("lockKey");
                            deleteFromRedis(lockKey);
                            log.info("续租失败，清理并发锁: {}", lockKey);
                        }
                        throw new RuntimeException("续租期间车辆已被预订，续租失败");
                    }
                } catch (Exception e) {
                    log.error("订单创建失败: {}", e.getMessage());
                    // 续租订单创建失败时清理并发锁
                    if (tempOrderData != null && tempOrderData.containsKey("lockKey")) {
                        String lockKey = (String) tempOrderData.get("lockKey");
                        deleteFromRedis(lockKey);
                        log.info("续租订单创建失败，清理并发锁: {}", lockKey);
                    }
                    throw new RuntimeException("订单创建失败: " + e.getMessage());
                }
            } else if ("challengeOrder".equals(result.getResult().getAttach())) {
                // 挑战赛订单的回调
                ChallengeCompetitionOrderTeam challengeCompetitionOrderTeam = new ChallengeCompetitionOrderTeam();
                challengeCompetitionOrderTeam.setOrderNo(outTradeNo);
                challengeCompetitionOrderTeam.setState("paid");
                // 根据订单编号更新订单状态为paid
                appChallengeCompetitionMapper.updateOrderStateByOrderNo(challengeCompetitionOrderTeam);
                // 清理Redis临时数据
                deleteFromRedis("CHALLENGE:" + outTradeNo);
                return 1;
            }

        } catch (Exception e) {
            log.error("支付回调处理异常", e);
            return 0;
        }
        return 1;
    }

    // 续租订单短信发送
    private void carContinueOrderSms(Date endDate, CarOrder carOrder){
        try {
            // 判断主单还车提示短信是否已发送，未发送则删除缓存
            if(Boolean.TRUE.equals(redisTemplate.hasKey("CAR_REFUND_SMS:" + carOrder.getOrderNo()))){
                deleteFromRedis("CAR_REFUND_SMS:"+carOrder.getOrderNo());
            }
            // 判断主单还车前30分钟短信是否已发送，未发送则删除缓存
            if(Boolean.TRUE.equals(redisTemplate.hasKey("CAR_REFUND_30:" + carOrder.getOrderNo()))){
                deleteFromRedis("CAR_REFUND_30:"+carOrder.getOrderNo());
            }
            // 判断续租还车提示短信是否已发送，未发送则删除缓存
            if(Boolean.TRUE.equals(redisTemplate.hasKey("CAR_REFUND_X_SMS:" + carOrder.getOrderNo()))){
                deleteFromRedis("CAR_REFUND_X_SMS:"+carOrder.getOrderNo());
            }
            // 判断续租还车前30分钟短信是否已发送，未发送则删除缓存
            if(Boolean.TRUE.equals(redisTemplate.hasKey("CAR_REFUND_X_30:" + carOrder.getOrderNo()))){
                deleteFromRedis("CAR_REFUND_X_30:"+carOrder.getOrderNo());
            }
            // 存入续租单的还车提示短信缓存
            long changeTime = endDate.getTime() - new Date().getTime();
            long changeTime30 = endDate.getTime() - new Date().getTime() - 30*60*1000;
            if(changeTime > 0){
                saveToRedis("CAR_REFUND_X_SMS:"+carOrder.getOrderNo(),carOrder.getOrderNo(),Duration.ofMillis(changeTime));
            }
            if(changeTime30 > 0){
                saveToRedis("CAR_REFUND_X_30:"+carOrder.getOrderNo(),carOrder.getOrderNo(),Duration.ofMillis(changeTime-30));
            }
            // 续租短信发送
            CarOrderSmsVO vo = carOrderMapper.getCarOrderSmsInfo(carOrder.getOrderNo());
            // 乘客短信发送
            carSmsService.userNexOrderSms(vo.getUserPhone(),vo.getUserName(),vo.getCarName());
            // 车企人员短信发送
            carSmsService.serviceNexOrderSms(vo.getCarPhone(),vo.getUserName(),vo.getOrderNo());
        }catch (Exception e){
            log.error("续租订单短信发送异常", e);
        }
    }

    private void sendCaptainSmsForNewOrder(String orderNo) {
        ShipOrder shipOrder = shipOrderMapper.getShipOrderInfoByOrderNo(orderNo);
        SysUser sysUser = sysUserMapper.getById(shipOrder.getUserId());
        SysUser captain = sysUserMapper.getById(shipOrder.getCaptionId());
        smsService.newShipOrderSms(captain.getPhonenumber(), orderNo, sysUser.getRealName());
        try {
            //发送模板消息
            wxTemplateUtil.sendTemplateMsg(shipOrder.getOrderId(), 1);
        } catch (Exception e) {
            log.error("创建订单" + orderNo + ",公众号模板消息发送异常，异常消息：" + e);
        }

    }

    // 新租车订单发消息
    private void sendCarSmsForNewOrder(String orderNo) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            CarOrderSmsVO order = carOrderMapper.getCarOrderSmsInfo(orderNo);
            // 用户发送消息
            carSmsService.createCarOrderSms(order.getUserPhone(), order.getCarName(), format.format(order.getTakeCarTime()),order.getTakeCarName(),order.getOrderNo());
            // 工作人员发消息
            carSmsService.newCarOrderSms(order.getCarPhone(),order.getCarName(),order.getUserName(),format.format(order.getTakeCarTime()),order.getOrderNo());
            // 创建还车日期缓存
            long changeTime = order.getReturnCarTime().getTime()-new Date().getTime();
            long changeTime30 = order.getReturnCarTime().getTime()-new Date().getTime()-30*60*1000;
            if(changeTime > 0){
                saveToRedis("CAR_REFUND_SMS:"+orderNo,orderNo,Duration.ofMillis(changeTime));
            }
            if(changeTime30 > 0){
                saveToRedis("CAR_REFUND_30:"+orderNo,orderNo,Duration.ofMillis(changeTime30));
            }
            // 创建取车日期缓存
            long changeTime2 = order.getTakeCarTime().getTime()-new Date().getTime()-30*60*1000;
            if(changeTime2 > 0){
                saveToRedis("CAR_TAKE_SMS:"+orderNo,orderNo,Duration.ofMillis(changeTime2));
            }
        }catch (Exception e){
            log.error("租车新订单短信发送失败");
        }
    }

    @Override
    @Transactional
    public int refund(ShipOrder shipOrder) {
        log.info("--------------------退款接口start---------------------------");
        // 1. 生成退单ID和金额转换
        Snowflake snowflake = IdUtil.getSnowflake(1, 3);
        long outRefundNo = snowflake.nextId();
        // 订单总金额,以分为单位
        Integer total = shipOrder.getTotalAmount().intValue();
        // 退款金额,以分为单位
        Integer refund = shipOrder.getRefundAmount().intValue();
        // 2. 组装待更新的订单数据（先不操作数据库）
        ShipOrder shipOrderInfo = new ShipOrder();
        shipOrderInfo.setState("cancel");
        shipOrderInfo.setRefuseReasonId(shipOrder.getRefuseReasonId());
        shipOrderInfo.setRefuseReason(shipOrder.getRefuseReason());
        shipOrderInfo.setOrderId(shipOrder.getOrderId());
        shipOrderInfo.setRefundNo(String.valueOf(outRefundNo));
        shipOrderInfo.setRefundUserId(shipOrder.getCaptionId());
        // 将金额从分转换为元
        BigDecimal refundAmount = new BigDecimal(refund).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        // 退款总额
        shipOrderInfo.setRefundAmount(refundAmount);
        // 已出港状态，不存退乘客保险金额
        if (!"out_dock".equals(shipOrder.getState())) {
            // 退乘客保险金额
            shipOrderInfo.setRefundInsurancePrice(shipOrder.getInsurancePrice());
        }
        // 退船费金额
        shipOrderInfo.setRefundBoatPrice(shipOrder.getRefundBoatPrice());
        // 临时的退款时间（回调为准）
        shipOrderInfo.setRefundDate(new Date());
        // 订单的取消时间
        shipOrderInfo.setCancelTime(new Date());

        try {
            log.info("当前退款的订单编号:" + shipOrder.getOrderNo());
            // 3. 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(shipOrder.getOrderNo() + "", outRefundNo + "", shipOrder.getRefuseReason(), refund, total);
            log.info("微信返回退款信息：{}", result);
            // 4. 根据微信结果统一设置退款状态
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                log.info("退款同步接口返回退款状态: {}", refundStatus);
                switch (refundStatus) {
                    case "SUCCESS":
                        shipOrderInfo.setRefundState(1);
                        iShipOrderService.sendCancelCaptainSms(shipOrder.getOrderId());
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        shipOrderInfo.setRefundState(3);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        shipOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                // PROCESSING时同步调用后应更新订单状态为"退款中"，最终状态异步回调更新
                // 5. 统一更新数据库（仅此一次）
                int shipOrderResult = shipOrderMapper.updateById(shipOrderInfo);
                if (shipOrderResult == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            // 6. 异常处理：检查是否已处理过
            ShipOrder orderDetail = shipOrderMapper.selectById(shipOrder.getOrderId());
            // 订单状态：1已退款2退款失败3退款中(防止防连点失效的情况)
            if (orderDetail.getRefundState() == 1 || orderDetail.getRefundState() == 2 || orderDetail.getRefundState() == 3) {
                log.warn("订单编号: {} 已处理，忽略重复处理", shipOrder.getOrderNo());
                throw new RuntimeException("订单已处理，忽略重复处理");
            }
            // 7. 捕获到异常时，标记为退款失败
            log.error("调用微信退款接口异常: {}", e.getMessage());
            // 退款失败
            shipOrderInfo.setRefundState(2);
            log.info("退款同步接口:调用微信退款接口异常订单编号:{},订单状态改为：退款失败", shipOrder.getOrderNo());
            // 更新订单状态
            shipOrderMapper.updateById(shipOrderInfo);
        }
        log.info("--------------------退款接口end---------------------------");
        return 1;
    }

    @Transactional
    @Override
    public Map<String, String> createOrder(PayRequestDTO request) {
        // 查询商品信息
        FishingCompetitionSeason season = fishingCompetitionSeasonMapper.selectById(request.getSeasonId());
        if (ObjectUtils.isNull(season)) {
            log.error("赛事季节{}不存在", request.getSeasonId());
            throw new RuntimeException("商品不存在");
        }

        // 计算金额（单位：分）
        BigDecimal totalAmount = season.getPrice();
        Integer totalFee;
        try {
            totalFee = totalAmount.multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        // 生成唯一订单号
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        String orderNo = String.valueOf(snowflake.nextId());

        SysUser sysUser = sysUserMapper.selectUserById(getUserId());
        // 业务区分标识，海钓赛事
        String attach = "fishingCompetition";
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "赛事报名费-" + season.getSeasonName(),
                    attach
            );

            // 生成小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);

            // 创建订单并入库
            FishingCompetitionOrder fishingCompetitionOrder = new FishingCompetitionOrder();
            fishingCompetitionOrder.setOrderNo(Long.parseLong(orderNo));

            fishingCompetitionOrder.setUserId(getUserId());
            fishingCompetitionOrder.setTotalAmount(new BigDecimal(totalFee).divide(BigDecimal.valueOf(100)));
            fishingCompetitionOrder.setState("created");
            fishingCompetitionOrder.setRefundState(0);
            fishingCompetitionOrder.setPayTime(new Date());
            fishingCompetitionOrder.setCompetitionId(request.getCompetitionId());
            fishingCompetitionOrder.setSeasonId(request.getSeasonId());
            try {
                fishingCompetitionOrderMapper.insert(fishingCompetitionOrder);
            } catch (Exception e) {
                log.error("订单创建失败");
                throw new RuntimeException("订单创建失败");
            }
            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("totalFee", totalFee);
            paymentParams.put("orderNo", orderNo);
            saveToRedis(orderNo, tempOrderData, Duration.ofMinutes(30));

            return paymentParams;
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常");
        }
    }

    /**
     * 订单创建（不含支付，金额为0）
     * @param request 订单参数
     * @return 结果
     */
    @Transactional
    @Override
    public Map<String, String> createOrderNoPay(PayRequestDTO request) {
        // 查询商品信息
        FishingCompetitionSeason season = fishingCompetitionSeasonMapper.selectById(request.getSeasonId());
        if (ObjectUtils.isNull(season)) {
            log.error("赛事季节{}不存在", request.getSeasonId());
            throw new RuntimeException("商品不存在");
        }

//        // 计算金额（单位：分）
//        BigDecimal totalAmount = season.getPrice();
//        Integer totalFee;
//        try {
//            totalFee = totalAmount.multiply(BigDecimal.valueOf(100)).intValueExact();
//        } catch (ArithmeticException e) {
//            log.error("金额计算错误: {}", e.getMessage());
//            throw new RuntimeException("金额计算错误");
//        }

        // 生成唯一订单号
        Snowflake snowflake = IdUtil.getSnowflake(1, 1);
        String orderNo = String.valueOf(snowflake.nextId());

//        SysUser sysUser = sysUserMapper.selectUserById(getUserId());
        // 业务区分标识，海钓赛事
        String attach = "fishingCompetition";
        try {
//            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
//                    orderNo,
//                    totalFee,
//                    sysUser.getMicroAppOpenId(),
//                    "赛事报名费-" + season.getSeasonName(),
//                    attach
//            );
//
//            // 生成小程序支付参数
//            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);

            // 创建订单并入库
            FishingCompetitionOrder fishingCompetitionOrder = new FishingCompetitionOrder();
            fishingCompetitionOrder.setOrderNo(Long.parseLong(orderNo));

            fishingCompetitionOrder.setUserId(getUserId());
            fishingCompetitionOrder.setTotalAmount(BigDecimal.valueOf(0));
            fishingCompetitionOrder.setState("completed");
            fishingCompetitionOrder.setRefundState(0);
            fishingCompetitionOrder.setPayTime(new Date());
            fishingCompetitionOrder.setCompetitionId(request.getCompetitionId());
            fishingCompetitionOrder.setSeasonId(request.getSeasonId());
            try {
                fishingCompetitionOrderMapper.insert(fishingCompetitionOrder);
            } catch (Exception e) {
                log.error("订单创建失败");
                throw new RuntimeException("订单创建失败");
            }
            // 保存临时订单到Redis
//            Map<String, Object> tempOrderData = new HashMap<>();
//            tempOrderData.put("totalFee", totalFee);
//            paymentParams.put("orderNo", orderNo);
//            saveToRedis(orderNo, tempOrderData, Duration.ofMinutes(30));

            return new HashMap<>();
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常");
        }
    }

    /**
     * 海钓约船，创建订单
     *
     * @param shipOrderDto 信息
     * @return 结果
     */
    @Transactional
    @Override
    public Map<String, String> createSeaFishingOrder(ShipOrderDto shipOrderDto) {
        // 查询当前日期船长有没有订单
        ShipOrder shipOrder = shipOrderMapper.selectShipOrderInfoByBookDate(shipOrderDto);
        if (null != shipOrder) {
            log.error("此船长当前日期已经被预约");
            throw new RuntimeException("当前日期已经被预约");
        }
        // 计算金额（单位：分）
        BigDecimal totalAmount = shipOrderDto.getTotalAmount();
        Integer totalFee;
        try {
            totalFee = totalAmount.multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        // 生成唯一订单号
        Snowflake snowflake = IdUtil.getSnowflake(1, 2);
        String orderNo = String.valueOf(snowflake.nextId());
        // 查询用户的openid
        SysUser sysUser = sysUserMapper.selectUserById(shipOrderDto.getUserId());
        // 业务区分标识，租船订单
        String attach = "shipOrder";
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "海钓租船费",
                    attach
            );

            // 生成生成预支付订单，获取小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);

            // 生成海钓租船的代付款订单
            ShipOrder createShipOrder = new ShipOrder();
            createShipOrder.setOrderNo(Long.valueOf(orderNo));
            createShipOrder.setUserId(shipOrderDto.getUserId());
            createShipOrder.setAreaId(shipOrderDto.getAreaId());
            createShipOrder.setShipId(shipOrderDto.getShipId());
            createShipOrder.setCaptionId(shipOrderDto.getCaptionId());
            createShipOrder.setState("created");
            createShipOrder.setTotalAmount(shipOrderDto.getTotalAmount());
            createShipOrder.setBookDate(shipOrderDto.getBookDate());
            createShipOrder.setOutDock(shipOrderDto.getOutDock());
            createShipOrder.setInDock(shipOrderDto.getInDock());
            createShipOrder.setRemark(shipOrderDto.getRemark());
            createShipOrder.setCharteringMode(0);
            createShipOrder.setPayTime(new Date());
            createShipOrder.setCreateTime(new Date());
            //2025/05/20追加业务start 船费与乘客保险费
            createShipOrder.setBoatPrice(shipOrderDto.getBoatPrice());
            createShipOrder.setInsurancePrice(shipOrderDto.getInsurancePrice());
            createShipOrder.setClubId(shipOrderDto.getClubId());
            //2025/05/20追加业务end 船费与乘客保险费
            int orderResult = shipOrderMapper.insert(createShipOrder);
            if (orderResult == 0) {
                log.error("订单创建失败");
                throw new RuntimeException("订单创建失败");
            }
            // 插入订单成员表
            for (Map<String, Object> stringObjectMap : shipOrderDto.getShipOrderMemberList()) {
                ShipOrderMember shipOrderMember = new ShipOrderMember();
                shipOrderMember.setOrderId(createShipOrder.getOrderId());
                shipOrderMember.setUserId(Long.parseLong(stringObjectMap.get("userId").toString()));
                int orderMemberResult = shipOrderMemberMapper.insert(shipOrderMember);
                if (orderMemberResult == 0) {
                    log.error("插入订单成员表失败，订单创建失败");
                    throw new RuntimeException("订单创建失败");
                }
            }

            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("orderNo", orderNo);
            tempOrderData.put("totalFee", totalFee);
            tempOrderData.put("userId", shipOrderDto.getUserId());
            paymentParams.put("orderNo", orderNo);
            saveToRedis("SHIP_ORDER:" + orderNo, tempOrderData, Duration.ofHours(1));
            saveToRedis("SHIP_ORDER_PAID:" + orderNo, tempOrderData, Duration.ofHours(6));
            saveToRedis("SHIP_ORDER_PAID_TWELVE:" + orderNo, tempOrderData, Duration.ofHours(12));
            // 计算当前时间与预约时间的时间差（分钟）
            Duration timeLeft = Duration.between(Instant.now(), shipOrderDto.getBookDate().toInstant());
            //判断是否为负数
            if (timeLeft.isNegative()) {
                log.error("订单的预约时间非法，与当前时间计算时间差小于等于0，订单ID为" + createShipOrder.getOrderId());
            } else {
                log.info("订单自动退款时间剩余" + timeLeft + "，订单id:" + createShipOrder.getOrderId());
                saveToRedis("SHIP_ORDER_CANCEL:" + createShipOrder.getOrderId(), tempOrderData, timeLeft);
            }
            return paymentParams;
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常");
        }
    }

    /**
     * 海钓约船，修改订单
     *
     * @param shipOrderDto
     * @return
     */
    @Override
    @Transactional
    public Map<String, String> updateSeaFishingOrder(ShipOrderDto shipOrderDto) {
        //根据订单id获取订单编号及支付金额
        ShipOrderDetailVo shipOrderDetailVo = shipOrderMapper.selectShipOrderInfoByOrderId(shipOrderDto.getOrderId());
        if (shipOrderDetailVo == null || shipOrderDetailVo.getOrderNo() == null) {
            log.error("根据订单id获取待支付订单编号失败，订单id为" + shipOrderDto.getOrderId());
            throw new RuntimeException("订单支付失败");
        }

        // 计算金额（单位：分）
        BigDecimal totalAmount = shipOrderDetailVo.getTotalAmount();
        Integer totalFee;
        try {
            totalFee = totalAmount.multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        String orderNo = String.valueOf(shipOrderDetailVo.getOrderNo());
        // 查询用户的openid
        SysUser sysUser = sysUserMapper.selectUserById(shipOrderDto.getUserId());
        // 业务区分标识，租船订单
        String attach = "shipOrder";
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "海钓租船费",
                    attach
            );

            // 生成生成预支付订单，获取小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);
            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("orderNo", orderNo);
            tempOrderData.put("totalFee", totalFee);
            tempOrderData.put("userId", shipOrderDto.getUserId());
            paymentParams.put("orderNo", orderNo);
            saveToRedis("SHIP_ORDER:" + orderNo, tempOrderData, Duration.ofHours(1));
            return paymentParams;
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常");
        }
    }

    /**
     * 生成订单号
     *
     * @return 订单号
     */
    private String generateOrderNo() {
        Snowflake snowflake = IdUtil.getSnowflake(1, 4);
        return String.valueOf(snowflake.nextId());
    }

    /**
     * 验证订单参数
     *
     * @param request 订单请求
     */
    private void validateOrderRequest(ScenicOrderDTO request) {
        if (request.getScenicSpotId() == null || request.getTicketId() == null) {
            throw new RuntimeException("景点id或门票id为空");
        }
        if (request.getTotalAmount() == null || request.getTotalAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("订单金额无效");
        }
        if (request.getTicketQuantity() == null || request.getTicketQuantity() <= 0) {
            throw new RuntimeException("门票数量无效");
        }
//        if(request.getMembers() == null || request.getMembers().isEmpty()) {
//            throw new RuntimeException("订单成员信息不能为空");
//        }
    }

    @Override
    @Transactional
    public Map<String, String> createScenicOrder(ScenicOrderDTO request) {
        // 参数校验
        validateOrderRequest(request);

        ScenicSpot scenicSpot = scenicSpotMapper.selectById(request.getScenicSpotId());
        ScenicSpotTicket scenicSpotTicket = scenicSpotTicketMapper.selectById(request.getTicketId());
        if (scenicSpot == null || scenicSpotTicket == null) {
            throw new RuntimeException("景点或门票不存在");
        }

        // 免费票逻辑
        if (request.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
            return createFreeTicketOrder(request, scenicSpot, scenicSpotTicket);
        }
        // 付费票逻辑
        return createPaidTicketOrder(request, scenicSpot, scenicSpotTicket);
    }

    /**
     * 创建免费门票订单
     */
    private Map<String, String> createFreeTicketOrder(ScenicOrderDTO request, ScenicSpot scenicSpot, ScenicSpotTicket scenicSpotTicket) {
        String orderNo = generateOrderNo();
        SysUser sysUser = validateUser();

        // 创建订单
        ScenicSpotTicketOrder scenicSpotTicketOrder = createTicketOrder(request, scenicSpot, scenicSpotTicket, orderNo, "paid");
        scenicSpotTicketOrder.setPayTime(new Date());
        saveOrder(scenicSpotTicketOrder);

        // 保存订单成员
//        saveOrderMembers(request.getMembers(), scenicSpotTicketOrder.getOrderId());
        
        Map<String, String> result = new HashMap<>();
        result.put("orderNo", orderNo);
        result.put("msg", "免费门票订单创建成功");
        return result;
    }

    /**
     * 创建付费门票订单
     */
    private Map<String, String> createPaidTicketOrder(ScenicOrderDTO request, ScenicSpot scenicSpot, ScenicSpotTicket scenicSpotTicket) {
        String orderNo = generateOrderNo();
        SysUser sysUser = validateUser();

        // 创建订单
        ScenicSpotTicketOrder scenicSpotTicketOrder = createTicketOrder(request, scenicSpot, scenicSpotTicket, orderNo, "created");
        saveOrder(scenicSpotTicketOrder);

        // 保存订单成员
//        saveOrderMembers(request.getMembers(), scenicSpotTicketOrder.getOrderId());
        
        try {
            // 调用微信支付接口
            String attach = "scenicOrder";
            int totalFee = request.getTotalAmount().multiply(new BigDecimal(100)).intValue();
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "景区报名费",
                    attach
            );

            // 生成小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);

            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("totalFee", totalFee);
            tempOrderData.put("orderNo", orderNo);
            paymentParams.put("orderNo", orderNo);
            saveToRedis(orderNo, tempOrderData, Duration.ofMinutes(30));

            return paymentParams;
        } catch (Exception e) {
            log.error("创建支付订单失败: {}", e.getMessage());
            throw new RuntimeException("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户信息
     */
    private SysUser validateUser() {
        SysUser sysUser = sysUserMapper.selectUserById(getUserId());
        if (sysUser == null || sysUser.getMicroAppOpenId() == null) {
            throw new RuntimeException("用户信息无效");
        }
        return sysUser;
    }

    /**
     * 创建门票订单对象
     */
    private ScenicSpotTicketOrder createTicketOrder(ScenicOrderDTO request, ScenicSpot scenicSpot,
                                                    ScenicSpotTicket scenicSpotTicket, String orderNo, String state) {
        ScenicSpotTicketOrder order = new ScenicSpotTicketOrder();
        order.setOrderNo(orderNo);
        order.setScenicSpotId(Long.valueOf(request.getScenicSpotId()));
        order.setTicketId(Long.valueOf(request.getTicketId()));
        order.setTicketName(scenicSpotTicket.getTicketName());
        order.setTicketPrice(scenicSpotTicket.getTicketPrice());
        order.setEffectStartDate(scenicSpotTicket.getEffectStartDate());
        order.setEffectEndDate(scenicSpotTicket.getEffectEndDate());
        order.setRefundRule(scenicSpotTicket.getRefundRule());
        order.setScenicSpotName(scenicSpot.getScenicSpotName());
        order.setTotalPrice(request.getTotalAmount());
        order.setTicketQuantity(request.getTicketQuantity());
        if (scenicSpotTicket.getRefundRule() == 3) {
            order.setRefundTime(scenicSpotTicket.getRefundTime());
        }
        order.setState(state);
        order.setUserId(getUserId());
        order.setEntranceStartTime(scenicSpotTicket.getEntranceStartTime());
        order.setEntranceEndTime(scenicSpotTicket.getEntranceEndTime());
        return order;
    }

    /**
     * 保存订单到数据库
     */
    private void saveOrder(ScenicSpotTicketOrder order) {
        int insertResult = scenicSpotTicketOrderMapper.insert(order);
        if (insertResult == 0) {
            throw new RuntimeException("订单创建失败");
        }
    }

    /**
     * 保存订单成员信息
     */
    private void saveOrderMembers(List<AppMemberVO> memberInfos, Long orderId) {
        List<ScenicSpotTicketOrderMember> members = memberInfos.stream()
                .map(member -> {
                    ScenicSpotTicketOrderMember orderMember = new ScenicSpotTicketOrderMember();
                    orderMember.setOrderId(orderId);
                    orderMember.setMemberName(member.getMemberName());
                    orderMember.setCertNo(member.getCertNo());
                    orderMember.setTel(member.getTel());
                    orderMember.setUserId(getUserId());
                    return orderMember;
                })
                .collect(Collectors.toList());

        if (!members.isEmpty()) {
            boolean saveResult = scenicSpotTicketOrderMemberService.saveBatch(members);
            if (!saveResult) {
                throw new RuntimeException("订单成员保存失败");
            }
        }
    }

    /**
     * 景区平台退款
     * @param scenicSpotTicketOrder
     * @return
     */
    @Override
    public int refundScenicOrder(ScenicSpotTicketOrder scenicSpotTicketOrder) {
        String refundReason = scenicSpotTicketOrder.getRefundReason();
        // 更新订单状态为已取消。插入退款单号等信息
        ScenicSpotTicketOrder ScenicSpotTicketOrderInfo = new ScenicSpotTicketOrder();
        ScenicSpotTicketOrderInfo.setOrderId(scenicSpotTicketOrder.getOrderId());
        // 将金额从分转换为元
        BigDecimal refundAmount = scenicSpotTicketOrder.getRefundAmount();
        ScenicSpotTicketOrderInfo.setRefundAmount(refundAmount);
        ScenicSpotTicketOrderInfo.setRefundDate(new Date());
        String reason;
        if (scenicSpotTicketOrder.getRefundUserId() == null) {
            reason = refundReason;
            ScenicSpotTicketOrderInfo.setRefundUserId(getUserId());
        } else {
            reason = REASON_EXPIRED_REFUND;
        }


        // 判断是否为免费门票
        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            // 免费门票直接更新状态为已退款
            ScenicSpotTicketOrderInfo.setState("refund");
            ScenicSpotTicketOrderInfo.setRefundReason(refundReason);
            scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
            log.info("免费门票退款成功，订单编号：{}", scenicSpotTicketOrder.getOrderNo());
            return 1;
        }

        // 付费门票走微信退款流程
        // 生成一个退单ID
        Snowflake snowflake = IdUtil.getSnowflake(1, 5);
        String outRefundNo = String.valueOf("SO" + snowflake.nextId());
        Integer total = refundAmount.multiply(new BigDecimal("100")).intValue();
        ScenicSpotTicketOrderInfo.setRefundNo(outRefundNo);
        //计算原始订单金额
        BigDecimal totalPrice = scenicSpotTicketOrder.getTotalPrice();
        int totalAmount = totalPrice.multiply(new BigDecimal("100")).intValue();

        try {
            log.info("退款的订单编号为" + scenicSpotTicketOrder.getOrderNo());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(scenicSpotTicketOrder.getOrderNo(), outRefundNo, reason, total, totalAmount);
            log.info("订单编号{}退款信息：{}", scenicSpotTicketOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                switch (refundStatus) {
                    case "SUCCESS":
                        ScenicSpotTicketOrderInfo.setState("refund");
                        ScenicSpotTicketOrderInfo.setRefundState(1);
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        ScenicSpotTicketOrderInfo.setState("refunding");
                        ScenicSpotTicketOrderInfo.setRefundState(1);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        ScenicSpotTicketOrderInfo.setState("refund_fail");
                        ScenicSpotTicketOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                ScenicSpotTicketOrderInfo.setRefundReason(refundReason);
                return scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            Long orderId = scenicSpotTicketOrder.getOrderId();
            ScenicSpotTicketOrder orderDetail = scenicSpotTicketOrderMapper.selectById(orderId);

            if (orderDetail != null) {
                String orderState = orderDetail.getState();
                if ("refund".equals(orderState) || "refunding".equals(orderState)) {
                    log.warn("订单[ID:{} No:{}] 已处理，忽略重复退款请求", orderId, scenicSpotTicketOrder.getOrderNo());
                    throw new RuntimeException(String.format("订单%s已处理，请勿重复操作", scenicSpotTicketOrder.getOrderNo()));
                }
            }

            log.error("景区订单[ID:{}]退款异常 - 错误信息: {}", orderId, e.getMessage(), e);
            log.info("订单[No:{}]状态变更为退款失败", scenicSpotTicketOrder.getOrderNo());

            ScenicSpotTicketOrderInfo.setState("refund_fail");
            ScenicSpotTicketOrderInfo.setRefundState(2);
            ScenicSpotTicketOrderInfo.setRefundReason(refundReason);
            //判断是否为定时任务自动退款失败的场合
            if ("auto".equals(ScenicSpotTicketOrderInfo.getSearchValue())) {
                ScenicSpotTicketOrderInfo.setRefundReason("过期自动退");
            }
            scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
            return 0;
        }

    }

    /**
     * 景区小程序退款
     * @param scenicSpotTicketOrder
     * @return
     */
    @Override
    public int refundAppScenicOrder(ScenicSpotTicketOrder scenicSpotTicketOrder) {
        // 更新订单状态为已取消。插入退款单号等信息
        ScenicSpotTicketOrder ScenicSpotTicketOrderInfo = new ScenicSpotTicketOrder();
        ScenicSpotTicketOrderInfo.setOrderId(scenicSpotTicketOrder.getOrderId());
        // 将金额从分转换为元
        BigDecimal refundAmount = scenicSpotTicketOrder.getRefundAmount();
        ScenicSpotTicketOrderInfo.setRefundAmount(refundAmount);
        ScenicSpotTicketOrderInfo.setRefundDate(new Date());
        ScenicSpotTicketOrderInfo.setRefundUserId(getUserId());


        // 判断是否为免费门票
        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            // 免费门票直接更新状态为已退款
            ScenicSpotTicketOrderInfo.setState("refund");
            scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
            log.info("免费门票退款成功，订单编号：{}", scenicSpotTicketOrder.getOrderNo());
            return 1;
        }

        // 付费门票走微信退款流程
        // 生成一个退单ID
        Snowflake snowflake = IdUtil.getSnowflake(1, 6);
        String outRefundNo = String.valueOf("KO" + snowflake.nextId());
        Integer total = refundAmount.multiply(new BigDecimal("100")).intValue();
        ScenicSpotTicketOrderInfo.setRefundNo(outRefundNo);

        try {
            log.info("退款的订单编号为" + scenicSpotTicketOrder.getOrderNo());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(scenicSpotTicketOrder.getOrderNo(), outRefundNo, REASON_USER_REFUND, total, total);
            log.info("订单编号{}退款信息：{}", scenicSpotTicketOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                switch (refundStatus) {
                    case "SUCCESS":
                        ScenicSpotTicketOrderInfo.setState("refund");
                        ScenicSpotTicketOrderInfo.setRefundState(1);
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        ScenicSpotTicketOrderInfo.setState("refunding");
                        ScenicSpotTicketOrderInfo.setRefundState(1);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        ScenicSpotTicketOrderInfo.setState("refund_fail");
                        ScenicSpotTicketOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            Long orderId = scenicSpotTicketOrder.getOrderId();
            ScenicSpotTicketOrder orderDetail = scenicSpotTicketOrderMapper.selectById(orderId);

            if (orderDetail != null) {
                String orderState = orderDetail.getState();
                if ("refund".equals(orderState) || "refunding".equals(orderState)) {
                    log.warn("订单[ID:{} No:{}] 已处理，忽略重复退款请求", orderId, scenicSpotTicketOrder.getOrderNo());
                    throw new RuntimeException(String.format("订单%s已处理，请勿重复操作", scenicSpotTicketOrder.getOrderNo()));
                }
            }

            log.error("景区订单[ID:{}]退款异常 - 错误信息: {}", orderId, e.getMessage(), e);
            log.info("订单[No:{}]状态变更为退款失败", scenicSpotTicketOrder.getOrderNo());

            ScenicSpotTicketOrderInfo.setState("refund_fail");
            ScenicSpotTicketOrderInfo.setRefundState(2);
            ScenicSpotTicketOrderInfo.setRefundReason("用户申请退款");
            scenicSpotTicketOrderMapper.updateById(ScenicSpotTicketOrderInfo);
        }
        return 0;
    }

    private <T> T getFromRedis(String key, Class<T> clazz) {
        try {
            Object value = redisTemplate.opsForValue().get(key);
            if (value != null && clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            return null;
        } catch (Exception e) {
            log.error("Redis读取异常，key: {}", key, e);
            return null;
        }
    }

    private void saveToRedis(String key, Object value, Duration timeout) {
        try {
            redisTemplate.opsForValue().set(key, value, timeout);
        } catch (Exception e) {
            log.error("Redis写入异常，key: {}", key, e);
        }
    }

    private void deleteFromRedis(String key) {
        try {
            redisTemplate.delete(key);
        } catch (Exception e) {
            log.error("Redis删除异常，key: {}", key, e);
        }
    }


    public void setScenicOrderExpiration(String orderNo, Long orderId, Date effectEndDate) {
        if (orderNo == null || orderId == null || effectEndDate == null) {
            log.warn("设置订单过期时间失败：参数不完整");
            return;
        }

        try {
            // 将effectEndDate设置为当天的23:59:59
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(effectEndDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endOfDay = calendar.getTime();

            // 计算从当前时间到有效期结束的时间差（秒）
            Date now = new Date();
            long expirationSeconds = (endOfDay.getTime() - now.getTime()) / 1000;

            if (expirationSeconds > 0) {
                String redisKey = REDIS_KEY_PREFIX + orderNo;
                redisTemplate.opsForValue().set(redisKey, orderId, Duration.ofSeconds(expirationSeconds));
                log.info("订单{}设置了过期时间，将在{}过期", orderNo, endOfDay);
            } else {
                log.warn("订单{}的有效期已过期，不设置Redis过期时间", orderNo);
            }
        } catch (Exception e) {
            log.error("设置订单{}过期时间失败: {}", orderNo, e.getMessage());
        }
    }

    /**
     * 海钓订单客户退款
     * @param shipOrder
     * @return
     */
    @Override
    @Transactional
    public int refundShipOrderByCustomer(ShipOrder shipOrder) {
        // 生成一个退单ID
        Snowflake snowflake = IdUtil.getSnowflake(1, 3);
        long outRefundNo = snowflake.nextId();
        // 价格转成以分为单位
        Integer total = shipOrder.getRefundAmount().intValue();
        // 更新订单状态为已取消。插入退款单号等信息
        ShipOrder shipOrderInfo = new ShipOrder();
        //订单状态
        shipOrderInfo.setState("cancel");
        //退款原因ID
        shipOrderInfo.setRefundReasonId(shipOrder.getRefundReasonId());
        //退款原因
        shipOrderInfo.setRefundReason(shipOrder.getRefundReason());
        //订单id
        shipOrderInfo.setOrderId(shipOrder.getOrderId());
        //商户退款单号（唯一）
        shipOrderInfo.setRefundNo(String.valueOf(outRefundNo));
        //退款人id
        shipOrderInfo.setRefundUserId(shipOrder.getUserId());
        //退款船费
        shipOrderInfo.setRefundBoatPrice(shipOrder.getRefundBoatPrice());
        //退款保险
        shipOrderInfo.setRefundInsurancePrice(shipOrder.getRefundInsurancePrice());
        //临时退款时间，等待回调更新
        shipOrderInfo.setRefundDate(new Date());
        //取消时间
        shipOrderInfo.setCancelTime(new Date());
        //1全额退2部分退
        shipOrderInfo.setRefundType(shipOrder.getRefundType());
        // 将金额从分转换为元
        BigDecimal refundAmount = new BigDecimal(total).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //退款总金额
        shipOrderInfo.setRefundAmount(refundAmount);
        //订单退款状态为退款中
        shipOrderInfo.setRefundState(3);
        //取消订单来源：0无1用户取消2船长取消3平台取消
        shipOrderInfo.setRefundUserType(1);

        try {
            log.info("退款的订单编号为" + shipOrder.getOrderNo());
            ShipOrder totalAmountOrder = shipOrderMapper.getTotalAmountByOrderId(shipOrder);
            BigDecimal fen = shipOrderUtil.getFen(totalAmountOrder.getTotalAmount());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(shipOrder.getOrderNo() + "", outRefundNo + "", shipOrder.getRefuseReason(), total, fen.intValue());
            log.info("订单编号{}退款信息：{}", shipOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                ShipOrder orderInfo = new ShipOrder();
                orderInfo.setOrderId(shipOrder.getOrderId());
                switch (refundStatus) {
                    case "SUCCESS":
                        orderInfo.setRefundState(1);
                        iShipOrderService.sendCancelCaptainSms(shipOrder.getOrderId());
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        orderInfo.setRefundState(3);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        orderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                // PROCESSING时同步调用后应更新订单状态为"退款中"，最终状态异步回调更新
//                shipOrderMapper.updateById(orderInfo);
                shipOrderInfo.setRefundState(orderInfo.getRefundState());
                int shipOrderResult = shipOrderMapper.updateById(shipOrderInfo);
                if (shipOrderResult == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            // 查询订单信息
            ShipOrder orderDetail = shipOrderMapper.selectById(shipOrder.getOrderId());
            // 订单状态：1已退款2退款失败3退款中(防止防连点失效的情况)
            if (orderDetail.getRefundState() == 1 || orderDetail.getRefundState() == 2 || orderDetail.getRefundState() == 3) {
                log.warn("订单编号: {} 已处理，忽略重复处理", shipOrder.getOrderNo());
                throw new RuntimeException("订单已处理，忽略重复处理");
            }
            // 其他异常
            log.error("调用微信退款接口异常: {}", e.getMessage());
            // 退款失败
            shipOrderInfo.setRefundState(2);
            log.info("退款同步接口:调用微信退款接口异常订单编号:{},订单状态改为：退款失败", shipOrder.getOrderNo());
            // 更新订单状态
            shipOrderMapper.updateById(shipOrderInfo);
        }
        return 0;
    }

    /**
     * 海钓订单平台退款
     * @param shipOrder
     * @return
     */
    @Override
    @Transactional
    public int refundShipOrderByManager(ShipOrder shipOrder) {
        // 生成一个退单ID
        Snowflake snowflake = IdUtil.getSnowflake(1, 3);
        long outRefundNo = snowflake.nextId();
        // 价格转成以分为单位
        Integer total = shipOrder.getRefundAmount().intValue();
        // 更新订单状态为已取消。插入退款单号等信息
        ShipOrder shipOrderInfo = new ShipOrder();
        //订单状态
        shipOrderInfo.setState("cancel");
        //平台退款原因
        shipOrderInfo.setPlatformRefundReason(shipOrder.getRefundReason());
        //订单id
        shipOrderInfo.setOrderId(shipOrder.getOrderId());
        //商户退款单号（唯一）
        shipOrderInfo.setRefundNo(String.valueOf(outRefundNo));
        //退款人id
        shipOrderInfo.setRefundUserId(shipOrder.getRefundUserId());
        //退款船费
        shipOrderInfo.setRefundBoatPrice(shipOrder.getRefundBoatPrice());
        //退款保险
        shipOrderInfo.setRefundInsurancePrice(shipOrder.getRefundInsurancePrice());
        //临时退款时间，等待回调更新
        shipOrderInfo.setRefundDate(new Date());
        //取消时间
        shipOrderInfo.setCancelTime(new Date());
        //1全额退2部分退
        shipOrderInfo.setRefundType(shipOrder.getRefundType());
        // 将金额从分转换为元
        BigDecimal refundAmount = new BigDecimal(total).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //退款总金额
        shipOrderInfo.setRefundAmount(refundAmount);
        //取消订单来源：0无1用户取消2船长取消3平台取消
        shipOrderInfo.setRefundUserType(3);
        //订单退款状态为退款中
        shipOrderInfo.setRefundState(3);

        try {
            log.info("退款的订单编号为" + shipOrder.getOrderNo());
            ShipOrder totalAmountOrder = shipOrderMapper.getTotalAmountByOrderId(shipOrder);
            BigDecimal fen = shipOrderUtil.getFen(totalAmountOrder.getTotalAmount());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(shipOrder.getOrderNo() + "", outRefundNo + "", shipOrder.getRefuseReason(), total, fen.intValue());
            log.info("订单编号{}退款信息：{}", shipOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                ShipOrder orderInfo = new ShipOrder();
                orderInfo.setOrderId(shipOrder.getOrderId());
                switch (refundStatus) {
                    case "SUCCESS":
                        orderInfo.setRefundState(1);
                        iShipOrderService.sendCancelCaptainSms(shipOrder.getOrderId());
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        orderInfo.setRefundState(3);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        orderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                // PROCESSING时同步调用后应更新订单状态为"退款中"，最终状态异步回调更新
//                shipOrderMapper.updateById(orderInfo);
                shipOrderInfo.setRefundState(orderInfo.getRefundState());
                int shipOrderResult = shipOrderMapper.updateById(shipOrderInfo);
                if (shipOrderResult == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            // 查询订单信息
            ShipOrder orderDetail = shipOrderMapper.selectById(shipOrder.getOrderId());
            // 订单状态：1已退款2退款失败3退款中(防止防连点失效的情况)
            if (orderDetail.getRefundState() == 1 || orderDetail.getRefundState() == 2 || orderDetail.getRefundState() == 3) {
                log.warn("订单编号: {} 已处理，忽略重复处理", shipOrder.getOrderNo());
                throw new RuntimeException("退款失败，疑似账户余额不足");
            }
            // 其他异常
            log.error("调用微信退款接口异常: {}", e.getMessage());
            // 退款失败
            shipOrderInfo.setRefundState(2);
            log.info("退款同步接口:调用微信退款接口异常订单编号:{},订单状态改为：退款失败", shipOrder.getOrderNo());
            // 更新订单状态
            shipOrderMapper.updateById(shipOrderInfo);
        }
        return 0;
    }

    /**
     * 处理景区订单退款
     * 
     * @param outRefundNo 退款单号
     * @param status 退款状态
     * @param amount 退款金额（单位：分）
     * @param needSendSms 是否需要发送短信通知
     * @return 处理结果状态码
     */
    private int handleScenicSpotRefund(String outRefundNo, String status, Integer amount, boolean needSendSms) {
        LambdaQueryWrapper<ScenicSpotTicketOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ScenicSpotTicketOrder::getRefundNo, outRefundNo);
        ScenicSpotTicketOrder scenicSpotTicketOrder = scenicSpotTicketOrderMapper.selectOne(queryWrapper);
        if (null != scenicSpotTicketOrder && scenicSpotTicketOrder.getRefundState() != null) {
            if (scenicSpotTicketOrder.getState().equals("refund") && "SUCCESS".equals(status) || 
                scenicSpotTicketOrder.getState().equals("refund_fail") && "CLOSED".equals(status)) {
                log.warn("退款单 {} 已处理，忽略重复回调", outRefundNo);
                // 返回成功状态码
                return 200;
            }
            // 更新退款状态
            ScenicSpotTicketOrder scenicSpotTicketOrderInfo = new ScenicSpotTicketOrder();
            scenicSpotTicketOrderInfo.setOrderId(scenicSpotTicketOrder.getOrderId());
            BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            scenicSpotTicketOrderInfo.setRefundAmount(refundAmount);
            scenicSpotTicketOrderInfo.setRefundDate(new Date());
            if ("SUCCESS".equals(status)) {
                if (refundAmount.compareTo(scenicSpotTicketOrder.getTotalPrice()) == 0) {
                    scenicSpotTicketOrderInfo.setRefundState(1);
                } else {
                    scenicSpotTicketOrderInfo.setRefundState(2);
                }
                scenicSpotTicketOrderInfo.setState("refund");
                // 发送短信通知
                if (needSendSms) {
                    SysUser sysUser = sysUserMapper.getById(scenicSpotTicketOrder.getUserId());
                    if (null != scenicSpotTicketOrder.getRefundUserId() && scenicSpotTicketOrder.getRefundUserId() == -1) {
                        smsService.ticketExpireSms(sysUser.getPhonenumber(), scenicSpotTicketOrder.getOrderNo(), scenicSpotTicketOrder.getScenicSpotName());
                    } else {
                        smsService.ticketRefundSms(sysUser.getPhonenumber(), scenicSpotTicketOrder.getOrderNo(), scenicSpotTicketOrder.getScenicSpotName());
                    }
                }
                log.info("退款状态：成功");
            } else {
                scenicSpotTicketOrderInfo.setState("refund_fail");
                log.warn("退款状态：失败");
            }
            scenicSpotTicketOrderMapper.updateById(scenicSpotTicketOrderInfo);
            log.info("退款回调结束");
            return 200;
        } else {
            log.warn("退款单 {} 不存在", outRefundNo);
            return 200;
        }
    }

    @Override
    @Transactional
    public Map<String, String> createCarOrder(CarOrderDTO request) {
        // 参数校验
        if(request.getCarId() == null) {
            throw new RuntimeException("车辆ID不能为空");
        }
        if(request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("订单金额无效");
        }
        if(request.getRentalDays() == null || request.getRentalDays() <= 0) {
            throw new RuntimeException("租车天数无效");
        }
        if(request.getStartDate() == null || request.getEndDate() == null) {
            throw new RuntimeException("租车日期无效");
        }

        // 验证取车时间至少大于当前时间一个小时
        Date currentTime = new Date();
        long oneHourInMillis = 60 * 60 * 1000; // 一小时的毫秒数
        Date minimumStartTime = new Date(currentTime.getTime() + oneHourInMillis);

        if(request.getStartDate().before(minimumStartTime)) {
            throw new RuntimeException("取车时间至少需要大于当前时间一个小时");
        }

        // 高并发处理 - 使用日期格式作为key防止重复提交
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String concurrencyKey = "CAR_ORDER_PROCESSING_" + request.getCarId() + "_" +
                               dateFormat.format(request.getStartDate()) + "_" + dateFormat.format(request.getEndDate()) + "_" +
                               System.currentTimeMillis();

        // 检查是否有正在处理的订单
        final String lockKey = "CAR_ORDER_LOCK_" + request.getCarId() + "_" +
                              dateFormat.format(request.getStartDate()) + "_" + dateFormat.format(request.getEndDate());

        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(
            lockKey,
            concurrencyKey,
            Duration.ofSeconds(30)
        );

        if (!Boolean.TRUE.equals(lockAcquired)) {
            throw new RuntimeException("该车辆在此时间段正在被其他用户预订，请稍后重试");
        }

        CarAvailabilityRequest carAvailabilityRequest = new CarAvailabilityRequest();
        carAvailabilityRequest.setCarId(request.getCarId());
        carAvailabilityRequest.setStartTime(request.getStartDate());
        carAvailabilityRequest.setEndTime(request.getEndDate());
        // 检查订单状态
        List<String> unavailableDates = carOrderService.checkCarAvailability(carAvailabilityRequest);
        if(!CollectionUtils.isEmpty(unavailableDates)) {
            throw new RuntimeException("车辆在指定日期内已被预订，请选择其他日期");
        }

        // 计算金额（单位：分）
//        BigDecimal totalAmount = request.getPrice().multiply(BigDecimal.valueOf(request.getRentalDays()));
        Integer totalFee;
        try {
            totalFee = request.getTotalAmount().multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        // 生成唯一订单号
        Snowflake snowflake = IdUtil.getSnowflake(1, 6);
        String orderNo = String.valueOf(snowflake.nextId());

        // 先创建订单占用库存
        try {
            // 获取车辆信息
            CarBaseInfo carBaseInfo = carBaseInfoMapper.selectById(request.getCarId());
            CarCompany carCompany = carCompanyMapper.selectById(carBaseInfo.getCompanyId());
            CarModel carModel = carModelMapper.selectById(carBaseInfo.getModelId());

            // 创建订单（先占用库存，状态为created）
            CarOrder carOrder = new CarOrder();
                carOrder.setOrderNo(orderNo);
                carOrder.setCarId(carBaseInfo.getCarId());
                carOrder.setCarName(carBaseInfo.getCarName());
                carOrder.setCompanyName(carCompany.getCompanyName());
                carOrder.setModelName(carModel.getModelName());
                carOrder.setAreaId(carBaseInfo.getAreaId());
                carOrder.setPrice(carBaseInfo.getPrice());
                carOrder.setTotalPrice(request.getTotalAmount());
                carOrder.setVolume(carBaseInfo.getVolume());
                carOrder.setOilType(carBaseInfo.getOilType());
                carOrder.setTransmission(carBaseInfo.getTransmission());
                carOrder.setTankCapacity(carBaseInfo.getTankCapacity());
                carOrder.setSeatQuantity(carBaseInfo.getSeatQuantity());
                carOrder.setDoorQuantity(carBaseInfo.getDoorQuantity());
                carOrder.setTakeCarTime(request.getStartDate());
                carOrder.setReturnCarTime(request.getEndDate());
                carOrder.setOrderDay(request.getRentalDays());
                carOrder.setFuelConsumption(carBaseInfo.getFuelConsumption());
                carOrder.setCarAge(carBaseInfo.getCarAge());
                carOrder.setPowerType(carBaseInfo.getPowerType());
                carOrder.setTackCarName(request.getTackCarName());
                carOrder.setReturnCarName(request.getReturnCarName());
                carOrder.setAreaParentName(request.getAreaParentName());
                carOrder.setAreaName(request.getAreaName());
                carOrder.setState("created");
                carOrder.setUserId(getUserId());
                carOrder.setCompanyId(Math.toIntExact(carBaseInfo.getCompanyId()));
                carOrder.setModelId(Math.toIntExact(carBaseInfo.getModelId()));
                carOrder.setOrderTotalPrice(request.getTotalAmount());
                carOrder.setCoverUrl(carBaseInfo.getCoverUrl());
                carOrder.setReturnNewCarTime(request.getEndDate());

                // 插入订单
                int insertResult = carOrderMapper.insert(carOrder);
                if(insertResult == 0) {
                    throw new RuntimeException("订单创建失败");
                }

                // 根据租车天数生成对应数量的CarOrderInventory记录
                List<CarOrderInventory> inventoryList = new ArrayList<>();

                // 第一条记录使用开始时间
                CarOrderInventory firstInventory = new CarOrderInventory();
                firstInventory.setCarId(request.getCarId());
                firstInventory.setOrderId(carOrder.getOrderId());
                firstInventory.setOrderDate(request.getStartDate());
                inventoryList.add(firstInventory);

                // 如果租车天数大于1，则需要添加中间的记录和最后一天的记录
                if (request.getRentalDays() > 1) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(request.getStartDate());

                    // 添加中间的记录（不包括第一天和最后一天）
                    for (int i = 1; i < request.getRentalDays() - 1; i++) {
                        calendar.add(Calendar.DAY_OF_MONTH, 1);

                        CarOrderInventory inventory = new CarOrderInventory();
                        inventory.setCarId(request.getCarId());
                        inventory.setOrderId(carOrder.getOrderId());
                        inventory.setOrderDate(calendar.getTime());
                        inventory.setIsDel(0);
                        inventoryList.add(inventory);
                    }

                    // 只有当租车天数大于1时，才添加最后一条记录使用结束时间
                    CarOrderInventory lastInventory = new CarOrderInventory();
                    lastInventory.setCarId(request.getCarId());
                    lastInventory.setOrderId(carOrder.getOrderId());
                    lastInventory.setOrderDate(request.getEndDate());
                    inventoryList.add(lastInventory);
                }

                if (!inventoryList.isEmpty()) {
                    try {
                        for (CarOrderInventory inventory : inventoryList) {
                            carOrderInventoryMapper.insert(inventory);
                        }

                        // 库存记录插入成功，继续后续流程
                    } catch (Exception e) {
                        log.error("插入库存记录失败: {}", e.getMessage());
                        throw new RuntimeException("车辆在指定日期内已被预订，请选择其他日期");
                    }
                }

                // 订单创建成功，生成支付参数
                // 查询用户的openid
                SysUser sysUser = sysUserMapper.selectUserById(getUserId());
                if(sysUser == null || sysUser.getMicroAppOpenId() == null) {
                    throw new RuntimeException("用户信息无效");
                }

                String attach = "carOrder";
                WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                        orderNo,
                        totalFee,
                        sysUser.getMicroAppOpenId(),
                        "租车费用",
                        attach
                );

                // 生成小程序支付参数
                Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);

                // 保存临时订单到Redis，30分钟后自动取消
                Map<String, Object> tempOrderData = new HashMap<>();
                tempOrderData.put("totalFee", totalFee);
                tempOrderData.put("orderNo", orderNo);
                paymentParams.put("orderId", String.valueOf(carOrder.getOrderId()));
                // 保存并发锁key，用于支付回调时清理
                tempOrderData.put("lockKey", lockKey);
                paymentParams.put("orderNo", orderNo);

                try {
                    // 设置60分钟过期，过期后自动取消订单
                    saveToRedis("CAR_ORDER:" + orderNo, tempOrderData, Duration.ofMinutes(60));
                    log.info("订单{}创建成功，60分钟后自动取消", orderNo);
                } catch (Exception e) {
                    log.error("Redis缓存保存失败: {}", e.getMessage());
                }

                return paymentParams;
            } catch (Exception e) {
                log.error("订单创建失败: {}", e.getMessage());
                // 订单创建失败时删除并发锁
                deleteFromRedis(lockKey);
                throw new RuntimeException("订单创建失败: " + e.getMessage());
            }
//        } catch (Exception e) {
//            log.error("系统异常: {}", e.getMessage(), e);
//            // 系统异常时删除并发锁
//            deleteFromRedis(lockKey);
//            throw new RuntimeException("系统异常: " + e.getMessage());
//        }
    }

    @Override
    @Transactional
    public Map<String, String> updateCarOrder(CarOrderDTO request) {
        LambdaQueryWrapper<CarOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarOrder::getOrderId, request.getOrderId()).eq(CarOrder::getState, "created");
        CarOrder carOrder = carOrderMapper.selectOne(lambdaQueryWrapper);
        if (carOrder == null || carOrder.getOrderNo() == null) {
            log.error("根据订单id获取待支付订单编号失败，订单id为{}", request.getOrderId());
            throw new RuntimeException("订单支付失败");
        }
        // 计算金额（单位：分）
        BigDecimal totalAmount = carOrder.getTotalPrice();
        Integer totalFee;
        try {
            totalFee = totalAmount.multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        String orderNo = String.valueOf(carOrder.getOrderNo());

        // 查询用户的openid
        SysUser sysUser = sysUserMapper.selectUserById(carOrder.getUserId());
        if(sysUser == null || sysUser.getMicroAppOpenId() == null) {
            throw new RuntimeException("用户信息无效");
        }

        String attach = "carOrder";
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "租车费用",
                    attach
            );

            // 生成小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);
            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("totalFee", totalFee);
            tempOrderData.put("orderNo", orderNo);
            paymentParams.put("orderNo", orderNo);
//            saveToRedis(orderNo, tempOrderData, Duration.ofMinutes(30));
//            saveToRedis("CAR_ORDER:" + orderNo, tempOrderData, Duration.ofHours(1));
            return paymentParams;
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常: " + e.getMessage());
        }

    }

    @Override
    public int refundCarOrder(CarOrder request) {

        CarOrder carOrderInfo = new CarOrder();
        LambdaQueryWrapper<CarOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CarOrder::getOrderId, request.getOrderId());
        CarOrder carOrder = carOrderMapper.selectOne(lambdaQueryWrapper);
        if (!"paid".equals(carOrder.getState()) || carOrder.getTakeCarTime() != null && carOrder.getTakeCarTime().before(new Date())) {
            return 0;
        }
        // 将金额从分转换为元
        BigDecimal refundAmount = carOrder.getOrderTotalPrice();
        carOrderInfo.setOrderId(request.getOrderId());
        carOrderInfo.setRefundAmount(refundAmount);
        carOrderInfo.setRefundDate(new Date());

        Snowflake snowflake = IdUtil.getSnowflake(1, 8);
        String outRefundNo = String.valueOf("CO" + snowflake.nextId());
        Integer total = refundAmount.multiply(new BigDecimal("100")).intValue();
        carOrderInfo.setRefundNo(outRefundNo);

        try {
            log.info("退款的订单编号为" + carOrder.getOrderNo());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(carOrder.getOrderNo(), outRefundNo, "用户退款", total, total);
            log.info("订单编号{}退款信息：{}", carOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                carOrderInfo.setRefundUserId(getUserId());
                switch (refundStatus) {
                    case "SUCCESS":
                        carOrderInfo.setRefundState(1);
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        carOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        carOrderInfo.setRefundState(3);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }

                // 车企员工发送短信
                try {
                    CarOrderSmsVO vo = carOrderMapper.getCarOrderSmsInfo(carOrder.getOrderNo());
                    carSmsService.cancelCarOrderSms(vo.getCarPhone(),vo.getUserName(),vo.getCarName(),vo.getOrderNo());

                }catch (Exception e){
                    log.error("订单取消短信发送失败: {}", e.getMessage());
                }
                carOrderInfo.setState("cancel");
                return carOrderMapper.updateById(carOrderInfo);
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            Long orderId = request.getOrderId();
            CarOrder carOrderDetail = carOrderMapper.selectById(orderId);

            if (carOrderDetail != null) {
                Integer orderState = carOrderDetail.getRefundState();
                if (orderState == 1 || orderState == 2) {
                    log.warn("订单[ID:{} No:{}] 已处理，忽略重复退款请求", orderId, carOrderDetail.getOrderNo());
                    throw new RuntimeException(String.format("订单%s已处理，请勿重复操作", carOrderDetail.getOrderNo()));
                }
            }

            log.error("租车订单[ID:{}]退款异常 - 错误信息: {}", orderId, e.getMessage(), e);
            carOrderInfo.setRefundUserId(getUserId());
            carOrderInfo.setRefundState(3);
//            carOrderInfo.setCompleteTime(new Date());
            carOrderMapper.updateById(carOrderInfo);
            return 0;
        }
    }

    @Override
    @Transactional
    public Map<String, String> createContinueCarOrder(CarOrderDTO request) {
        if(request.getOrderId() == null) {
            throw new RuntimeException("ID不能为空");
        }
        if(request.getPrice() == null || request.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("订单金额无效");
        }
        if(request.getRentalDays() == null || request.getRentalDays() <= 0) {
            throw new RuntimeException("租车天数无效");
        }
        if(request.getStartDate() == null || request.getEndDate() == null) {
            throw new RuntimeException("租车日期无效");
        }

        // 验证续租开始时间至少大于当前时间一个小时
        Date currentTime = new Date();
        long oneHourInMillis = 60 * 60 * 1000; // 一小时的毫秒数
        Date minimumStartTime = new Date(currentTime.getTime() + oneHourInMillis);

        if(request.getStartDate().before(minimumStartTime)) {
            throw new RuntimeException("续租开始时间至少需要大于当前时间一个小时");
        }

        // 高并发处理 - 续租订单也需要并发控制
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String concurrencyKey = "CAR_CONTINUE_ORDER_PROCESSING_" + request.getCarId() + "_" +
                               dateFormat.format(request.getStartDate()) + "_" + dateFormat.format(request.getEndDate()) + "_" +
                               System.currentTimeMillis();

        // 检查是否有正在处理的续租订单
        final String lockKey = "CAR_CONTINUE_ORDER_LOCK_" + request.getCarId() + "_" +
                              dateFormat.format(request.getStartDate()) + "_" + dateFormat.format(request.getEndDate());

        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(
            lockKey,
            concurrencyKey,
            Duration.ofSeconds(30)
        );

        if (!Boolean.TRUE.equals(lockAcquired)) {
            throw new RuntimeException("该车辆在此时间段正在被其他用户续租，请稍后重试");
        }

        CarOrder carOrder = carOrderMapper.selectById(request.getOrderId());

        if(!carOrder.getState().equals("driving")) {
            // 续租失败时删除并发锁
            deleteFromRedis(lockKey);
            throw new RuntimeException("当前订单状态已更新，请刷新页面!");
        }

        // 【重要】检查续租期间的库存可用性，防止并发超卖
        CarAvailabilityRequest carAvailabilityRequest = new CarAvailabilityRequest();
        carAvailabilityRequest.setCarId(request.getCarId());
        carAvailabilityRequest.setStartTime(request.getStartDate());
        carAvailabilityRequest.setEndTime(request.getEndDate());
        // 检查续租期间库存可用性
        List<String> unavailableDates = carOrderService.checkCarAvailability(carAvailabilityRequest);
        if(!CollectionUtils.isEmpty(unavailableDates)) {
            // 库存不足时删除并发锁
            deleteFromRedis(lockKey);
            throw new RuntimeException("车辆在指定日期内已被预订，请选择其他日期");
        }

        // 查询当前订单的库存记录，如果term_id不为null，则需要验证term表中的state是否为driving
        List<CarOrderInventory> inventoryList = carOrderInventoryMapper.selectList(new LambdaQueryWrapper<CarOrderInventory>()
                .eq(CarOrderInventory::getOrderId, request.getOrderId())
                .eq(CarOrderInventory::getIsDel, 0)
                .orderByDesc(CarOrderInventory::getOrderDate));

        // 过滤库存记录：如果term_id不为null，则检查对应的term记录状态
        inventoryList.stream().filter(inventory -> {
            if (inventory.getTermId() == null) {
                return true; // 普通订单库存记录
            } else {
                // 续租订单库存记录，需要检查term表状态
                CarOrderTenancyTerm term = carOrderTenancyTermMapper.selectById(inventory.getTermId());
                return term != null && term.getIsDel() == 0 && "driving".equals(term.getState());
            }
        }).forEach(v -> {
            // 如果订单日期大于等于续租开始日期，则不允许续租
            if(isDateAfterOrEqual(v.getOrderDate(), request.getStartDate())){
                // 续租日期冲突时删除并发锁
                deleteFromRedis(lockKey);
                throw new RuntimeException("续租日期不能小于等于当前订单的结束日期");
            }
        });

        // 计算金额（单位：分）
//        BigDecimal totalAmount = request.getTotalAmount().multiply(BigDecimal.valueOf(request.getRentalDays()));
        Integer totalFee;
        try {
            totalFee = request.getTotalAmount().multiply(BigDecimal.valueOf(100)).intValueExact();
        } catch (ArithmeticException e) {
            log.error("金额计算错误: {}", e.getMessage());
            throw new RuntimeException("金额计算错误");
        }

        // 生成唯一订单号
        Snowflake snowflake = IdUtil.getSnowflake(1, 9);
        String orderNo = String.valueOf(snowflake.nextId());

        // 查询用户的openid
        SysUser sysUser = sysUserMapper.selectUserById(getUserId());
        if(sysUser == null || sysUser.getMicroAppOpenId() == null) {
            throw new RuntimeException("用户信息无效");
        }


        String attach = "carContinueOrder";
        try {
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    sysUser.getMicroAppOpenId(),
                    "续租费用",
                    attach
            );

            // 生成小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);


                // 保存临时订单到Redis
                Map<String, Object> tempOrderData = new HashMap<>();
                tempOrderData.put("orderNo", orderNo);
                tempOrderData.put("orderId", request.getOrderId());
                // 确保类型一致性，统一转换为Long
                tempOrderData.put("rentalDays", request.getRentalDays().longValue());
                // 保存时间戳而不是Date对象，避免序列化问题
                tempOrderData.put("endDate", request.getEndDate().getTime());
                tempOrderData.put("carId", request.getCarId());
                // 保存价格和总金额，确保回调时数据一致性
                tempOrderData.put("price", request.getPrice());
                tempOrderData.put("totalAmount", request.getTotalAmount());
                tempOrderData.put("totalFee", totalFee);
                // 保存并发锁key，用于支付回调时清理
                tempOrderData.put("lockKey", lockKey);

                log.info("Redis缓存参数: {}", tempOrderData);
                paymentParams.put("orderNo", orderNo);

                try {
                    saveToRedis(orderNo, tempOrderData, Duration.ofHours(1));
                    log.info("续租订单Redis缓存保存成功: {}", orderNo);
                } catch (Exception e) {
                    log.error("Redis缓存保存失败: {}", e.getMessage());
                    // 缓存失败时删除并发锁
                    deleteFromRedis(lockKey);
                    throw new RuntimeException("订单缓存失败");
                }
                return paymentParams;

        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            // 系统异常时删除并发锁
            deleteFromRedis(lockKey);
            throw new RuntimeException("系统异常: " + e.getMessage());
        }
    }

    /**
     * 租车订单平台退款
     * @param carOrder
     * @return
     */
    @Override
    public CarOrder refundCarOrderByManager(CarOrder carOrder) {
        CarOrder carOrderInfo = new CarOrder();
        Integer refundAmount = carOrder.getRefundAmount().multiply(new BigDecimal("100")).intValue();
        Integer totalPrice = carOrder.getOrderTotalPrice().multiply(new BigDecimal("100")).intValue();
        try {
            log.info("平台退款，退款的订单编号为" + carOrder.getOrderNo());
            // 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(carOrder.getOrderNo(), carOrder.getRefundNo(), carOrder.getPlatformRefundReason(), refundAmount, totalPrice);
            log.info("平台退款，订单编号{}退款信息：{}", carOrder.getOrderNo(), result);
            // 检查空结果
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                switch (refundStatus) {
                    case "SUCCESS":
                        carOrderInfo.setRefundState(1);
                        log.info("平台退款，同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        carOrderInfo.setRefundState(2);
                        log.info("平台退款，同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        carOrderInfo.setRefundState(3);
                        log.info("平台退款，同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                return carOrderInfo;
            } else {
                log.error("平台退款，微信退款接口返回空结果");
                throw new RuntimeException("平台退款，微信退款返回空结果");
            }
        } catch (Exception e) {
            throw new RuntimeException("微信退款接口调用失败", e);
        }
    }

    /**
     * 平台租车订单退款回调
     * @param outRefundNo
     * @param status
     * @param amount
     * @return
     */
    public int handleCarOrderManagerRefund(String outRefundNo, String status, Integer amount) {
        log.info("平台退款，微信返回退款状态：{}", status);
        log.info("平台退款，退单号：{}", outRefundNo);
        CarOrder carOrder = new CarOrder();
        List<CarOrderPayDto> carOrderList = new ArrayList<>();
        //原始单
        if (outRefundNo.startsWith("CMY")) {
            //根据退单号查询原始单信息
            carOrder = carOrderManagerMapper.getCarOrderInfoByRefundNo(outRefundNo);
            //根据退单号查询所有租车订单信息
            carOrderList = carOrderManagerMapper.getCarOrderListByRefundNo(outRefundNo);
        }
        //续租单
        if (outRefundNo.startsWith("CMX")) {
            carOrder = carOrderManagerMapper.getCarOrderTenancyTermInfoByRefundNo(outRefundNo);
            //根据退单号查询所有租车订单信息
            carOrderList = carOrderManagerMapper.getCarOrderTenancyTermListByRefundNo(outRefundNo);
        }
        log.info("平台退款，数据库当前订单详细信息：{}", carOrder);
        // 查询数据库，检查退款单是否已退款
        if (null != carOrder && carOrder.getRefundState() != null) {
            if (carOrder.getRefundState() == 1 && "SUCCESS".equals(status) || carOrder.getRefundState() == 2 && "CLOSED".equals(status)) {
                log.info("平台退款，退款单 {} 已处理，忽略重复回调", outRefundNo);
                // 返回成功状态码
                return 200;
            }
            // 更新退款状态
            // 将金额从分转换为元
            BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            CarOrder carOrderParam = new CarOrder();
            carOrderParam.setOrderId(carOrder.getOrderId());//订单id
            carOrderParam.setUpdateBy(getUserName());//更新人
            carOrderParam.setRefundAmount(refundAmount);//退款金额
            carOrderParam.setState("cancel");//订单状态
            // 判断退款是否成功
            if ("SUCCESS".equals(status)) {
                carOrderParam.setRefundState(1);//订单退款状态
                log.info("平台退款，租车订单退款状态：成功");
            } else {
                carOrderParam.setRefundState(3);//订单退款状态
                log.info("平台退款，租车订单退款状态：失败");
            }
            log.info("平台退款，更新租车订单编号:{},的订单状态为{}", carOrderParam.getOrderNo(), carOrderParam.getRefundState());

            //抽取续租订单
            List<CarOrderPayDto> orderType2List = carOrderList.stream()
                    .filter(item -> item != null && item.getOrderType() == 2)
                    .collect(Collectors.toList());
            //抽取原始订单数据
            List<CarOrderPayDto> orderType1List = carOrderList.stream()
                    .filter(item -> item != null && item.getOrderType() == 1)
                    .collect(Collectors.toList());
            log.info("平台退款，原始单列表:{}", orderType1List);
            log.info("平台退款，续租单列表:{}", orderType2List);
            CarOrder carOrderParameter = new CarOrder();
            //更新总退款金额
            BigDecimal refundTotalPrice = carOrderManagerMapper.getrefundTotalPriceByOrderId(orderType1List.get(0).getOrderId());
            BigDecimal newRefundTotalPrice = refundAmount.add(refundTotalPrice == null ? new BigDecimal(0) : refundTotalPrice);
            carOrderParam.setRefundTotalPrice(newRefundTotalPrice);
            carOrderParameter.setRefundTotalPrice(newRefundTotalPrice);
            log.info("平台退款，总退款金额累加前:{}", refundTotalPrice);
            log.info("平台退款，总退款金额累加后:{}", newRefundTotalPrice);
            //原始单退款
            if (outRefundNo.startsWith("CMY")) {
                //获取所有订单退单状态
//                CarOrderPayDto carOrder1 = shipOrderUtil.handleOriginalOrderRefund(orderType1List.get(0), orderType2List);
                Integer allOrderRefundState = shipOrderUtil.handlerAllOrderRefundState(carOrderList, "SUCCESS".equals(status) ? 1 : 3, 1, carOrder.getOrderNo());
                carOrderParam.setAllOrderRefundState(allOrderRefundState);//全部订单退款状态
                log.info("平台退款，回调，计算全部订单退款状态，结果为" + allOrderRefundState);
                //如果当前订单退款失败，判断全部订单退款状态是否为全部退款
//                if (!"SUCCESS".equals(status)) {
//                    Integer state = shipOrderUtil.handleFileRefundOrder(carOrderList, carOrder.getOrderNo());
//
//                    if (state != null) {
//                        carOrderParam.setAllOrderRefundState(state);//全部订单退款状态
//                    }
//                }
                //如果当前原始订单没有续订单，需要更新完成时间
//                if (StringUtils.isEmpty(orderType2List)) {
//                    carOrderParam.setCompleteTime(new Date());
//                }
                //原始订单退单回调更新状态
                carOrderManagerMapper.updateCarOrderRefundInfo(carOrderParam);
                //原始单退单发送短信
                try {
                    if("SUCCESS".equals(status)){
                        CarOrderSmsVO vo = carOrderMapper.getCarOrderSmsInfo(carOrder.getOrderNo());
                        // 客户短信
                        carSmsService.carOrderRefundSms(vo.getUserPhone(),vo.getOrderNo(),vo.getCarName());
                    }
                }catch (Exception e){
                    log.error("平台退款原始单短信发送失败");
                }
            }
            //续租单退款
            if (outRefundNo.startsWith("CMX")) {
                //获取所有订单退单状态
//                CarOrderPayDto carOrderPayDto = shipOrderUtil.handleContinuationOrderRefund(orderType1List.get(0), carOrderList, carOrder.getOrderNo());
                Integer allOrderRefundState = shipOrderUtil.handlerAllOrderRefundState(carOrderList, "SUCCESS".equals(status) ? 1 : 3, 2, carOrder.getOrderNo());
                //续租单退单回调更新状态
                carOrderManagerMapper.updateCarOrderTenancyTermRefundInfo(carOrderParam);

                carOrderParameter.setAllOrderRefundState(allOrderRefundState);//全部订单退款状态
                carOrderParameter.setOrderId(orderType1List.get(0).getOrderId());
                log.info("平台退款，回调，计算全部订单退款状态，结果为" + allOrderRefundState);
//                //如果当前订单退款失败，判断全部订单退款状态是否为全部退款
//                if (!"SUCCESS".equals(status)) {
//                    Integer state = shipOrderUtil.handleFileRefundOrder(carOrderList, carOrder.getOrderNo());
//                    log.info("平台退款，当前订单退款失败，计算全部订单退款状态，结果为" + state);
//                    if (state != null) {
//                        carOrderParameter.setAllOrderRefundState(state);//全部订单退款状态
//                    }
//                }
                //获取当前订单id下订单不为cancel的最大还车时间
                Date returnCarTime = carOrderManagerMapper.getReturnCarTimeByOrderId(carOrderParameter.getOrderId());
                carOrderParameter.setReturnNewCarTime(returnCarTime);
                //续租单退款回调同时更新原始订单退款状态
                carOrderManagerMapper.updateCarOrderRefundState(carOrderParameter);
            }
            log.info("平台退款，租车订单：退款异步回调结束");
            return 200;
        } else {
            log.error("平台退款，租车订单退款单 {} 不存在", outRefundNo);
            return 200;
        }
    }
    /**
     * 比较两个日期的年月日部分，忽略时分秒
     * @param date1 第一个日期
     * @param date2 第二个日期
     * @return 如果 date1 的年月日在 date2 之后或相等，返回 true；否则返回 false
     */
    private boolean isDateAfterOrEqual(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }

        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        cal2.set(Calendar.HOUR_OF_DAY, 0);
        cal2.set(Calendar.MINUTE, 0);
        cal2.set(Calendar.SECOND, 0);
        cal2.set(Calendar.MILLISECOND, 0);

        return cal1.getTime().after(cal2.getTime()) || cal1.getTime().equals(cal2.getTime());
    }

    /**
     * 安全转换为Long类型
     */
    private Long convertToLong(Object obj) {
        if (obj == null) return null;
        if (obj instanceof Long) return (Long) obj;
        if (obj instanceof Integer) return ((Integer) obj).longValue();
        if (obj instanceof String) {
            try {
                return Long.parseLong((String) obj);
            } catch (NumberFormatException e) {
                log.error("无法转换为Long: {}", obj);
                return null;
            }
        }
        log.error("不支持的Long转换类型: {}", obj.getClass());
        return null;
    }

    /**
     * 安全转换为Date类型
     */
    private Date convertToDate(Object obj) {
        if (obj == null) return null;
        if (obj instanceof Date) return (Date) obj;
        if (obj instanceof Long) return new Date((Long) obj);
        if (obj instanceof String) {
            try {
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse((String) obj);
            } catch (Exception e) {
                log.error("无法转换为Date: {}", obj);
                return null;
            }
        }
        log.error("不支持的Date转换类型: {}", obj.getClass());
        return null;
    }

    /**
     * 安全转换为BigDecimal类型
     */
    private BigDecimal convertToBigDecimal(Object obj) {
        if (obj == null) return null;
        if (obj instanceof BigDecimal) return (BigDecimal) obj;
        if (obj instanceof Double) return BigDecimal.valueOf((Double) obj);
        if (obj instanceof Float) return BigDecimal.valueOf((Float) obj);
        if (obj instanceof Long) return BigDecimal.valueOf((Long) obj);
        if (obj instanceof Integer) return BigDecimal.valueOf((Integer) obj);
        if (obj instanceof String) {
            try {
                return new BigDecimal((String) obj);
            } catch (NumberFormatException e) {
                log.error("无法转换为BigDecimal: {}", obj);
                return null;
            }
        }
        log.error("不支持的BigDecimal转换类型: {}", obj.getClass());
        return null;
    }

    /**
     * 创建挑战赛订单
     * @param price
     * @param orderNo
     * @param microAppOpenId
     * @return
     */
    public Map<String, String> createChallengeOrder(BigDecimal price, String orderNo, String microAppOpenId) {
        try {
            // 调用微信支付接口
            String attach = "challengeOrder";
            int totalFee = price.multiply(new BigDecimal(100)).intValue();
            log.info("小程序 挑战赛报名支付开始，订单编号：" + orderNo + "，订单金额：" + price + "，openId：" + microAppOpenId);
            WxPayUnifiedOrderV3Result.JsapiResult jsapiResult = wxPayUtil.createOrder(
                    orderNo,
                    totalFee,
                    microAppOpenId,
                    "挑战赛报名费",
                    attach
            );

            // 生成小程序支付参数
            Map<String, String> paymentParams = wxPayUtil.startWXPay(jsapiResult);
            log.info("生成小程序支付参数：" + paymentParams);
            paymentParams.put("orderNo", orderNo);
            // 保存临时订单到Redis
            Map<String, Object> tempOrderData = new HashMap<>();
            tempOrderData.put("totalFee", totalFee);
            tempOrderData.put("orderNo", orderNo);
            saveToRedis("CHALLENGE:" + orderNo, tempOrderData, Duration.ofMinutes(30));

            return paymentParams;
        } catch (Exception e) {
            log.error("创建挑战赛支付订单失败: {}", e.getMessage());
            throw new RuntimeException("创建挑战赛支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 积分赛后台退款
     *
     * @param fishingCompetitionOrder
     * @return
     */
    @Override
    @Transactional
    public int refundfishingOrder(FishingCompetitionOrder fishingCompetitionOrder) {
        log.info("--------------------积分赛退款接口start---------------------------");
        // 1. 生成退单ID和金额转换
        Snowflake snowflake = IdUtil.getSnowflake(1, 10);
        long outRefundNo = snowflake.nextId();
        // 订单总金额,以分为单位
        Integer total = fishingCompetitionOrder.getTotalAmount().intValue();
        // 退款金额,以分为单位
        Integer refund = fishingCompetitionOrder.getRefundAmount().intValue();
        // 2. 组装待更新的订单数据（先不操作数据库）
        FishingCompetitionOrder fishingCompetitionOrderInfo = new FishingCompetitionOrder();
        fishingCompetitionOrderInfo.setState("refused");
        fishingCompetitionOrderInfo.setRefundReason(fishingCompetitionOrder.getRefundReason());
        fishingCompetitionOrderInfo.setOrderId(fishingCompetitionOrder.getOrderId());
        fishingCompetitionOrderInfo.setRefundType(fishingCompetitionOrder.getRefundType());
        fishingCompetitionOrderInfo.setRefundNo("FCO" + outRefundNo);
        fishingCompetitionOrderInfo.setRefundUserId(getUserId());
        // 将金额从分转换为元
        BigDecimal refundAmount = new BigDecimal(refund).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        // 退款总额
        fishingCompetitionOrderInfo.setRefundAmount(refundAmount);
        // 临时的退款时间（回调为准）
        fishingCompetitionOrderInfo.setRefundDate(new Date());

        try {
            log.info("当前退款的订单编号:" + fishingCompetitionOrder.getOrderNo());
            // 3. 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(fishingCompetitionOrder.getOrderNo() + "", fishingCompetitionOrderInfo.getRefundNo() + "", fishingCompetitionOrderInfo.getRefundReason(), refund, total);
            log.info("微信返回退款信息：{}", result);
            // 4. 根据微信结果统一设置退款状态
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                log.info("退款同步接口返回退款状态: {}", refundStatus);
                switch (refundStatus) {
                    case "SUCCESS":
                        fishingCompetitionOrderInfo.setRefundState(1);
                        // 发短信
                        // iShipOrderService.sendCancelCaptainSms(fishingCompetitionOrderInfo.getOrderId());
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        fishingCompetitionOrderInfo.setRefundState(3);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        fishingCompetitionOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                // PROCESSING时同步调用后应更新订单状态为"退款中"，最终状态异步回调更新
                // 5. 统一更新数据库（仅此一次）
                int orderResult = fishingCompetitionOrderMapper.updateById(fishingCompetitionOrderInfo);
                if (orderResult == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            // 6. 异常处理：查询订单信息,检查是否已处理过
            FishingCompetitionOrder orderDetail = fishingCompetitionOrderMapper.selectById(fishingCompetitionOrderInfo.getOrderId());
            // 订单状态：1已退款2退款失败3退款中(防止防连点失效的情况)
            if (orderDetail.getRefundState() == 1 || orderDetail.getRefundState() == 2 || orderDetail.getRefundState() == 3) {
                log.warn("订单编号: {} 已处理，忽略重复处理", fishingCompetitionOrder.getOrderNo());
                throw new RuntimeException("订单已处理，忽略重复处理");
            }
            // 其他异常
            log.error("调用微信退款接口异常: {}", e.getMessage());
            // 7. 捕获到异常时，标记为退款失败
            fishingCompetitionOrderInfo.setRefundState(2);
            log.info("退款同步接口:调用微信退款接口异常订单编号:{},订单状态改为：退款失败", fishingCompetitionOrder.getOrderNo());
            // 更新订单状态
            fishingCompetitionOrderMapper.updateById(fishingCompetitionOrderInfo);
        }
        log.info("--------------------积分赛退款接口end---------------------------");
        return 1;
    }

    /**
     * 积分赛订单退款回调
     *
     * @param outRefundNo 退款单号
     * @param status      状态
     * @param amount      钱
     * @return 结果
     */
    public int handleFishingCompetitionOrderRefund(String outRefundNo, String status, Integer amount) {
        log.info("退款类型：积分赛订单退款");
        log.info("微信返回退款状态：{}", status);
        FishingCompetitionOrder fishingCompetitionOrder = fishingCompetitionOrderMapper.selectRefundStatusByRefundNo(outRefundNo);
        log.info("数据库当前积分赛订单详细信息：{}", fishingCompetitionOrder);
        // 查询数据库，检查退款单是否已退款
        if (null != fishingCompetitionOrder && fishingCompetitionOrder.getRefundState() != null) {
            if (fishingCompetitionOrder.getRefundState() == 1 && "SUCCESS".equals(status) || fishingCompetitionOrder.getRefundState() == 2 && "CLOSED".equals(status)) {
                log.warn("退款单 {} 已处理，忽略重复回调", outRefundNo);
                // 返回成功状态码
                return 200;
            }
            // 更新退款状态
            FishingCompetitionOrder orderInfo = new FishingCompetitionOrder();
            orderInfo.setOrderId(fishingCompetitionOrder.getOrderId());
            // 更新为实际的退款到账时间
            orderInfo.setRefundDate(new Date());
            // 将金额从分转换为元
            BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            orderInfo.setRefundAmount(refundAmount);
            // 判断退款是否成功
            if ("SUCCESS".equals(status)) {
                orderInfo.setRefundState(1);
                log.info("积分赛订单退款状态：成功");
            } else {
                orderInfo.setRefundState(2);
                log.warn("积分赛订单退款状态：失败");
            }
            log.info("更新积分赛订单编号:{},的订单状态为{}", orderInfo.getOrderNo(), orderInfo.getRefundState());
            fishingCompetitionOrderMapper.updateById(orderInfo);
            log.info("积分赛订单：退款异步回调结束");
            return 200;
        } else {
            log.warn("积分赛订单退款单 {} 不存在", outRefundNo);
            return 200;
        }
    }

    /**
     * 挑战赛后台退款
     *
     * @return 结果
     */
    @Override
    @Transactional
    public int refundChallengeCompetitionOrder(ChallengeCompetitionOrderTeam challengeCompetitionOrderTeam) {
        log.info("--------------------挑战赛退款接口start---------------------------");
        // 1. 生成退单ID和金额转换
        Snowflake snowflake = IdUtil.getSnowflake(1, 11);
        long outRefundNo = snowflake.nextId();
        // 订单总金额,以分为单位
        Integer total = challengeCompetitionOrderTeam.getTotalPrice().intValue();
        // 退款金额,以分为单位
        Integer refund = challengeCompetitionOrderTeam.getRefundAmount().intValue();
        // 2. 组装待更新的订单数据（先不操作数据库）
        ChallengeCompetitionOrderTeam challengeCompetitionOrderInfo = new ChallengeCompetitionOrderTeam();
        challengeCompetitionOrderInfo.setState("cancel");
        challengeCompetitionOrderInfo.setRefundReason(challengeCompetitionOrderTeam.getRefundReason());
        challengeCompetitionOrderInfo.setTeamId(challengeCompetitionOrderTeam.getTeamId());
        challengeCompetitionOrderInfo.setRefundType(challengeCompetitionOrderTeam.getRefundType());
        challengeCompetitionOrderInfo.setRefundNo("CCO" + outRefundNo);
        challengeCompetitionOrderInfo.setRefundUserId(getUserId());
        // 将金额从分转换为元
        BigDecimal refundAmount = new BigDecimal(refund).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        // 退款总额
        challengeCompetitionOrderInfo.setRefundAmount(refundAmount);
        // 临时的退款时间（回调为准）
        challengeCompetitionOrderInfo.setRefundDate(new Date());

        try {
            log.info("当前退款的订单编号:" + challengeCompetitionOrderTeam.getOrderNo());
            // 3. 调用微信退款接口
            WxPayRefundV3Result result = wxPayUtil.refundOrder(challengeCompetitionOrderTeam.getOrderNo() + "", challengeCompetitionOrderInfo.getRefundNo() + "", challengeCompetitionOrderTeam.getRefundReason(), refund, total);
            log.info("微信返回退款信息：{}", result);
            // 4. 根据微信结果统一设置退款状态
            if (result != null) {
                // 状态
                String refundStatus = result.getStatus();
                log.info("退款同步接口返回退款状态: {}", refundStatus);
                switch (refundStatus) {
                    case "SUCCESS":
                        challengeCompetitionOrderInfo.setRefundState(1);
                        // 发短信
                        // iShipOrderService.sendCancelCaptainSms(fishingCompetitionOrderInfo.getOrderId());
                        log.info("同步接口返回退款成功，微信退款单号: {}", result.getRefundId());
                        break;
                    case "PROCESSING":
                        challengeCompetitionOrderInfo.setRefundState(3);
                        log.info("同步接口返回退款处理中，微信退款单号: {}", result.getRefundId());
                        break;
                    default:
                        challengeCompetitionOrderInfo.setRefundState(2);
                        log.info("同步接口返回退款失败，微信退款单号: {}", result.getRefundId());
                }
                // PROCESSING时同步调用后应更新订单状态为"退款中"，最终状态异步回调更新
                // 5. 统一更新数据库（仅此一次）
                int orderResult = challengeCompetitionOrderTeamMapper.updateById(challengeCompetitionOrderInfo);
                if (orderResult == 0) {
                    log.error("更新订单状态失败");
                    throw new RuntimeException("更新订单状态失败");
                }
            } else {
                log.error("微信退款接口返回空结果");
                throw new RuntimeException("微信退款返回空结果");
            }
        } catch (Exception e) {
            // 6. 异常处理：查询订单信息,检查是否已处理过
            ChallengeCompetitionOrderTeam orderDetail = challengeCompetitionOrderTeamMapper.selectById(challengeCompetitionOrderTeam.getTeamId());
            // 订单状态：1已退款2退款失败3退款中(防止防连点失效的情况)
            if (orderDetail.getRefundState() == 1 || orderDetail.getRefundState() == 2 || orderDetail.getRefundState() == 3) {
                log.warn("订单编号: {} 已处理，忽略重复处理", challengeCompetitionOrderTeam.getOrderNo());
                throw new RuntimeException("订单已处理，忽略重复处理");
            }
            // 其他异常
            log.error("调用微信退款接口异常: {}", e.getMessage());
            // 7. 捕获到异常时，标记为退款失败
            challengeCompetitionOrderInfo.setRefundState(2);
            log.info("退款同步接口:调用微信退款接口异常订单编号:{},订单状态改为：退款失败", challengeCompetitionOrderTeam.getOrderNo());
            // 更新订单状态
            challengeCompetitionOrderTeamMapper.updateById(challengeCompetitionOrderInfo);
        }
        log.info("--------------------挑战赛退款接口end---------------------------");
        return 1;
    }

    /**
     * 挑战赛订单退款回调
     *
     * @param outRefundNo 退款单号
     * @param status      状态
     * @param amount      钱
     * @return 结果
     */
    public int handleChallengeCompetitionOrderRefund(String outRefundNo, String status, Integer amount) {
        log.info("退款类型：挑战赛订单退款");
        log.info("微信返回退款状态：{}", status);
        ChallengeCompetitionOrderTeam challengeCompetitionOrderTeam = challengeCompetitionOrderTeamMapper.selectRefundStatusByRefundNo(outRefundNo);
        log.info("数据库当前挑战赛订单详细信息：{}", challengeCompetitionOrderTeam);
        // 查询数据库，检查退款单是否已退款
        if (null != challengeCompetitionOrderTeam && challengeCompetitionOrderTeam.getRefundState() != null) {
            if (challengeCompetitionOrderTeam.getRefundState() == 1 && "SUCCESS".equals(status) || challengeCompetitionOrderTeam.getRefundState() == 2 && "CLOSED".equals(status)) {
                log.warn("退款单 {} 已处理，忽略重复回调", outRefundNo);
                // 返回成功状态码
                return 200;
            }
            // 更新退款状态
            ChallengeCompetitionOrderTeam orderInfo = new ChallengeCompetitionOrderTeam();
            orderInfo.setTeamId(challengeCompetitionOrderTeam.getTeamId());
            // 更新为实际的退款到账时间
            orderInfo.setRefundDate(new Date());
            // 将金额从分转换为元
            BigDecimal refundAmount = new BigDecimal(amount).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
            orderInfo.setRefundAmount(refundAmount);
            // 判断退款是否成功
            if ("SUCCESS".equals(status)) {
                orderInfo.setRefundState(1);
                log.info("挑战赛订单退款状态：成功");
            } else {
                orderInfo.setRefundState(2);
                log.warn("挑战赛订单退款状态：失败");
            }
            log.info("更新挑战赛订单编号:{},的订单状态为{}", orderInfo.getOrderNo(), orderInfo.getRefundState());
            challengeCompetitionOrderTeamMapper.updateById(orderInfo);
            log.info("挑战赛订单：退款异步回调结束");
            return 200;
        } else {
            log.warn("挑战赛订单退款单 {} 不存在", outRefundNo);
            return 200;
        }
    }
}
