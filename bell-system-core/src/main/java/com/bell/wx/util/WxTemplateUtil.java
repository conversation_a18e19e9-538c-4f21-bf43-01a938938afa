package com.bell.wx.util;

import com.bell.common.constant.AppConstants;
import com.bell.common.core.domain.entity.SysUser;
import com.bell.common.exception.ServiceException;
import com.bell.common.utils.StringUtils;
import com.bell.ship.domain.ShipOrder;
import com.bell.ship.mapper.ShipOrderMapper;
import com.bell.system.domain.SendResult;
import com.bell.system.domain.TemplateMessage;
import com.bell.system.mapper.SysUserMapper;
import com.bell.system.util.WechatTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;

/**
 * 发送微信模板消息工具类
 */
@Slf4j
@Component
public class WxTemplateUtil {

    @Autowired
    private ShipOrderMapper shipOrderMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private WechatTemplateUtil wechatTemplateUtil;

    /**
     * 向船长/客服发送公众号消息
     *
     * @param orderId 订单id
     * @param type 1下单 2取消订单
     * @return 结果
     */
    public SendResult sendTemplateMsg(Long orderId, Integer type){
        try {
            TemplateMessage templateMessage = new TemplateMessage();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ShipOrder order = shipOrderMapper.selectById(orderId);
            if(null == order || null == order.getUserId() || null == order.getCaptionId()){
                throw new ServiceException("订单信息异常");
            }
            // 客户信息
            SysUser user = sysUserMapper.selectUserById(order.getUserId());
            // 船长信息
            SysUser captain = sysUserMapper.selectUserById(order.getCaptionId());
            // 客服信息
            String customerTel = shipOrderMapper.getCustomertelByConfigId(9L);
            if(type == 1){
                if(null == order.getPayTime() || null == order.getOrderNo()){
                    throw new ServiceException("订单信息异常");
                }
                templateMessage.setUserName(user.getRealName());
                templateMessage.setOrderNo(order.getOrderNo().toString());
                templateMessage.setPayTime(sdf.format(order.getPayTime()));
                // 发送船长新订单消息
                if(StringUtils.isEmpty(captain.getGzhOpenId())){
                    log.error("船长未绑定公众号账号，新增订单消息发送失败");
                }else{
                    templateMessage.setTouser(captain.getGzhOpenId());
                    templateMessage.setTemplate_id(AppConstants.NEW_SHIP_ORDER_CAPTION_SMS_TEMPLATE_ID);
                    wechatTemplateUtil.sendTemplateMessage(templateMessage);
                }
                // 发送客服新订单消息
                if(null != customerTel){
                    SysUser kfUser = sysUserMapper.selectUserByPhonenumber(customerTel);
                    if(null != kfUser && null != kfUser.getGzhOpenId()){
                        templateMessage.setTouser(kfUser.getGzhOpenId());
                        templateMessage.setUserPhone(user.getPhonenumber());
                        templateMessage.setCaptainName(captain.getRealName());
                        templateMessage.setCaptainPhone(captain.getPhonenumber());
                        templateMessage.setTemplate_id(AppConstants.NEW_SHIP_ORDER_SERVICE_SMS_TEMPLATE_ID);
                        return wechatTemplateUtil.sendTemplateMessage(templateMessage);
                    }else{
                        log.error("客服账户未关联公众号，新增订单消息发送失败");
                    }
                }else{
                    log.error("客服账户未注册，新增订单消息发送失败");
                }
                return new SendResult(408,"消息发送失败","");
            }else{
                // 发送船长取消订单消息
                if(null == order.getCancelTime() || null == order.getOrderNo()){
                    throw new ServiceException("订单信息异常");
                }
                if (StringUtils.isEmpty(captain.getGzhOpenId())){
                    log.error("船长未绑定公众号账号，取消订单消息发送失败");
                    return new SendResult(408,"船长未绑定公众号账号，取消订单消息发送失败","");
                }
                templateMessage.setTouser(captain.getGzhOpenId());
                templateMessage.setUserName(user.getRealName());
                templateMessage.setOrderNo(order.getOrderNo().toString());
                templateMessage.setCancelTime(sdf.format(order.getCancelTime()));
                templateMessage.setTemplate_id(AppConstants.USER_CANCEL_ORDER_SMS_TEMPLATE_ID);
                return wechatTemplateUtil.sendTemplateMessage(templateMessage);
            }
        }catch (Exception e){
            throw new ServiceException("公众号消息发送失败");
        }
    }
}
