# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8880
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
    # WebSocket相关配置
    websocket:
      # WebSocket文本消息缓冲区大小（2MB）
      max-text-message-buffer-size: 2097152
      # WebSocket二进制消息缓冲区大小（2MB）
      max-binary-message-buffer-size: 2097152

# Spring配置
spring:
  config:
    activate:
      on-profile: prod
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ****************************************************************************************************************************************************
        username: root
        password: bell@!2025
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: bell
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 500MB
      # 设置总上传的文件大小
      max-request-size: 500MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 密码
    password: bell@!2025
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql
sfeport:
  # 微信相关
  appId: wx466373006a84b516
  appSecret: f5ef79f61a3880858c0517210b34a2f1
# 支付相关参数
wx:
  pay:
    appId: wx466373006a84b516 #微信公众号或者小程序等的appid
    mchId: 1662775330 #微信支付商户号
    #微信支付商户密钥
    apiV3Key: a4dae8e30605021f20a24cf42c40e3d1
    # 证书相对路径 （以classpath:开头）
    privateKeyPath: classpath:apiclient_key.pem
    # 证书相对路径 （以classpath:开头）商户私钥
    privateCertPath: classpath:apiclient_cert.pem
    # 支付回调通知地址 https://api.sfeport.com
    returnUrl: https://api.sfeport.com/app/wxpay/notify/pay
    # 退款回调地址
    refundUrl: https://api.sfeport.com/app/wxpay/notify/refund
    # 证书相对路径 （以classpath:开头
    keyPath: classpath:apiclient_cert.p12 # p12证书的位置，可以指定绝对路径，也可以指定类路径（以classpath:开头）
# 公众号相关
wechat:
  mpAppId: wxc0bd67bee4e8fd1b
  mpSecret: ef3cd78facbcdbbaeeaf6e6c0d93cf1f
  getOpenIdUrl: https://api.weixin.qq.com/sns/oauth2/access_token?appid=${wechat.mpAppId}&secret=${wechat.mpSecret}&code=CODE&grant_type=authorization_code
  state: formal
  url: https://wx.sfeport.com
# 阿里云实人认证
ali:
  auth:
    host: http://id2meta.market.alicloudapi.com
    path: /id2meta
    AppCode: 5a42abe3cda3460d86c7ac0b84b3e084
    AppKey: 204836057
    AppSecret: W81yY2Nr78qGYUTk10BNKV8vZfnbzCkg
netty:
  port: 8889  # 硬件连接端口
hkiot:
  appKey: 1923298472484974609
  appSecret: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAICr8UTLiaM57t6iVleJifM/a4JIY9hcPZp5GarRyofScENJUpvVOKVs+sDhCBIos4aMamfDgB0+X4ekzGqtYbRPbBmbvLBDeehXXrydqsoyKeJ9Cf7hEP/Ypg9jqU/Ygyta9n7RLN3P87IsZ0U6Ks95MHUMXw86a/fMaSGqfTOxAgMBAAECgYBo2zX3iSK1+L0XgxV3qqbXqsQS3Kk5JRoyqyNmmCZC/xPrUXJXea/650+FJR0tpyt9pyoDO0dJDY5N/Nd0y6xFSgc+XMDJGGGMa1CeNL+wcytaWWtaH3zjvNvT4HXEijR1I9Q0XAt90W8pLcLKrhMVmBfX6HkOdQqF/iYtZTfDEQJBAP9cxcYIqW+o0xiEJ3KPJSXsBwPcD00vZ3kqd3s9fZf5+sC9WoXhKeulqdKzKb7ptFhBVtIbbh+61kHTIAFRHgUCQQCA/jB0ceIw2SzYD0TeMaU3XSLsr9peI0cd6Bo1sTDqqtU6y4LxZ/v61Dmhqqv/aK6Dvh/8OUCB83O5tD2SRgK9AkAhh2/cYbTFmXgmJuv7CbqJ8SS2qAVGUCwY56+KefdspgD7n6EsJPVZIIZcyPN5QrxQPjwFGF7sQZ7yQ6NXKOx1AkBBgtcRDJWXwXRo9rg/MG98QqzuEBGTJc+EfNBy3F6/j6tjhOAnwmvS492xIYDg4kVfWzvbWShN11q6wbNTioLxAkAvJpOrDoNggNXDCnVQfrBVIL2RYgRrdgYtFhYS827J0LyueCtmz7hP9qlIXr4BK6t5prKmqXYkH0hvhtgyNPBQ
  openDoorUrl: https://open-api.hikiot.com/issue/v1/device/openDoor
  appTokenUrl: https://open-api.hikiot.com/auth/exchangeAppToken
  userTokenUrl: https://open-api.hikiot.com/auth/third/code2Token
  authCodeUrl: https://open-api.hikiot.com/auth/third/applyAuthCode
  userName: 13898440917
  password: qyss24680